\documentclass{resume}
\usepackage{zh_CN-Adobefonts_external} 
\usepackage{linespacing_fix}
\usepackage{cite}
\begin{document}
\pagenumbering{gobble}



%***"%"后面的所有内容是注释而非代码，不会输出到最后的PDF中
%***使用本模板，只需要参照输出的PDF，在本文档的相应位置做简单替换即可
%***修改之后，输出更新后的PDF，只需要点击Overleaf中的“Recompile”按钮即可
%**********************************姓名********************************************
\name{高涵}
%**********************************联系信息****************************************
%第一个括号里写手机号，第二个写邮箱
\contactInfo{15050818613}{<EMAIL>}
%**********************************其他信息****************************************
%在大括号内填写其他信息，最多填写4个，但是如果选择不填信息，
%那么大括号必须空着不写，而不能删除大括号。
%\otherInfo后面的四个大括号里的所有信息都会在一行输出
%如果想要写两行，那就用两次这个指令（\otherInfo{}{}{}{}）即可


%*********************************照片**********************************************
%照片需要放到images文件夹下，名字必须是you.jpg，如果不需要照片可以不添加此行命令
%0.15的意思是，照片的宽度是页面宽度的0.15倍，调整大小，避免遮挡文字
\yourphoto{0.11}
%**********************************正文**********************************************


%***大标题，下面有横线做分割
%***一般的标题有：教育背景，实习（项目）经历，工作经历，自我评价，求职意向，等等
\section{教育背景}


%***********一行子标题**************
%***第一个大括号里的内容向左对齐，第二个大括号里的内容向右对齐
%***\textbf{}括号里的字是粗体，\textit{}括号里的字是斜体
\datedsubsection{\textbf{北京邮电大学}，人工智能，\textit{硕士}}{2023.09 - 2026.06}


%***********列举*********************
%***可添加多个\item，得到多个列举项，类似的也可以用\textbf{}、\textit{}做强调


\section{实习经历}

\datedsubsection{\textbf{苏州睿首智能科技有限公司} - 后端开发工程师}{2024.10 - 2025.02}
\begin{itemize}[parsep=0.5ex]
  \item 主要产出：优化数据库并发控制机制、设计接口安全认证、改进事务管理、解决HTTP数据解析问题
\end{itemize}
\begin{itemize}[parsep=0.5ex]
  \item 整合\textbf{悲观锁}，解决相同ID的多请求并发插入问题，避免重复记录导致的更新失败  
\end{itemize}
\begin{itemize}[parsep=0.5ex]
  \item 针对多线程数据库表创建与数据操作冲突问题，采用 \textbf{Synchronized机制+ConcurrentHashMap} 确保表创建的原子性，并使用线程安全的表名缓存提高查询效率
\end{itemize}
\begin{itemize}[parsep=0.5ex]
  \item 基于 \textbf{RabbitMQ TTL+DLX} 及批量消费策略解决高并发场景下的 消息堆积 问题，并结合幂等性设计（基于 \textbf{Redis 分布式锁 + MySQL 唯一索引}） 防止消息重消费
\end{itemize}
\begin{itemize}[parsep=0.5ex]
  \item 设计API签名认证算法，为用户分配 \textbf{唯一AK/SK} 进行鉴权，确保接口调用安全性和可追溯性
\end{itemize}
\begin{itemize}[parsep=0.5ex]
  \item 利用字节数组处理 \textbf{HTTP分块编码} 问题，自定义 \textbf{StringHttpMessageConverter} 字符集解决@RequestPart中文乱码，提升数据解析与传输效率
\end{itemize}
\begin{itemize}[parsep=0.5ex]
  \item 通过 \textbf{ApplicationListener} 优化Spring启动时事务管理，避免系统启动时数据初始化异常
\end{itemize}


\section{项目经历}

\datedsubsection{\textbf{Ghan RPC - 基于 Vert.x 的 RPC 框架} - 后端开发}{2024.07 - 2024.09}

基于 Java + Etcd + Vert.x 及自定义协议构建高性能 RPC 框架，提供 Spring Boot Starter 以注解和配置方式简化集成，实现本地方法般的远程调用。

\begin{itemize}[parsep=0.5ex]
  \item 基于 \textbf{JDK 动态代理 + 工厂模式}，为指定服务接口动态生成 HTTP 代理对象，实现远程方法无感调用。
\end{itemize}

\begin{itemize}[parsep=0.5ex]
  \item 定义统一接口，实现 \textbf{JSON、Kryo、Hessian} 序列化器，并通过 \textbf{ThreadLocal 解决 Kryo 线程安全} 问题，同时采用 \textbf{工厂模式 + 单例模式} 统一管理序列化器。
\end{itemize}
\begin{itemize}[parsep=0.5ex]
  \item 基于 \textbf{资源路径扫描 + 反射 自实现} SPI 机制，支持用户自定义序列化器、负载均衡、重试及容错策略，提升框架扩展性。
\end{itemize}
\begin{itemize}[parsep=0.5ex]
  \item 基于 \textbf{Vert.x RecordParser} 解决半包粘包问题，并通过 \textbf{装饰者模式} 封装 \textbf{TcpBufferHandlerWrapper}，实现请求处理器增强，提升代码可维护性。
\end{itemize}

\datedsubsection{\textbf{贝壳找房搜索发现推荐系统优化}}{2024.03 - 2024.05}

\textbf{项目背景}：进行搜索发现功能优化，基于用户行为建模实现9项个性化Query推荐，重点优化精排模型与结果多样性。

\textbf{项目技术}：
\begin{itemize}[parsep=0.5ex]

  \item 样本优化：创新设计多模态联合标注策略，通过行为模式分析挖掘潜在正样本，有效缓解样本偏差问题，实现离线GAUC提升1.3PT，线上关键指标显著提升（pCTR↑2.5\%，人均GMV↑1.5\%）
  \item 方鸿渐在月夜下情境所迫，吻了苏小姐，第二日不得不告诉苏小姐自己所爱并非她
\end{itemize}



\section{专业技能和其他}
\datedsubsection{\textbf{技术栈：}}{}

\begin{itemize}[parsep=0.5ex]
  \item 熟悉 Java编程语言，掌握集合框架、I/O流、异常、反射等机制
  \item 熟悉JVM底层原理，如类加载、垃圾回收算法等，熟悉JVM垃圾回收器的使用以及核心参数
  \item 熟练使用 SpringBoot 、Mybatis 等框架，能够运用并独立开发项目，熟悉AOP、IOC等原理
  \item 熟悉操作系统基本原理，熟悉进程、线程、虚拟内存等概念，了解Select、Poll等模型的原理
  \item 熟悉 MySQL 使用和原理，熟悉索引、事务等机制，能够对常见 SQL 语句调优， 了解读写分离、分库分表
  \item 熟悉 Redis 使用和原理，线程模型、持久化机等机制，熟悉缓存雪崩、穿透、击穿解决方案
  \item 熟悉RockeMQ、Kafla 消息中间件的使用，了解过重消费、消息积压等问题解决
\end{itemize}

\datedsubsection{\textbf{荣誉奖项：} 国家励志奖学金、河北省普通高等学校优秀毕业生、蓝桥杯第十二届软件类河北省B类赛省三等奖}{}

\end{document}
