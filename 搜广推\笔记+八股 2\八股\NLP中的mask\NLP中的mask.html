<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/><meta name="exporter-version" content="Evernote Mac 9.7.14 (474683)"/><meta name="created" content="2021-11-14 07:46:48 +0000"/><meta name="updated" content="2021-11-14 07:46:48 +0000"/><meta name="content-class" content="yinxiang.markdown"/><title>NLP中的mask</title></head><body><div style="font-size: 14px; margin: 0; padding: 0; width: 100%;"><p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">NLP中的mask</strong></p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;">[Mask]的存在可以迫使模型通过上下文信息去学习一词多义。<br clear="none"/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">RNN中的Mask</strong><br clear="none"/>
对于RNN等模型，本身是可以直接处理不定长数据的，因此它不需要提前告知 sequence length，但是在实践中，为了 batch 训练，一般会把不定长的序列 padding 到相同长度，再用 mask 去区分非 padding 部分和 padding 部分。区分的目的是使得RNN只作用到它实际长度的句子，而不会处理无用的 padding 部分，这样RNN的输出和隐状态都会是对应句子实际的最后一位。另外，对于token级别的任务，也可以通过mask去忽略 padding 部分对应的loss。<br clear="none"/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">transformer中的mask</strong><br clear="none"/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">padding mask</strong><br clear="none"/>
我们在训练的过程中，数据往往都是以Batch的形式输入进的模型，而一个batch中的每一句话不能保证长度都是一样的，所以需要使用PADDING的方式将所有的句子都补全到最长的长度，比如拿0进行填充，但是这种用0填充的位置的信息是完全没有意义的，因此我们希望这个位置不参与后期的反向传播过程。在Self-attention的计算当中，我们自然也不希望有效词的注意力集中在这些没有意义的位置上，因此使用了PADDING MASK的方式。PADDING MASK在attention的计算过程中处于softmax之前，通过PADDING MASK的操作，使得补全位置上的值成为一个非常大的负数（可以是负无穷），这样的话，经过Softmax层的时候，这些位置上的概率就是0。以此操作就相当于把补全位置的无用信息给遮蔽掉了。<br clear="none"/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">sequence mask</strong><br clear="none"/>
sequence mask 是为了使得 decoder 不能看见未来的信息。也就是对于一个序列，在 time_step 为 t 的时刻，我们的解码输出应该只能依赖于 t 时刻之前的输出，而不能依赖 t 之后的输出。产生一个上三角矩阵，上三角的值全为0。把这个矩阵作用在每一个序列上。<br clear="none"/>
对于 decoder 的 self-attention，里面使用到的 scaled dot-product attention，同时需要padding mask 和 sequence mask 作为 attn_mask，具体实现就是两个mask相加作为attn_mask。<br clear="none"/>
其他情况，attn_mask 一律等于 padding mask。</p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">Bert中的mask</strong><br clear="none"/>
为了在语言模型的训练中，<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">使用上下文信息又不泄露标签信息</strong>，采用了Masked LM，简单来说就是随机的选择序列的部分token用 [Mask] 标记代替。</p>
</div><center style="display:none !important;visibility:collapse !important;height:0 !important;white-space:nowrap;width:100%;overflow:hidden">**NLP%E4%B8%AD%E7%9A%84mask**%0A%0A%5BMask%5D%E7%9A%84%E5%AD%98%E5%9C%A8%E5%8F%AF%E4%BB%A5%E8%BF%AB%E4%BD%BF%E6%A8%A1%E5%9E%8B%E9%80%9A%E8%BF%87%E4%B8%8A%E4%B8%8B%E6%96%87%E4%BF%A1%E6%81%AF%E5%8E%BB%E5%AD%A6%E4%B9%A0%E4%B8%80%E8%AF%8D%E5%A4%9A%E4%B9%89%E3%80%82%0A**RNN%E4%B8%AD%E7%9A%84Mask**%0A%E5%AF%B9%E4%BA%8ERNN%E7%AD%89%E6%A8%A1%E5%9E%8B%EF%BC%8C%E6%9C%AC%E8%BA%AB%E6%98%AF%E5%8F%AF%E4%BB%A5%E7%9B%B4%E6%8E%A5%E5%A4%84%E7%90%86%E4%B8%8D%E5%AE%9A%E9%95%BF%E6%95%B0%E6%8D%AE%E7%9A%84%EF%BC%8C%E5%9B%A0%E6%AD%A4%E5%AE%83%E4%B8%8D%E9%9C%80%E8%A6%81%E6%8F%90%E5%89%8D%E5%91%8A%E7%9F%A5%20sequence%20length%EF%BC%8C%E4%BD%86%E6%98%AF%E5%9C%A8%E5%AE%9E%E8%B7%B5%E4%B8%AD%EF%BC%8C%E4%B8%BA%E4%BA%86%20batch%20%E8%AE%AD%E7%BB%83%EF%BC%8C%E4%B8%80%E8%88%AC%E4%BC%9A%E6%8A%8A%E4%B8%8D%E5%AE%9A%E9%95%BF%E7%9A%84%E5%BA%8F%E5%88%97%20padding%20%E5%88%B0%E7%9B%B8%E5%90%8C%E9%95%BF%E5%BA%A6%EF%BC%8C%E5%86%8D%E7%94%A8%20mask%20%E5%8E%BB%E5%8C%BA%E5%88%86%E9%9D%9E%20padding%20%E9%83%A8%E5%88%86%E5%92%8C%20padding%20%E9%83%A8%E5%88%86%E3%80%82%E5%8C%BA%E5%88%86%E7%9A%84%E7%9B%AE%E7%9A%84%E6%98%AF%E4%BD%BF%E5%BE%97RNN%E5%8F%AA%E4%BD%9C%E7%94%A8%E5%88%B0%E5%AE%83%E5%AE%9E%E9%99%85%E9%95%BF%E5%BA%A6%E7%9A%84%E5%8F%A5%E5%AD%90%EF%BC%8C%E8%80%8C%E4%B8%8D%E4%BC%9A%E5%A4%84%E7%90%86%E6%97%A0%E7%94%A8%E7%9A%84%20padding%20%E9%83%A8%E5%88%86%EF%BC%8C%E8%BF%99%E6%A0%B7RNN%E7%9A%84%E8%BE%93%E5%87%BA%E5%92%8C%E9%9A%90%E7%8A%B6%E6%80%81%E9%83%BD%E4%BC%9A%E6%98%AF%E5%AF%B9%E5%BA%94%E5%8F%A5%E5%AD%90%E5%AE%9E%E9%99%85%E7%9A%84%E6%9C%80%E5%90%8E%E4%B8%80%E4%BD%8D%E3%80%82%E5%8F%A6%E5%A4%96%EF%BC%8C%E5%AF%B9%E4%BA%8Etoken%E7%BA%A7%E5%88%AB%E7%9A%84%E4%BB%BB%E5%8A%A1%EF%BC%8C%E4%B9%9F%E5%8F%AF%E4%BB%A5%E9%80%9A%E8%BF%87mask%E5%8E%BB%E5%BF%BD%E7%95%A5%20padding%20%E9%83%A8%E5%88%86%E5%AF%B9%E5%BA%94%E7%9A%84loss%E3%80%82%0A**transformer%E4%B8%AD%E7%9A%84mask**%0A**padding%20mask**%0A%E6%88%91%E4%BB%AC%E5%9C%A8%E8%AE%AD%E7%BB%83%E7%9A%84%E8%BF%87%E7%A8%8B%E4%B8%AD%EF%BC%8C%E6%95%B0%E6%8D%AE%E5%BE%80%E5%BE%80%E9%83%BD%E6%98%AF%E4%BB%A5Batch%E7%9A%84%E5%BD%A2%E5%BC%8F%E8%BE%93%E5%85%A5%E8%BF%9B%E7%9A%84%E6%A8%A1%E5%9E%8B%EF%BC%8C%E8%80%8C%E4%B8%80%E4%B8%AAbatch%E4%B8%AD%E7%9A%84%E6%AF%8F%E4%B8%80%E5%8F%A5%E8%AF%9D%E4%B8%8D%E8%83%BD%E4%BF%9D%E8%AF%81%E9%95%BF%E5%BA%A6%E9%83%BD%E6%98%AF%E4%B8%80%E6%A0%B7%E7%9A%84%EF%BC%8C%E6%89%80%E4%BB%A5%E9%9C%80%E8%A6%81%E4%BD%BF%E7%94%A8PADDING%E7%9A%84%E6%96%B9%E5%BC%8F%E5%B0%86%E6%89%80%E6%9C%89%E7%9A%84%E5%8F%A5%E5%AD%90%E9%83%BD%E8%A1%A5%E5%85%A8%E5%88%B0%E6%9C%80%E9%95%BF%E7%9A%84%E9%95%BF%E5%BA%A6%EF%BC%8C%E6%AF%94%E5%A6%82%E6%8B%BF0%E8%BF%9B%E8%A1%8C%E5%A1%AB%E5%85%85%EF%BC%8C%E4%BD%86%E6%98%AF%E8%BF%99%E7%A7%8D%E7%94%A80%E5%A1%AB%E5%85%85%E7%9A%84%E4%BD%8D%E7%BD%AE%E7%9A%84%E4%BF%A1%E6%81%AF%E6%98%AF%E5%AE%8C%E5%85%A8%E6%B2%A1%E6%9C%89%E6%84%8F%E4%B9%89%E7%9A%84%EF%BC%8C%E5%9B%A0%E6%AD%A4%E6%88%91%E4%BB%AC%E5%B8%8C%E6%9C%9B%E8%BF%99%E4%B8%AA%E4%BD%8D%E7%BD%AE%E4%B8%8D%E5%8F%82%E4%B8%8E%E5%90%8E%E6%9C%9F%E7%9A%84%E5%8F%8D%E5%90%91%E4%BC%A0%E6%92%AD%E8%BF%87%E7%A8%8B%E3%80%82%E5%9C%A8Self-attention%E7%9A%84%E8%AE%A1%E7%AE%97%E5%BD%93%E4%B8%AD%EF%BC%8C%E6%88%91%E4%BB%AC%E8%87%AA%E7%84%B6%E4%B9%9F%E4%B8%8D%E5%B8%8C%E6%9C%9B%E6%9C%89%E6%95%88%E8%AF%8D%E7%9A%84%E6%B3%A8%E6%84%8F%E5%8A%9B%E9%9B%86%E4%B8%AD%E5%9C%A8%E8%BF%99%E4%BA%9B%E6%B2%A1%E6%9C%89%E6%84%8F%E4%B9%89%E7%9A%84%E4%BD%8D%E7%BD%AE%E4%B8%8A%EF%BC%8C%E5%9B%A0%E6%AD%A4%E4%BD%BF%E7%94%A8%E4%BA%86PADDING%20MASK%E7%9A%84%E6%96%B9%E5%BC%8F%E3%80%82PADDING%20MASK%E5%9C%A8attention%E7%9A%84%E8%AE%A1%E7%AE%97%E8%BF%87%E7%A8%8B%E4%B8%AD%E5%A4%84%E4%BA%8Esoftmax%E4%B9%8B%E5%89%8D%EF%BC%8C%E9%80%9A%E8%BF%87PADDING%20MASK%E7%9A%84%E6%93%8D%E4%BD%9C%EF%BC%8C%E4%BD%BF%E5%BE%97%E8%A1%A5%E5%85%A8%E4%BD%8D%E7%BD%AE%E4%B8%8A%E7%9A%84%E5%80%BC%E6%88%90%E4%B8%BA%E4%B8%80%E4%B8%AA%E9%9D%9E%E5%B8%B8%E5%A4%A7%E7%9A%84%E8%B4%9F%E6%95%B0%EF%BC%88%E5%8F%AF%E4%BB%A5%E6%98%AF%E8%B4%9F%E6%97%A0%E7%A9%B7%EF%BC%89%EF%BC%8C%E8%BF%99%E6%A0%B7%E7%9A%84%E8%AF%9D%EF%BC%8C%E7%BB%8F%E8%BF%87Softmax%E5%B1%82%E7%9A%84%E6%97%B6%E5%80%99%EF%BC%8C%E8%BF%99%E4%BA%9B%E4%BD%8D%E7%BD%AE%E4%B8%8A%E7%9A%84%E6%A6%82%E7%8E%87%E5%B0%B1%E6%98%AF0%E3%80%82%E4%BB%A5%E6%AD%A4%E6%93%8D%E4%BD%9C%E5%B0%B1%E7%9B%B8%E5%BD%93%E4%BA%8E%E6%8A%8A%E8%A1%A5%E5%85%A8%E4%BD%8D%E7%BD%AE%E7%9A%84%E6%97%A0%E7%94%A8%E4%BF%A1%E6%81%AF%E7%BB%99%E9%81%AE%E8%94%BD%E6%8E%89%E4%BA%86%E3%80%82%0A**sequence%20mask**%0Asequence%20mask%20%E6%98%AF%E4%B8%BA%E4%BA%86%E4%BD%BF%E5%BE%97%20decoder%20%E4%B8%8D%E8%83%BD%E7%9C%8B%E8%A7%81%E6%9C%AA%E6%9D%A5%E7%9A%84%E4%BF%A1%E6%81%AF%E3%80%82%E4%B9%9F%E5%B0%B1%E6%98%AF%E5%AF%B9%E4%BA%8E%E4%B8%80%E4%B8%AA%E5%BA%8F%E5%88%97%EF%BC%8C%E5%9C%A8%20time_step%20%E4%B8%BA%20t%20%E7%9A%84%E6%97%B6%E5%88%BB%EF%BC%8C%E6%88%91%E4%BB%AC%E7%9A%84%E8%A7%A3%E7%A0%81%E8%BE%93%E5%87%BA%E5%BA%94%E8%AF%A5%E5%8F%AA%E8%83%BD%E4%BE%9D%E8%B5%96%E4%BA%8E%20t%20%E6%97%B6%E5%88%BB%E4%B9%8B%E5%89%8D%E7%9A%84%E8%BE%93%E5%87%BA%EF%BC%8C%E8%80%8C%E4%B8%8D%E8%83%BD%E4%BE%9D%E8%B5%96%20t%20%E4%B9%8B%E5%90%8E%E7%9A%84%E8%BE%93%E5%87%BA%E3%80%82%E4%BA%A7%E7%94%9F%E4%B8%80%E4%B8%AA%E4%B8%8A%E4%B8%89%E8%A7%92%E7%9F%A9%E9%98%B5%EF%BC%8C%E4%B8%8A%E4%B8%89%E8%A7%92%E7%9A%84%E5%80%BC%E5%85%A8%E4%B8%BA0%E3%80%82%E6%8A%8A%E8%BF%99%E4%B8%AA%E7%9F%A9%E9%98%B5%E4%BD%9C%E7%94%A8%E5%9C%A8%E6%AF%8F%E4%B8%80%E4%B8%AA%E5%BA%8F%E5%88%97%E4%B8%8A%E3%80%82%0A%E5%AF%B9%E4%BA%8E%20decoder%20%E7%9A%84%20self-attention%EF%BC%8C%E9%87%8C%E9%9D%A2%E4%BD%BF%E7%94%A8%E5%88%B0%E7%9A%84%20scaled%20dot-product%20attention%EF%BC%8C%E5%90%8C%E6%97%B6%E9%9C%80%E8%A6%81padding%20mask%20%E5%92%8C%20sequence%20mask%20%E4%BD%9C%E4%B8%BA%20attn_mask%EF%BC%8C%E5%85%B7%E4%BD%93%E5%AE%9E%E7%8E%B0%E5%B0%B1%E6%98%AF%E4%B8%A4%E4%B8%AAmask%E7%9B%B8%E5%8A%A0%E4%BD%9C%E4%B8%BAattn_mask%E3%80%82%0A%E5%85%B6%E4%BB%96%E6%83%85%E5%86%B5%EF%BC%8Cattn_mask%20%E4%B8%80%E5%BE%8B%E7%AD%89%E4%BA%8E%20padding%20mask%E3%80%82%0A%0A**Bert%E4%B8%AD%E7%9A%84mask**%0A%E4%B8%BA%E4%BA%86%E5%9C%A8%E8%AF%AD%E8%A8%80%E6%A8%A1%E5%9E%8B%E7%9A%84%E8%AE%AD%E7%BB%83%E4%B8%AD%EF%BC%8C**%E4%BD%BF%E7%94%A8%E4%B8%8A%E4%B8%8B%E6%96%87%E4%BF%A1%E6%81%AF%E5%8F%88%E4%B8%8D%E6%B3%84%E9%9C%B2%E6%A0%87%E7%AD%BE%E4%BF%A1%E6%81%AF**%EF%BC%8C%E9%87%87%E7%94%A8%E4%BA%86Masked%20LM%EF%BC%8C%E7%AE%80%E5%8D%95%E6%9D%A5%E8%AF%B4%E5%B0%B1%E6%98%AF%E9%9A%8F%E6%9C%BA%E7%9A%84%E9%80%89%E6%8B%A9%E5%BA%8F%E5%88%97%E7%9A%84%E9%83%A8%E5%88%86token%E7%94%A8%20%5BMask%5D%20%E6%A0%87%E8%AE%B0%E4%BB%A3%E6%9B%BF%E3%80%82</center></body></html>