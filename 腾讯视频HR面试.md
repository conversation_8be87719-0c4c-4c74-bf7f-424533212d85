---

---
<mark style="background: #FFB8EBA6;">让世界看到你的影响力</mark>

<mark style="background: #BBFABBA6;">为什么选择腾讯</mark>
智慧连接，美好生活
腾讯长期致力于社交平台与数字内容两大核心业务，积极拥抱产业互联网，
努力成为各行各业数字化助手，通过连接，提升每一个人的生活品质。

用行动关心你的成长
这里有着富有挑战和影响力的工作内容，完备的职业培训和晋升体系，
以及灵活的内部转岗机制，引领你发现更多可能。

迎接每个特别的你
自由包容的文化氛围让你自由发展，你的声音会被认真聆听，在这里，
你遇到的不只是同事，更是并肩成长的伙伴。

福利关怀浸透你的生活
你的努力会被铭记，你的付出会被真诚对待，舒适与科技融合的办公环境，
极具竞争力的薪酬和激励体系，完善和个性化员工福利，只为回报你的尽心拼搏。

PCG平台与内容事业群——腾讯业务形态最丰富的事业群，致力于推动互联网平台和数字内容生态的融合发展，整合社交、工具以及内容业务。这里有你熟悉的QQ、QQ空间等社交平台，腾讯文档、应用宝等工具平台，新闻、视频、体育等内容业务。 “让内容创造美好”是我们的使命。我们致力于促进IP跨平台、多形态发展，为创作者提供更好的创作土壤，为用户创造高质量的数字内容与消费体验。同时，技术是PCG的第一生产力，我们通过对技术工业化的持续改造，建设包含代码、算法、数据、内容的技术中台管线，促进平台与内容的融合发展。此外，我们还在不断积极探索，发挥社交、工具、内容及技术的优势，探索更多可能。

<mark style="background: #BBFABBA6;">具有成就感的事情？</mark>
在开发智能多模态内容管理平台时，参与解决了康养系统中多模态数据融合与高并发协作的挑战。初期因数据编码标准不统一，跨模态检索准确率不足40%，且接口响应缓慢（2秒），多人编辑冲突频发。通过重构系统架构：1）采用Spring AI与CLIP模型统一编码文本、代码、图片，结合人工元数据将检索准确率提升至60%；2）引入Reactor响应式编程与Redis+Caffeine多级缓存，将接口响应缩短至几ms；3）设计WebSocket权限校验与“编辑锁”机制，彻底解决并发冲突。最终系统支撑日均5W+请求，知识管理效率提升，并助力康养健康领域的建设，践行“科技向善”的技术价值。

<mark style="background: #BBFABBA6;">为什么选择全栈开发岗位（脚本运镜处理+UE 渲染+3 D 资产管理）？</mark>

全栈开发最吸引我的是其“端到端”的技术掌控能力。在腾讯的业务场景中，需要前端与后端深度协同，而全栈开发能够让我同时参与从用户交互逻辑设计（脚本运镜处理）、高性能渲染实现（UE 引擎优化），到 3 D 资产的自动化管理全流程。这种技术闭环的实践机会，正是我渴望深入探索的方向。腾讯视频Vision Pro版开发与互动引擎技术融合探索，这一过程使得客户端开发从传统的2D视图转向处理3D场景，接近于游戏客户端开发。同时在腾讯视频和很多产品中也引入了 AI，也对在大模型应用在产品上有一些技术架构的沉淀，比如根据大家的意图进行个性化推荐

<mark style="background: #BBFABBA6;">后端开发的工作内容是什么样的？</mark>
后端开发的核心是构建高可用、高性能的系统架构，具体包括：

**服务设计**：基于RESTful或gRPC设计API，支持前端与引擎的高效交互（如UE的HTTP请求处理）；

**业务逻辑实现**：例如3D资产的元数据管理、版本控制、权限校验等，需结合腾讯的TSF（Tencent Service Framework）微服务框架；

**数据层优化**：使用MySQL或TDSQL（腾讯分布式数据库）管理海量3D模型数据，确保查询效率；

**系统扩展性**：通过Kubernetes（腾讯云TKE）实现服务弹性扩缩容，应对突发流量（如游戏大版本更新）；

**安全与监控**：集成腾讯云的SCF（无服务器函数）与监控平台（如蓝鲸），保障系统稳定性。

<mark style="background: #BBFABBA6;">三个关键词描述自己</mark>

**技术敏锐度**：在GitHub上持续跟踪UE的开源项目（如MetaHuman），并尝试将其技术点融入自己的项目。执行力强

**工程化思维**：在开发相关产品，采用了“模块化设计”思想，将拆分为独立微服务，提升系统可维护性；

**持续学习力**，多任务处理能力不足，按优先级做，有点内向

<mark style="background: #BBFABBA6;">学校里做项目和在公司有什么不一样？</mark>

学校项目更注重**理论验证与技术可行性**；而公司项目更强调**工程化落地与用户价值**，例如：

**需求复杂度**：公司项目需对接真实业务场景（如腾讯游戏的3D资产流水线），需考虑版本兼容性、多端适配等；

**协作模式**：学校项目多为独立开发或小团队，而公司需遵循敏捷开发流程（如腾讯的“双周迭代”），使用内部工具链（如蓝鲸PaaS）；

**用户导向**：学校项目以功能实现为目标，而公司需持续收集用户反馈
这种差异让我更理解腾讯“用户为本”的价值观，也锻炼了我的工程化思维。

<mark style="background: #BBFABBA6;">**有没有遇到过比较难沟通的情况？</mark>

在开发一个在线云库系统时，后端团队认为应优先保证API稳定性，而前端团队希望快速上线新功能。我通过以下方式化解：

- **数据驱动**：用压测数据证明当前架构的瓶颈在数据库而非接口设计；
- **方案平衡**：提出分阶段交付：先优化核心API，再逐步扩展功能；

<mark style="background: #BBFABBA6;">如果遇到意见不一的情况怎么处理？</mark>
 我会遵循“用户价值优先”原则，结合腾讯的协作文化分三步解决：

**需求对齐**：明确业务目标（如提升渲染效率还是缩短开发周期）；

**技术验证**：通过原型或数据对比方案优劣（例如用A/B测试验证不同渲染策略）；

**共识推进**：若仍存分歧，可引入第三方（如架构师或产品经理）评估风险。

<mark style="background: #BBFABBA6;">职业规划</mark>

**短期（1-2年）**：进行技术理论和业务经验的积累，成为能独立负责核心模块的全栈工程师 。**中期（3-5年）**：成长为技术负责人，带领团队解决复杂问题，并推动技术方案在腾讯多个业务线落地；**长期**：成为技术专家，探索3D内容与AI的结合，推动下一代沉浸式内容体验的技术革新

<mark style="background: #BBFABBA6;">高强度工作下的抗压与时间管理方法</mark>

**优先级管理：**
使用“四象限法则”区分任务紧急/重要性（如UE渲染优化优先于文档整理）；
参考腾讯的“敏捷看板”工具，每日同步任务进度。
**技术效率提升：**
预研阶段通过原型快速验证方案（如用UE蓝图模拟核心逻辑）；
复用腾讯内部技术资产（如TSF的微服务模板）。
**心理调节：**
设置“技术小目标”（如每日攻克一个UE插件开发难题），增强成就感；

<mark style="background: #BBFABBA6;">选择公司的标准与优先级</mark>

**技术挑战性**：能否参与前沿技术（如UE5+AI的3D内容生成）；

**团队氛围**：**职业发展**：晋升路径与培训资源。
腾讯在技术积累（如腾讯游戏引擎）、业务规模（全球Top级用户量）和价值观（“科技向善”）上的优势，使其成为我的首选

反问：

**岗位技术方向** 缺什么

多久可以出结果，可以方便更快和老师协商

**关于职业发展**：  “在腾讯的全栈开发路径中，从工程师到技术负责人的关键能力提升点是什么？团队是否有对应的培养计划？”

