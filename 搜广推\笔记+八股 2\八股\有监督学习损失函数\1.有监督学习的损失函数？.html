<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/><meta name="exporter-version" content="Evernote Mac 9.7.14 (474683)"/><meta name="created" content="2021-11-14 07:45:46 +0000"/><meta name="updated" content="2021-11-14 07:45:46 +0000"/><meta name="content-class" content="yinxiang.markdown"/><title>1.有监督学习的损失函数？</title></head><body><div style="font-size: 14px; margin: 0; padding: 0; width: 100%;"><p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">1.有监督学习的损失函数？</strong><br clear="none"/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">损失函数，风险函数</strong><br clear="none"/>
损失函数度量模型一次预测的好坏，风险函数度量<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">平均意义</strong>下模型预测的好坏</p>
<ul style="line-height: 160%; box-sizing: content-box; display: block; list-style-type: disc; padding-left: 30px; margin: 6px 0 10px; color: #333;"><li style="line-height: 160%; box-sizing: content-box; position: relative;">分类问题
<ul style="line-height: 160%; box-sizing: content-box; display: block; list-style-type: disc; padding-left: 30px; margin: 6px 0 10px; color: #333; margin-top: 0; margin-bottom: 0;"><li style="line-height: 160%; box-sizing: content-box; position: relative;">0-1损失函数：<br clear="none"/>
<img src="https://app.yinxiang.com/FileSharing.action?hash=1/3bafa4876140b66e707d2556f2619049-5769" width="282" height="79"/></li><li style="line-height: 160%; box-sizing: content-box; position: relative;">Hinge损失函数：0-1损失函数相对紧的凸上界<br clear="none"/>
<img src="https://app.yinxiang.com/FileSharing.action?hash=1/33dc2b1484d6be57da661d2b25c9e7e5-4533" width="300" height="47"/><br clear="none"/>
使用次梯度下降法求解</li><li style="line-height: 160%; box-sizing: content-box; position: relative;">逻辑回归损失函数：0-1损失函数的凸上界<br clear="none"/>
<img src="https://app.yinxiang.com/FileSharing.action?hash=1/9ce3c5061ee55d48ebe28444b2e06aff-3611" width="291" height="50"/></li><li style="line-height: 160%; box-sizing: content-box; position: relative;">交叉熵损失函数：0-1损失函数的光滑凸上界<br clear="none"/>
<img src="https://app.yinxiang.com/FileSharing.action?hash=1/bafd9c2f9b490f5470f316e91db41876-3698" width="288" height="57"/><br clear="none"/>
<img src="https://app.yinxiang.com/FileSharing.action?hash=1/c882e7e78509fe1b2da597ab68dc573b-3420451" width="4000" height="3000"/></li></ul>
</li><li style="line-height: 160%; box-sizing: content-box; position: relative;">回归问题
<ul style="line-height: 160%; box-sizing: content-box; display: block; list-style-type: disc; padding-left: 30px; margin: 6px 0 10px; color: #333; margin-top: 0; margin-bottom: 0;"><li style="line-height: 160%; box-sizing: content-box; position: relative;">平方损失函数：（对异常点敏感）<br clear="none"/>
<img src="https://app.yinxiang.com/FileSharing.action?hash=1/12df75200f435763f98c274df9efa9c7-2504" width="194" height="50"/></li><li style="line-height: 160%; box-sizing: content-box; position: relative;">绝对损失函数：（相当于做中值回归，比平方损失函数鲁棒，但在f=y处无法求导）<br clear="none"/>
<img src="https://app.yinxiang.com/FileSharing.action?hash=1/074db020573ffa0d52c9e1acf1fef459-2420" width="205" height="51"/></li><li style="line-height: 160%; box-sizing: content-box; position: relative;">Huber损失函数：（综合考虑可导性和鲁棒性）<br clear="none"/>
<img src="https://app.yinxiang.com/FileSharing.action?hash=1/b8ca141711ccb932974c90a79f42037f-4734" width="353" height="50"/><br clear="none"/>
在|f−y|较小时为平方损失，在|f−y|较大时为线性损失，处处可导，且对异常点鲁棒<br clear="none"/>
<img src="https://app.yinxiang.com/FileSharing.action?hash=1/8aa41fa38a0d16fda42d49e1bb3decb0-3553379" width="4000" height="3000"/></li></ul>
</li></ul>
</div><center style="display:none !important;visibility:collapse !important;height:0 !important;white-space:nowrap;width:100%;overflow:hidden">**1.%E6%9C%89%E7%9B%91%E7%9D%A3%E5%AD%A6%E4%B9%A0%E7%9A%84%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%EF%BC%9F**%0A**%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%EF%BC%8C%E9%A3%8E%E9%99%A9%E5%87%BD%E6%95%B0**%0A%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%E5%BA%A6%E9%87%8F%E6%A8%A1%E5%9E%8B%E4%B8%80%E6%AC%A1%E9%A2%84%E6%B5%8B%E7%9A%84%E5%A5%BD%E5%9D%8F%EF%BC%8C%E9%A3%8E%E9%99%A9%E5%87%BD%E6%95%B0%E5%BA%A6%E9%87%8F**%E5%B9%B3%E5%9D%87%E6%84%8F%E4%B9%89**%E4%B8%8B%E6%A8%A1%E5%9E%8B%E9%A2%84%E6%B5%8B%E7%9A%84%E5%A5%BD%E5%9D%8F%0A*%20%E5%88%86%E7%B1%BB%E9%97%AE%E9%A2%98%0A%20%20%20%20*%200-1%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%EF%BC%9A%0A%20%20%20%20!%5B3bafa4876140b66e707d2556f2619049.png%5D(en-resource%3A%2F%2Fdatabase%2F883%3A1)%0A%20%20%20%20*%20Hinge%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%EF%BC%9A0-1%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%E7%9B%B8%E5%AF%B9%E7%B4%A7%E7%9A%84%E5%87%B8%E4%B8%8A%E7%95%8C%0A%20%20%20%20!%5B33dc2b1484d6be57da661d2b25c9e7e5.png%5D(en-resource%3A%2F%2Fdatabase%2F885%3A1)%0A%20%20%20%20%E4%BD%BF%E7%94%A8%E6%AC%A1%E6%A2%AF%E5%BA%A6%E4%B8%8B%E9%99%8D%E6%B3%95%E6%B1%82%E8%A7%A3%0A%20%20%20%20*%20%E9%80%BB%E8%BE%91%E5%9B%9E%E5%BD%92%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%EF%BC%9A0-1%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%E7%9A%84%E5%87%B8%E4%B8%8A%E7%95%8C%0A%20%20%20%20!%5B9ce3c5061ee55d48ebe28444b2e06aff.png%5D(en-resource%3A%2F%2Fdatabase%2F887%3A1)%0A%20%20%20%20*%20%E4%BA%A4%E5%8F%89%E7%86%B5%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%EF%BC%9A0-1%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%E7%9A%84%E5%85%89%E6%BB%91%E5%87%B8%E4%B8%8A%E7%95%8C%0A%20%20%20%20!%5Bbafd9c2f9b490f5470f316e91db41876.png%5D(en-resource%3A%2F%2Fdatabase%2F889%3A1)%0A%20%20%20%20!%5Bc882e7e78509fe1b2da597ab68dc573b.jpeg%5D(en-resource%3A%2F%2Fdatabase%2F905%3A1)%0A*%20%E5%9B%9E%E5%BD%92%E9%97%AE%E9%A2%98%0A%20%20%20%20*%20%E5%B9%B3%E6%96%B9%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%EF%BC%9A%EF%BC%88%E5%AF%B9%E5%BC%82%E5%B8%B8%E7%82%B9%E6%95%8F%E6%84%9F%EF%BC%89%0A%20%20%20%20%20%20%20%20!%5B12df75200f435763f98c274df9efa9c7.png%5D(en-resource%3A%2F%2Fdatabase%2F891%3A1)%0A%20%20%20%20*%20%E7%BB%9D%E5%AF%B9%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%EF%BC%9A%EF%BC%88%E7%9B%B8%E5%BD%93%E4%BA%8E%E5%81%9A%E4%B8%AD%E5%80%BC%E5%9B%9E%E5%BD%92%EF%BC%8C%E6%AF%94%E5%B9%B3%E6%96%B9%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%E9%B2%81%E6%A3%92%EF%BC%8C%E4%BD%86%E5%9C%A8f%3Dy%E5%A4%84%E6%97%A0%E6%B3%95%E6%B1%82%E5%AF%BC%EF%BC%89%0A%20%20%20%20%20%20%20%20!%5B074db020573ffa0d52c9e1acf1fef459.png%5D(en-resource%3A%2F%2Fdatabase%2F893%3A1)%0A%20%20%20%20*%20Huber%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%EF%BC%9A%EF%BC%88%E7%BB%BC%E5%90%88%E8%80%83%E8%99%91%E5%8F%AF%E5%AF%BC%E6%80%A7%E5%92%8C%E9%B2%81%E6%A3%92%E6%80%A7%EF%BC%89%0A%20%20%20%20%20%20%20%20!%5Bb8ca141711ccb932974c90a79f42037f.png%5D(en-resource%3A%2F%2Fdatabase%2F895%3A1)%0A%20%20%20%20%20%20%20%20%E5%9C%A8%7Cf%E2%88%92y%7C%E8%BE%83%E5%B0%8F%E6%97%B6%E4%B8%BA%E5%B9%B3%E6%96%B9%E6%8D%9F%E5%A4%B1%EF%BC%8C%E5%9C%A8%7Cf%E2%88%92y%7C%E8%BE%83%E5%A4%A7%E6%97%B6%E4%B8%BA%E7%BA%BF%E6%80%A7%E6%8D%9F%E5%A4%B1%EF%BC%8C%E5%A4%84%E5%A4%84%E5%8F%AF%E5%AF%BC%EF%BC%8C%E4%B8%94%E5%AF%B9%E5%BC%82%E5%B8%B8%E7%82%B9%E9%B2%81%E6%A3%92%0A%20%20%20%20%20%20%20%20!%5B8aa41fa38a0d16fda42d49e1bb3decb0.jpeg%5D(en-resource%3A%2F%2Fdatabase%2F899%3A1)%0A%0A%0A%0A%0A%0A%0A</center></body></html>