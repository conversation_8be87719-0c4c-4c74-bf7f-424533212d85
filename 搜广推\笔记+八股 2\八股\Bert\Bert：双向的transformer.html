<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/><meta name="exporter-version" content="Evernote Mac 9.7.14 (474683)"/><meta name="created" content="2021-11-29 03:29:24 +0000"/><meta name="updated" content="2021-11-29 03:29:24 +0000"/><meta name="content-class" content="yinxiang.markdown"/><title>Bert：双向的transformer</title></head><body><div style="font-size: 14px; margin: 0; padding: 0; width: 100%;"><p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">Bert：双向的transformer</strong><br clear="none"/>
预训练模型，采用了 Transformer 的 Encoder 结构，没有decoder（只要学到其中语义关系即可，不需要去解码完成具体的任务。），但是模型结构比 Transformer 要深，多个 Transformer Encoder 一层一层地堆叠起来。<br clear="none"/>
训练主要分为两个阶段：预训练阶段和 Fine-tuning 阶段。预训练使用两个任务，使用Masked LM（Masked Language Model）学习词语在上下文中的表示；用Next Sentence Prediction来学习句子级表示。在它之前是GPT，GPT是一个单向语言模型的预训练过程（它和gpt的区别就是bert为啥叫双向 bi-directional），更适用于文本生成，通过前文去预测当前的字。<br clear="none"/>
<img src="https://app.yinxiang.com/FileSharing.action?hash=1/ca7079f3136c87fe5007ae19eb33893c-259590" width="448" height="938"/><br clear="none"/>
预训练阶段与 Word2Vec，ELMo 等类似，是在大型数据集上根据一些预训练任务训练得到。Fine-tuning 阶段是后续用于一些下游任务的时候进行微调，例如文本分类，词性标注，问答系统等，BERT 无需调整结构就可以在不同的任务上进行微调。<br clear="none"/>
模型的主要创新点都在pre-train方法上，即用了Masked LM和Next Sentence Prediction两种方法分别捕捉词语和句子级别的representation。</p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;">BERT模型的主要输入是文本中各个字/词(或者称为token)的原始词向量，该向量既可以随机初始化，也可以利用Word2Vector等算法进行预训练以作为初始值；输出是文本中各个字/词融合了全文语义信息后的向量表示，<br clear="none"/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">bert使用内置的tokenizer进行分词</strong><br clear="none"/>
tokenization使用的方法是WordPiece tokenization. 这是一个数据驱动式的tokenization方法，旨在权衡词典大小和oov词(训练时未出现，测试时出现了的单词)的个数。这种方法把例子中的“strawberries”切分成了“straw” 和“berries”。使用WordPiece tokenization让BERT在处理英文文本的时候仅需要存储30,522 个词，而且很少遇到oov的词。<br clear="none"/>
模型可以词根以及前缀等信息，学习到这个词的大致信息，而不是一个OOV。</p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">Masked LM</strong><br clear="none"/>
预训练任务1，类似完形填空，在句子中随机遮盖一部分单词，然后同时利用被覆盖单词上下文的信息预测遮盖的单词，这样可以更好地根据全文理解单词的意思。Masked LM 是 BERT 的重点，和 biLSTM 预测方法是有区别的。<br clear="none"/>
在将单词序列输入给 BERT 之前，每个序列中有 <strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">15％</strong> 的单词被 [MASK] token 替换。然后模型尝试基于序列中其他未被 mask 的单词的上下文来预测被mask的原单词.<br clear="none"/>
如果一直用标记[MASK]代替（在实际预测时是碰不到这个标记的）会影响模型，具体的MASK是有trick的：</p>
<ul style="line-height: 160%; box-sizing: content-box; display: block; list-style-type: disc; padding-left: 30px; margin: 6px 0 10px; color: #333;"><li style="line-height: 160%; box-sizing: content-box; position: relative;">10%的单词会被替代成其他单词，</li><li style="line-height: 160%; box-sizing: content-box; position: relative;">10%的单词不替换，</li><li style="line-height: 160%; box-sizing: content-box; position: relative;">剩下80%才被替换为[MASK]<br clear="none"/>
Mask掉的单词就是在输入侧加入的所谓噪音，这样<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">强迫模型在编码当前时刻的时候不能太依赖于当前的词，而要考虑它的上下文，增强泛化能力</strong>，<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">缺点导致预训练阶段和Fine-tuning阶段不一致的问题。</strong></li></ul>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">Next Sentence Prediction (NSP)</strong><br clear="none"/>
预训练任务2，下一句预测任务，这个任务主要是让模型能够更好地理解句子间的关系。<br clear="none"/>
句子级负采样<br clear="none"/>
BERT 的输入 Embedding 由三个部分相加得到：<br clear="none"/>
Token Embedding，Segment Embedding，Position Embedding。<br clear="none"/>
<img src="https://app.yinxiang.com/FileSharing.action?hash=1/cb0fa00eb1dd4a224cf8e5bebf9de337-276838" width="1200" height="364"/><br clear="none"/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">Token Embedding</strong>：单词的 Embedding，例如 [CLS] dog 等，通过训练学习得到。<br clear="none"/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">Segment Embedding</strong>：用于区分每一个单词属于句子 A 还是句子 B，如果只输入一个句子就只使用 EA，通过训练学习得到。<br clear="none"/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">Position Embedding</strong>：<br clear="none"/>
参数式编码，编码单词出现的位置，与 Transformer 使用固定的三角函数公式计算不同，BERT 的 Position Embedding 也是通过学习得到的。</p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">fine tuning</strong><br clear="none"/>
对于常规分类任务中，需要在 Transformer 的输出之上加一个分类层</p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">Bert适用任务</strong></p>
<ul style="line-height: 160%; box-sizing: content-box; display: block; list-style-type: disc; padding-left: 30px; margin: 6px 0 10px; color: #333;"><li style="line-height: 160%; box-sizing: content-box; position: relative;">如果NLP任务偏向在语言本身中就包含答案，而不特别依赖文本外的其它特征，往往应用Bert能够极大提升应用效果。典型的任务比如QA和阅读理解。</li><li style="line-height: 160%; box-sizing: content-box; position: relative;">Bert特别适合解决句子或者段落的匹配类任务。一个原因是：很可能是因为Bert在预训练阶段增加了Next Sentence Prediction任务，所以能够在预训练阶段学会一些句间关系的知识，而如果下游任务正好涉及到句间关系判断，就特别吻合Bert本身的长处，于是效果就特别明显。第二个可能的原因是：因为Self Attention机制自带句子A中单词和句子B中任意单词的Attention效果，而这种细粒度的匹配对于句子匹配类的任务尤其重要，所以Transformer的本质特性也决定了它特别适合解决这类任务。</li><li style="line-height: 160%; box-sizing: content-box; position: relative;">Bert适合解决需要深层语义特征的任务，对有些NLP任务来说，浅层的特征即可解决问题，典型的浅层特征性任务比如分词，POS词性标注，NER，文本分类等任务，这种类型的任务，只需要较短的上下文，以及浅层的非语义的特征，貌似就可以较好地解决问题。</li></ul>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">Bert的mask和CBOW区别？</strong></p>
<ul style="line-height: 160%; box-sizing: content-box; display: block; list-style-type: disc; padding-left: 30px; margin: 6px 0 10px; color: #333;"><li style="line-height: 160%; box-sizing: content-box; position: relative;">在CBOW中，每个单词都会成为input word，而BERT不是这么做的，原因是这样做的话，训练数据就太大了，而且训练时间也会非常长。</li><li style="line-height: 160%; box-sizing: content-box; position: relative;">对于输入数据部分，CBOW中的输入数据只有待预测单词的上下文，而BERT的输入是带有[MASK] token的“完整”句子，也就是说BERT在输入端将待预测的input word用[MASK] token代替了。</li></ul>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">bert为什么是双向的？</strong><br clear="none"/>
biLSTM是在网络结构上的双层，每个单词都从正向和反向都得到一个表示， 然后将此表示进行连接， 则此时认为这就是单词的双向表示。<br clear="none"/>
BERT并没有将一个序列反向输入到网络中，双向的意思表示它在处理一个词的时候，能考虑到该词前面和后面单词的信息，从而获取上下文的语义。<br clear="none"/>
transformer中的mask attention使得只能考虑当前词前面的词信息，所以使用双向transformer可以考虑前后单词信息。</p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">为什么 BERT 选择 mask 掉 15% 这个比例的词，可以是其他的比例吗？</strong><br clear="none"/>
效果好？<br clear="none"/>
从 CBOW 的滑动窗口角度，10%~20% 都是还 ok 的比例。</p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">为什么 BERT 在第一句前会加一个 [CLS] 标志?</strong><br clear="none"/>
BERT 在第一句前会加一个 [CLS] 标志，最后一层<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">该位对应向量可以作为整句话的语义表示</strong>，从而用于下游的分类任务等。为什么选它呢，因为与文本中已有的其它词相比，这个无明显语义信息的符号会 更“公平”地融合文本中各个词的语义信息，从而更好的表示整句话的语义。</p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">为什么Bert文本长度最多是512？</strong><br clear="none"/>
BERT 模型最多能处理 512 个 token 的文本。导致这一瓶颈的根本原因是 BERT 使用了从随机初始化训练出来的绝对位置编码，一般的最大位置设为了 512，因此顶多只能处理 512 个 token，多出来的部分就没有位置编码可用了。当然，还有一个重要的原因是 Attention的复杂度，导致长序列时显存用量大大增加，一般显卡也finetune不了。<br clear="none"/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">Bert wordpiece的不足</strong><br clear="none"/>
为了解决OOV的问题，我们通常会把一个词切分成更细粒度的WordPiece。BERT在Pretraining的时候是随机Mask这些WordPiece的，这就可能出现只Mask一个词的一部分的情况<br clear="none"/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">bert输入输出</strong><br clear="none"/>
BERT模型的主要输入是文本中各个词的原始词向量，该向量既可以随机初始化，也可以利用Word2Vector等算法进行预训练以作为初始值；输出是文本中各个词融合了全文语义信息后的向量表示。</p>
</div><center style="display:none !important;visibility:collapse !important;height:0 !important;white-space:nowrap;width:100%;overflow:hidden">**Bert%EF%BC%9A%E5%8F%8C%E5%90%91%E7%9A%84transformer**%0A%E9%A2%84%E8%AE%AD%E7%BB%83%E6%A8%A1%E5%9E%8B%EF%BC%8C%E9%87%87%E7%94%A8%E4%BA%86%20Transformer%20%E7%9A%84%20Encoder%20%E7%BB%93%E6%9E%84%EF%BC%8C%E6%B2%A1%E6%9C%89decoder%EF%BC%88%E5%8F%AA%E8%A6%81%E5%AD%A6%E5%88%B0%E5%85%B6%E4%B8%AD%E8%AF%AD%E4%B9%89%E5%85%B3%E7%B3%BB%E5%8D%B3%E5%8F%AF%EF%BC%8C%E4%B8%8D%E9%9C%80%E8%A6%81%E5%8E%BB%E8%A7%A3%E7%A0%81%E5%AE%8C%E6%88%90%E5%85%B7%E4%BD%93%E7%9A%84%E4%BB%BB%E5%8A%A1%E3%80%82%EF%BC%89%EF%BC%8C%E4%BD%86%E6%98%AF%E6%A8%A1%E5%9E%8B%E7%BB%93%E6%9E%84%E6%AF%94%20Transformer%20%E8%A6%81%E6%B7%B1%EF%BC%8C%E5%A4%9A%E4%B8%AA%20Transformer%20Encoder%20%E4%B8%80%E5%B1%82%E4%B8%80%E5%B1%82%E5%9C%B0%E5%A0%86%E5%8F%A0%E8%B5%B7%E6%9D%A5%E3%80%82%0A%E8%AE%AD%E7%BB%83%E4%B8%BB%E8%A6%81%E5%88%86%E4%B8%BA%E4%B8%A4%E4%B8%AA%E9%98%B6%E6%AE%B5%EF%BC%9A%E9%A2%84%E8%AE%AD%E7%BB%83%E9%98%B6%E6%AE%B5%E5%92%8C%20Fine-tuning%20%E9%98%B6%E6%AE%B5%E3%80%82%E9%A2%84%E8%AE%AD%E7%BB%83%E4%BD%BF%E7%94%A8%E4%B8%A4%E4%B8%AA%E4%BB%BB%E5%8A%A1%EF%BC%8C%E4%BD%BF%E7%94%A8Masked%20LM%EF%BC%88Masked%20Language%20Model%EF%BC%89%E5%AD%A6%E4%B9%A0%E8%AF%8D%E8%AF%AD%E5%9C%A8%E4%B8%8A%E4%B8%8B%E6%96%87%E4%B8%AD%E7%9A%84%E8%A1%A8%E7%A4%BA%EF%BC%9B%E7%94%A8Next%20Sentence%20Prediction%E6%9D%A5%E5%AD%A6%E4%B9%A0%E5%8F%A5%E5%AD%90%E7%BA%A7%E8%A1%A8%E7%A4%BA%E3%80%82%E5%9C%A8%E5%AE%83%E4%B9%8B%E5%89%8D%E6%98%AFGPT%EF%BC%8CGPT%E6%98%AF%E4%B8%80%E4%B8%AA%E5%8D%95%E5%90%91%E8%AF%AD%E8%A8%80%E6%A8%A1%E5%9E%8B%E7%9A%84%E9%A2%84%E8%AE%AD%E7%BB%83%E8%BF%87%E7%A8%8B%EF%BC%88%E5%AE%83%E5%92%8Cgpt%E7%9A%84%E5%8C%BA%E5%88%AB%E5%B0%B1%E6%98%AFbert%E4%B8%BA%E5%95%A5%E5%8F%AB%E5%8F%8C%E5%90%91%20bi-directional%EF%BC%89%EF%BC%8C%E6%9B%B4%E9%80%82%E7%94%A8%E4%BA%8E%E6%96%87%E6%9C%AC%E7%94%9F%E6%88%90%EF%BC%8C%E9%80%9A%E8%BF%87%E5%89%8D%E6%96%87%E5%8E%BB%E9%A2%84%E6%B5%8B%E5%BD%93%E5%89%8D%E7%9A%84%E5%AD%97%E3%80%82%0A!%5Bca7079f3136c87fe5007ae19eb33893c.png%5D(evernotecid%3A%2F%2F50E31125-9E8E-4F15-8430-8CF4D64A8B72%2Fappyinxiangcom%2F30104613%2FENResource%2Fp449)%0A%E9%A2%84%E8%AE%AD%E7%BB%83%E9%98%B6%E6%AE%B5%E4%B8%8E%20Word2Vec%EF%BC%8CELMo%20%E7%AD%89%E7%B1%BB%E4%BC%BC%EF%BC%8C%E6%98%AF%E5%9C%A8%E5%A4%A7%E5%9E%8B%E6%95%B0%E6%8D%AE%E9%9B%86%E4%B8%8A%E6%A0%B9%E6%8D%AE%E4%B8%80%E4%BA%9B%E9%A2%84%E8%AE%AD%E7%BB%83%E4%BB%BB%E5%8A%A1%E8%AE%AD%E7%BB%83%E5%BE%97%E5%88%B0%E3%80%82Fine-tuning%20%E9%98%B6%E6%AE%B5%E6%98%AF%E5%90%8E%E7%BB%AD%E7%94%A8%E4%BA%8E%E4%B8%80%E4%BA%9B%E4%B8%8B%E6%B8%B8%E4%BB%BB%E5%8A%A1%E7%9A%84%E6%97%B6%E5%80%99%E8%BF%9B%E8%A1%8C%E5%BE%AE%E8%B0%83%EF%BC%8C%E4%BE%8B%E5%A6%82%E6%96%87%E6%9C%AC%E5%88%86%E7%B1%BB%EF%BC%8C%E8%AF%8D%E6%80%A7%E6%A0%87%E6%B3%A8%EF%BC%8C%E9%97%AE%E7%AD%94%E7%B3%BB%E7%BB%9F%E7%AD%89%EF%BC%8CBERT%20%E6%97%A0%E9%9C%80%E8%B0%83%E6%95%B4%E7%BB%93%E6%9E%84%E5%B0%B1%E5%8F%AF%E4%BB%A5%E5%9C%A8%E4%B8%8D%E5%90%8C%E7%9A%84%E4%BB%BB%E5%8A%A1%E4%B8%8A%E8%BF%9B%E8%A1%8C%E5%BE%AE%E8%B0%83%E3%80%82%0A%E6%A8%A1%E5%9E%8B%E7%9A%84%E4%B8%BB%E8%A6%81%E5%88%9B%E6%96%B0%E7%82%B9%E9%83%BD%E5%9C%A8pre-train%E6%96%B9%E6%B3%95%E4%B8%8A%EF%BC%8C%E5%8D%B3%E7%94%A8%E4%BA%86Masked%20LM%E5%92%8CNext%20Sentence%20Prediction%E4%B8%A4%E7%A7%8D%E6%96%B9%E6%B3%95%E5%88%86%E5%88%AB%E6%8D%95%E6%8D%89%E8%AF%8D%E8%AF%AD%E5%92%8C%E5%8F%A5%E5%AD%90%E7%BA%A7%E5%88%AB%E7%9A%84representation%E3%80%82%0A%0ABERT%E6%A8%A1%E5%9E%8B%E7%9A%84%E4%B8%BB%E8%A6%81%E8%BE%93%E5%85%A5%E6%98%AF%E6%96%87%E6%9C%AC%E4%B8%AD%E5%90%84%E4%B8%AA%E5%AD%97%2F%E8%AF%8D(%E6%88%96%E8%80%85%E7%A7%B0%E4%B8%BAtoken)%E7%9A%84%E5%8E%9F%E5%A7%8B%E8%AF%8D%E5%90%91%E9%87%8F%EF%BC%8C%E8%AF%A5%E5%90%91%E9%87%8F%E6%97%A2%E5%8F%AF%E4%BB%A5%E9%9A%8F%E6%9C%BA%E5%88%9D%E5%A7%8B%E5%8C%96%EF%BC%8C%E4%B9%9F%E5%8F%AF%E4%BB%A5%E5%88%A9%E7%94%A8Word2Vector%E7%AD%89%E7%AE%97%E6%B3%95%E8%BF%9B%E8%A1%8C%E9%A2%84%E8%AE%AD%E7%BB%83%E4%BB%A5%E4%BD%9C%E4%B8%BA%E5%88%9D%E5%A7%8B%E5%80%BC%EF%BC%9B%E8%BE%93%E5%87%BA%E6%98%AF%E6%96%87%E6%9C%AC%E4%B8%AD%E5%90%84%E4%B8%AA%E5%AD%97%2F%E8%AF%8D%E8%9E%8D%E5%90%88%E4%BA%86%E5%85%A8%E6%96%87%E8%AF%AD%E4%B9%89%E4%BF%A1%E6%81%AF%E5%90%8E%E7%9A%84%E5%90%91%E9%87%8F%E8%A1%A8%E7%A4%BA%EF%BC%8C%0A**bert%E4%BD%BF%E7%94%A8%E5%86%85%E7%BD%AE%E7%9A%84tokenizer%E8%BF%9B%E8%A1%8C%E5%88%86%E8%AF%8D**%0Atokenization%E4%BD%BF%E7%94%A8%E7%9A%84%E6%96%B9%E6%B3%95%E6%98%AFWordPiece%20tokenization.%20%E8%BF%99%E6%98%AF%E4%B8%80%E4%B8%AA%E6%95%B0%E6%8D%AE%E9%A9%B1%E5%8A%A8%E5%BC%8F%E7%9A%84tokenization%E6%96%B9%E6%B3%95%EF%BC%8C%E6%97%A8%E5%9C%A8%E6%9D%83%E8%A1%A1%E8%AF%8D%E5%85%B8%E5%A4%A7%E5%B0%8F%E5%92%8Coov%E8%AF%8D(%E8%AE%AD%E7%BB%83%E6%97%B6%E6%9C%AA%E5%87%BA%E7%8E%B0%EF%BC%8C%E6%B5%8B%E8%AF%95%E6%97%B6%E5%87%BA%E7%8E%B0%E4%BA%86%E7%9A%84%E5%8D%95%E8%AF%8D)%E7%9A%84%E4%B8%AA%E6%95%B0%E3%80%82%E8%BF%99%E7%A7%8D%E6%96%B9%E6%B3%95%E6%8A%8A%E4%BE%8B%E5%AD%90%E4%B8%AD%E7%9A%84%E2%80%9Cstrawberries%E2%80%9D%E5%88%87%E5%88%86%E6%88%90%E4%BA%86%E2%80%9Cstraw%E2%80%9D%20%E5%92%8C%E2%80%9Cberries%E2%80%9D%E3%80%82%E4%BD%BF%E7%94%A8WordPiece%20tokenization%E8%AE%A9BERT%E5%9C%A8%E5%A4%84%E7%90%86%E8%8B%B1%E6%96%87%E6%96%87%E6%9C%AC%E7%9A%84%E6%97%B6%E5%80%99%E4%BB%85%E9%9C%80%E8%A6%81%E5%AD%98%E5%82%A830%2C522%20%E4%B8%AA%E8%AF%8D%EF%BC%8C%E8%80%8C%E4%B8%94%E5%BE%88%E5%B0%91%E9%81%87%E5%88%B0oov%E7%9A%84%E8%AF%8D%E3%80%82%0A%E6%A8%A1%E5%9E%8B%E5%8F%AF%E4%BB%A5%E8%AF%8D%E6%A0%B9%E4%BB%A5%E5%8F%8A%E5%89%8D%E7%BC%80%E7%AD%89%E4%BF%A1%E6%81%AF%EF%BC%8C%E5%AD%A6%E4%B9%A0%E5%88%B0%E8%BF%99%E4%B8%AA%E8%AF%8D%E7%9A%84%E5%A4%A7%E8%87%B4%E4%BF%A1%E6%81%AF%EF%BC%8C%E8%80%8C%E4%B8%8D%E6%98%AF%E4%B8%80%E4%B8%AAOOV%E3%80%82%0A%0A**Masked%20LM**%0A%E9%A2%84%E8%AE%AD%E7%BB%83%E4%BB%BB%E5%8A%A11%EF%BC%8C%E7%B1%BB%E4%BC%BC%E5%AE%8C%E5%BD%A2%E5%A1%AB%E7%A9%BA%EF%BC%8C%E5%9C%A8%E5%8F%A5%E5%AD%90%E4%B8%AD%E9%9A%8F%E6%9C%BA%E9%81%AE%E7%9B%96%E4%B8%80%E9%83%A8%E5%88%86%E5%8D%95%E8%AF%8D%EF%BC%8C%E7%84%B6%E5%90%8E%E5%90%8C%E6%97%B6%E5%88%A9%E7%94%A8%E8%A2%AB%E8%A6%86%E7%9B%96%E5%8D%95%E8%AF%8D%E4%B8%8A%E4%B8%8B%E6%96%87%E7%9A%84%E4%BF%A1%E6%81%AF%E9%A2%84%E6%B5%8B%E9%81%AE%E7%9B%96%E7%9A%84%E5%8D%95%E8%AF%8D%EF%BC%8C%E8%BF%99%E6%A0%B7%E5%8F%AF%E4%BB%A5%E6%9B%B4%E5%A5%BD%E5%9C%B0%E6%A0%B9%E6%8D%AE%E5%85%A8%E6%96%87%E7%90%86%E8%A7%A3%E5%8D%95%E8%AF%8D%E7%9A%84%E6%84%8F%E6%80%9D%E3%80%82Masked%20LM%20%E6%98%AF%20BERT%20%E7%9A%84%E9%87%8D%E7%82%B9%EF%BC%8C%E5%92%8C%20biLSTM%20%E9%A2%84%E6%B5%8B%E6%96%B9%E6%B3%95%E6%98%AF%E6%9C%89%E5%8C%BA%E5%88%AB%E7%9A%84%E3%80%82%20%0A%E5%9C%A8%E5%B0%86%E5%8D%95%E8%AF%8D%E5%BA%8F%E5%88%97%E8%BE%93%E5%85%A5%E7%BB%99%20BERT%20%E4%B9%8B%E5%89%8D%EF%BC%8C%E6%AF%8F%E4%B8%AA%E5%BA%8F%E5%88%97%E4%B8%AD%E6%9C%89%20**15%EF%BC%85**%20%E7%9A%84%E5%8D%95%E8%AF%8D%E8%A2%AB%20%5BMASK%5D%20token%20%E6%9B%BF%E6%8D%A2%E3%80%82%E7%84%B6%E5%90%8E%E6%A8%A1%E5%9E%8B%E5%B0%9D%E8%AF%95%E5%9F%BA%E4%BA%8E%E5%BA%8F%E5%88%97%E4%B8%AD%E5%85%B6%E4%BB%96%E6%9C%AA%E8%A2%AB%20mask%20%E7%9A%84%E5%8D%95%E8%AF%8D%E7%9A%84%E4%B8%8A%E4%B8%8B%E6%96%87%E6%9D%A5%E9%A2%84%E6%B5%8B%E8%A2%ABmask%E7%9A%84%E5%8E%9F%E5%8D%95%E8%AF%8D.%0A%E5%A6%82%E6%9E%9C%E4%B8%80%E7%9B%B4%E7%94%A8%E6%A0%87%E8%AE%B0%5BMASK%5D%E4%BB%A3%E6%9B%BF%EF%BC%88%E5%9C%A8%E5%AE%9E%E9%99%85%E9%A2%84%E6%B5%8B%E6%97%B6%E6%98%AF%E7%A2%B0%E4%B8%8D%E5%88%B0%E8%BF%99%E4%B8%AA%E6%A0%87%E8%AE%B0%E7%9A%84%EF%BC%89%E4%BC%9A%E5%BD%B1%E5%93%8D%E6%A8%A1%E5%9E%8B%EF%BC%8C%E5%85%B7%E4%BD%93%E7%9A%84MASK%E6%98%AF%E6%9C%89trick%E7%9A%84%EF%BC%9A%0A*%2010%25%E7%9A%84%E5%8D%95%E8%AF%8D%E4%BC%9A%E8%A2%AB%E6%9B%BF%E4%BB%A3%E6%88%90%E5%85%B6%E4%BB%96%E5%8D%95%E8%AF%8D%EF%BC%8C%0A*%2010%25%E7%9A%84%E5%8D%95%E8%AF%8D%E4%B8%8D%E6%9B%BF%E6%8D%A2%EF%BC%8C%0A*%20%E5%89%A9%E4%B8%8B80%25%E6%89%8D%E8%A2%AB%E6%9B%BF%E6%8D%A2%E4%B8%BA%5BMASK%5D%0AMask%E6%8E%89%E7%9A%84%E5%8D%95%E8%AF%8D%E5%B0%B1%E6%98%AF%E5%9C%A8%E8%BE%93%E5%85%A5%E4%BE%A7%E5%8A%A0%E5%85%A5%E7%9A%84%E6%89%80%E8%B0%93%E5%99%AA%E9%9F%B3%EF%BC%8C%E8%BF%99%E6%A0%B7**%E5%BC%BA%E8%BF%AB%E6%A8%A1%E5%9E%8B%E5%9C%A8%E7%BC%96%E7%A0%81%E5%BD%93%E5%89%8D%E6%97%B6%E5%88%BB%E7%9A%84%E6%97%B6%E5%80%99%E4%B8%8D%E8%83%BD%E5%A4%AA%E4%BE%9D%E8%B5%96%E4%BA%8E%E5%BD%93%E5%89%8D%E7%9A%84%E8%AF%8D%EF%BC%8C%E8%80%8C%E8%A6%81%E8%80%83%E8%99%91%E5%AE%83%E7%9A%84%E4%B8%8A%E4%B8%8B%E6%96%87%EF%BC%8C%E5%A2%9E%E5%BC%BA%E6%B3%9B%E5%8C%96%E8%83%BD%E5%8A%9B**%EF%BC%8C**%E7%BC%BA%E7%82%B9%E5%AF%BC%E8%87%B4%E9%A2%84%E8%AE%AD%E7%BB%83%E9%98%B6%E6%AE%B5%E5%92%8CFine-tuning%E9%98%B6%E6%AE%B5%E4%B8%8D%E4%B8%80%E8%87%B4%E7%9A%84%E9%97%AE%E9%A2%98%E3%80%82**%0A%0A**Next%20Sentence%20Prediction%20(NSP)**%0A%E9%A2%84%E8%AE%AD%E7%BB%83%E4%BB%BB%E5%8A%A12%EF%BC%8C%E4%B8%8B%E4%B8%80%E5%8F%A5%E9%A2%84%E6%B5%8B%E4%BB%BB%E5%8A%A1%EF%BC%8C%E8%BF%99%E4%B8%AA%E4%BB%BB%E5%8A%A1%E4%B8%BB%E8%A6%81%E6%98%AF%E8%AE%A9%E6%A8%A1%E5%9E%8B%E8%83%BD%E5%A4%9F%E6%9B%B4%E5%A5%BD%E5%9C%B0%E7%90%86%E8%A7%A3%E5%8F%A5%E5%AD%90%E9%97%B4%E7%9A%84%E5%85%B3%E7%B3%BB%E3%80%82%0A%E5%8F%A5%E5%AD%90%E7%BA%A7%E8%B4%9F%E9%87%87%E6%A0%B7%0ABERT%20%E7%9A%84%E8%BE%93%E5%85%A5%20Embedding%20%E7%94%B1%E4%B8%89%E4%B8%AA%E9%83%A8%E5%88%86%E7%9B%B8%E5%8A%A0%E5%BE%97%E5%88%B0%EF%BC%9A%0AToken%20Embedding%EF%BC%8CSegment%20Embedding%EF%BC%8CPosition%20Embedding%E3%80%82%0A!%5Bcb0fa00eb1dd4a224cf8e5bebf9de337.png%5D(evernotecid%3A%2F%2F50E31125-9E8E-4F15-8430-8CF4D64A8B72%2Fappyinxiangcom%2F30104613%2FENResource%2Fp448)%0A**Token%20Embedding**%EF%BC%9A%E5%8D%95%E8%AF%8D%E7%9A%84%20Embedding%EF%BC%8C%E4%BE%8B%E5%A6%82%20%5BCLS%5D%20dog%20%E7%AD%89%EF%BC%8C%E9%80%9A%E8%BF%87%E8%AE%AD%E7%BB%83%E5%AD%A6%E4%B9%A0%E5%BE%97%E5%88%B0%E3%80%82%0A**Segment%20Embedding**%EF%BC%9A%E7%94%A8%E4%BA%8E%E5%8C%BA%E5%88%86%E6%AF%8F%E4%B8%80%E4%B8%AA%E5%8D%95%E8%AF%8D%E5%B1%9E%E4%BA%8E%E5%8F%A5%E5%AD%90%20A%20%E8%BF%98%E6%98%AF%E5%8F%A5%E5%AD%90%20B%EF%BC%8C%E5%A6%82%E6%9E%9C%E5%8F%AA%E8%BE%93%E5%85%A5%E4%B8%80%E4%B8%AA%E5%8F%A5%E5%AD%90%E5%B0%B1%E5%8F%AA%E4%BD%BF%E7%94%A8%20EA%EF%BC%8C%E9%80%9A%E8%BF%87%E8%AE%AD%E7%BB%83%E5%AD%A6%E4%B9%A0%E5%BE%97%E5%88%B0%E3%80%82%0A**Position%20Embedding**%EF%BC%9A%0A%E5%8F%82%E6%95%B0%E5%BC%8F%E7%BC%96%E7%A0%81%EF%BC%8C%E7%BC%96%E7%A0%81%E5%8D%95%E8%AF%8D%E5%87%BA%E7%8E%B0%E7%9A%84%E4%BD%8D%E7%BD%AE%EF%BC%8C%E4%B8%8E%20Transformer%20%E4%BD%BF%E7%94%A8%E5%9B%BA%E5%AE%9A%E7%9A%84%E4%B8%89%E8%A7%92%E5%87%BD%E6%95%B0%E5%85%AC%E5%BC%8F%E8%AE%A1%E7%AE%97%E4%B8%8D%E5%90%8C%EF%BC%8CBERT%20%E7%9A%84%20Position%20Embedding%20%E4%B9%9F%E6%98%AF%E9%80%9A%E8%BF%87%E5%AD%A6%E4%B9%A0%E5%BE%97%E5%88%B0%E7%9A%84%E3%80%82%0A%0A**fine%20tuning**%0A%E5%AF%B9%E4%BA%8E%E5%B8%B8%E8%A7%84%E5%88%86%E7%B1%BB%E4%BB%BB%E5%8A%A1%E4%B8%AD%EF%BC%8C%E9%9C%80%E8%A6%81%E5%9C%A8%20Transformer%20%E7%9A%84%E8%BE%93%E5%87%BA%E4%B9%8B%E4%B8%8A%E5%8A%A0%E4%B8%80%E4%B8%AA%E5%88%86%E7%B1%BB%E5%B1%82%0A%0A**Bert%E9%80%82%E7%94%A8%E4%BB%BB%E5%8A%A1**%0A*%20%E5%A6%82%E6%9E%9CNLP%E4%BB%BB%E5%8A%A1%E5%81%8F%E5%90%91%E5%9C%A8%E8%AF%AD%E8%A8%80%E6%9C%AC%E8%BA%AB%E4%B8%AD%E5%B0%B1%E5%8C%85%E5%90%AB%E7%AD%94%E6%A1%88%EF%BC%8C%E8%80%8C%E4%B8%8D%E7%89%B9%E5%88%AB%E4%BE%9D%E8%B5%96%E6%96%87%E6%9C%AC%E5%A4%96%E7%9A%84%E5%85%B6%E5%AE%83%E7%89%B9%E5%BE%81%EF%BC%8C%E5%BE%80%E5%BE%80%E5%BA%94%E7%94%A8Bert%E8%83%BD%E5%A4%9F%E6%9E%81%E5%A4%A7%E6%8F%90%E5%8D%87%E5%BA%94%E7%94%A8%E6%95%88%E6%9E%9C%E3%80%82%E5%85%B8%E5%9E%8B%E7%9A%84%E4%BB%BB%E5%8A%A1%E6%AF%94%E5%A6%82QA%E5%92%8C%E9%98%85%E8%AF%BB%E7%90%86%E8%A7%A3%E3%80%82%0A*%20Bert%E7%89%B9%E5%88%AB%E9%80%82%E5%90%88%E8%A7%A3%E5%86%B3%E5%8F%A5%E5%AD%90%E6%88%96%E8%80%85%E6%AE%B5%E8%90%BD%E7%9A%84%E5%8C%B9%E9%85%8D%E7%B1%BB%E4%BB%BB%E5%8A%A1%E3%80%82%E4%B8%80%E4%B8%AA%E5%8E%9F%E5%9B%A0%E6%98%AF%EF%BC%9A%E5%BE%88%E5%8F%AF%E8%83%BD%E6%98%AF%E5%9B%A0%E4%B8%BABert%E5%9C%A8%E9%A2%84%E8%AE%AD%E7%BB%83%E9%98%B6%E6%AE%B5%E5%A2%9E%E5%8A%A0%E4%BA%86Next%20Sentence%20Prediction%E4%BB%BB%E5%8A%A1%EF%BC%8C%E6%89%80%E4%BB%A5%E8%83%BD%E5%A4%9F%E5%9C%A8%E9%A2%84%E8%AE%AD%E7%BB%83%E9%98%B6%E6%AE%B5%E5%AD%A6%E4%BC%9A%E4%B8%80%E4%BA%9B%E5%8F%A5%E9%97%B4%E5%85%B3%E7%B3%BB%E7%9A%84%E7%9F%A5%E8%AF%86%EF%BC%8C%E8%80%8C%E5%A6%82%E6%9E%9C%E4%B8%8B%E6%B8%B8%E4%BB%BB%E5%8A%A1%E6%AD%A3%E5%A5%BD%E6%B6%89%E5%8F%8A%E5%88%B0%E5%8F%A5%E9%97%B4%E5%85%B3%E7%B3%BB%E5%88%A4%E6%96%AD%EF%BC%8C%E5%B0%B1%E7%89%B9%E5%88%AB%E5%90%BB%E5%90%88Bert%E6%9C%AC%E8%BA%AB%E7%9A%84%E9%95%BF%E5%A4%84%EF%BC%8C%E4%BA%8E%E6%98%AF%E6%95%88%E6%9E%9C%E5%B0%B1%E7%89%B9%E5%88%AB%E6%98%8E%E6%98%BE%E3%80%82%E7%AC%AC%E4%BA%8C%E4%B8%AA%E5%8F%AF%E8%83%BD%E7%9A%84%E5%8E%9F%E5%9B%A0%E6%98%AF%EF%BC%9A%E5%9B%A0%E4%B8%BASelf%20Attention%E6%9C%BA%E5%88%B6%E8%87%AA%E5%B8%A6%E5%8F%A5%E5%AD%90A%E4%B8%AD%E5%8D%95%E8%AF%8D%E5%92%8C%E5%8F%A5%E5%AD%90B%E4%B8%AD%E4%BB%BB%E6%84%8F%E5%8D%95%E8%AF%8D%E7%9A%84Attention%E6%95%88%E6%9E%9C%EF%BC%8C%E8%80%8C%E8%BF%99%E7%A7%8D%E7%BB%86%E7%B2%92%E5%BA%A6%E7%9A%84%E5%8C%B9%E9%85%8D%E5%AF%B9%E4%BA%8E%E5%8F%A5%E5%AD%90%E5%8C%B9%E9%85%8D%E7%B1%BB%E7%9A%84%E4%BB%BB%E5%8A%A1%E5%B0%A4%E5%85%B6%E9%87%8D%E8%A6%81%EF%BC%8C%E6%89%80%E4%BB%A5Transformer%E7%9A%84%E6%9C%AC%E8%B4%A8%E7%89%B9%E6%80%A7%E4%B9%9F%E5%86%B3%E5%AE%9A%E4%BA%86%E5%AE%83%E7%89%B9%E5%88%AB%E9%80%82%E5%90%88%E8%A7%A3%E5%86%B3%E8%BF%99%E7%B1%BB%E4%BB%BB%E5%8A%A1%E3%80%82%0A*%20Bert%E9%80%82%E5%90%88%E8%A7%A3%E5%86%B3%E9%9C%80%E8%A6%81%E6%B7%B1%E5%B1%82%E8%AF%AD%E4%B9%89%E7%89%B9%E5%BE%81%E7%9A%84%E4%BB%BB%E5%8A%A1%EF%BC%8C%E5%AF%B9%E6%9C%89%E4%BA%9BNLP%E4%BB%BB%E5%8A%A1%E6%9D%A5%E8%AF%B4%EF%BC%8C%E6%B5%85%E5%B1%82%E7%9A%84%E7%89%B9%E5%BE%81%E5%8D%B3%E5%8F%AF%E8%A7%A3%E5%86%B3%E9%97%AE%E9%A2%98%EF%BC%8C%E5%85%B8%E5%9E%8B%E7%9A%84%E6%B5%85%E5%B1%82%E7%89%B9%E5%BE%81%E6%80%A7%E4%BB%BB%E5%8A%A1%E6%AF%94%E5%A6%82%E5%88%86%E8%AF%8D%EF%BC%8CPOS%E8%AF%8D%E6%80%A7%E6%A0%87%E6%B3%A8%EF%BC%8CNER%EF%BC%8C%E6%96%87%E6%9C%AC%E5%88%86%E7%B1%BB%E7%AD%89%E4%BB%BB%E5%8A%A1%EF%BC%8C%E8%BF%99%E7%A7%8D%E7%B1%BB%E5%9E%8B%E7%9A%84%E4%BB%BB%E5%8A%A1%EF%BC%8C%E5%8F%AA%E9%9C%80%E8%A6%81%E8%BE%83%E7%9F%AD%E7%9A%84%E4%B8%8A%E4%B8%8B%E6%96%87%EF%BC%8C%E4%BB%A5%E5%8F%8A%E6%B5%85%E5%B1%82%E7%9A%84%E9%9D%9E%E8%AF%AD%E4%B9%89%E7%9A%84%E7%89%B9%E5%BE%81%EF%BC%8C%E8%B2%8C%E4%BC%BC%E5%B0%B1%E5%8F%AF%E4%BB%A5%E8%BE%83%E5%A5%BD%E5%9C%B0%E8%A7%A3%E5%86%B3%E9%97%AE%E9%A2%98%E3%80%82%0A%0A**Bert%E7%9A%84mask%E5%92%8CCBOW%E5%8C%BA%E5%88%AB%EF%BC%9F**%0A*%20%E5%9C%A8CBOW%E4%B8%AD%EF%BC%8C%E6%AF%8F%E4%B8%AA%E5%8D%95%E8%AF%8D%E9%83%BD%E4%BC%9A%E6%88%90%E4%B8%BAinput%20word%EF%BC%8C%E8%80%8CBERT%E4%B8%8D%E6%98%AF%E8%BF%99%E4%B9%88%E5%81%9A%E7%9A%84%EF%BC%8C%E5%8E%9F%E5%9B%A0%E6%98%AF%E8%BF%99%E6%A0%B7%E5%81%9A%E7%9A%84%E8%AF%9D%EF%BC%8C%E8%AE%AD%E7%BB%83%E6%95%B0%E6%8D%AE%E5%B0%B1%E5%A4%AA%E5%A4%A7%E4%BA%86%EF%BC%8C%E8%80%8C%E4%B8%94%E8%AE%AD%E7%BB%83%E6%97%B6%E9%97%B4%E4%B9%9F%E4%BC%9A%E9%9D%9E%E5%B8%B8%E9%95%BF%E3%80%82%0A*%20%E5%AF%B9%E4%BA%8E%E8%BE%93%E5%85%A5%E6%95%B0%E6%8D%AE%E9%83%A8%E5%88%86%EF%BC%8CCBOW%E4%B8%AD%E7%9A%84%E8%BE%93%E5%85%A5%E6%95%B0%E6%8D%AE%E5%8F%AA%E6%9C%89%E5%BE%85%E9%A2%84%E6%B5%8B%E5%8D%95%E8%AF%8D%E7%9A%84%E4%B8%8A%E4%B8%8B%E6%96%87%EF%BC%8C%E8%80%8CBERT%E7%9A%84%E8%BE%93%E5%85%A5%E6%98%AF%E5%B8%A6%E6%9C%89%5BMASK%5D%20token%E7%9A%84%E2%80%9C%E5%AE%8C%E6%95%B4%E2%80%9D%E5%8F%A5%E5%AD%90%EF%BC%8C%E4%B9%9F%E5%B0%B1%E6%98%AF%E8%AF%B4BERT%E5%9C%A8%E8%BE%93%E5%85%A5%E7%AB%AF%E5%B0%86%E5%BE%85%E9%A2%84%E6%B5%8B%E7%9A%84input%20word%E7%94%A8%5BMASK%5D%20token%E4%BB%A3%E6%9B%BF%E4%BA%86%E3%80%82%0A%0A**bert%E4%B8%BA%E4%BB%80%E4%B9%88%E6%98%AF%E5%8F%8C%E5%90%91%E7%9A%84%EF%BC%9F**%0AbiLSTM%E6%98%AF%E5%9C%A8%E7%BD%91%E7%BB%9C%E7%BB%93%E6%9E%84%E4%B8%8A%E7%9A%84%E5%8F%8C%E5%B1%82%EF%BC%8C%E6%AF%8F%E4%B8%AA%E5%8D%95%E8%AF%8D%E9%83%BD%E4%BB%8E%E6%AD%A3%E5%90%91%E5%92%8C%E5%8F%8D%E5%90%91%E9%83%BD%E5%BE%97%E5%88%B0%E4%B8%80%E4%B8%AA%E8%A1%A8%E7%A4%BA%EF%BC%8C%20%E7%84%B6%E5%90%8E%E5%B0%86%E6%AD%A4%E8%A1%A8%E7%A4%BA%E8%BF%9B%E8%A1%8C%E8%BF%9E%E6%8E%A5%EF%BC%8C%20%E5%88%99%E6%AD%A4%E6%97%B6%E8%AE%A4%E4%B8%BA%E8%BF%99%E5%B0%B1%E6%98%AF%E5%8D%95%E8%AF%8D%E7%9A%84%E5%8F%8C%E5%90%91%E8%A1%A8%E7%A4%BA%E3%80%82%0ABERT%E5%B9%B6%E6%B2%A1%E6%9C%89%E5%B0%86%E4%B8%80%E4%B8%AA%E5%BA%8F%E5%88%97%E5%8F%8D%E5%90%91%E8%BE%93%E5%85%A5%E5%88%B0%E7%BD%91%E7%BB%9C%E4%B8%AD%EF%BC%8C%E5%8F%8C%E5%90%91%E7%9A%84%E6%84%8F%E6%80%9D%E8%A1%A8%E7%A4%BA%E5%AE%83%E5%9C%A8%E5%A4%84%E7%90%86%E4%B8%80%E4%B8%AA%E8%AF%8D%E7%9A%84%E6%97%B6%E5%80%99%EF%BC%8C%E8%83%BD%E8%80%83%E8%99%91%E5%88%B0%E8%AF%A5%E8%AF%8D%E5%89%8D%E9%9D%A2%E5%92%8C%E5%90%8E%E9%9D%A2%E5%8D%95%E8%AF%8D%E7%9A%84%E4%BF%A1%E6%81%AF%EF%BC%8C%E4%BB%8E%E8%80%8C%E8%8E%B7%E5%8F%96%E4%B8%8A%E4%B8%8B%E6%96%87%E7%9A%84%E8%AF%AD%E4%B9%89%E3%80%82%0Atransformer%E4%B8%AD%E7%9A%84mask%20attention%E4%BD%BF%E5%BE%97%E5%8F%AA%E8%83%BD%E8%80%83%E8%99%91%E5%BD%93%E5%89%8D%E8%AF%8D%E5%89%8D%E9%9D%A2%E7%9A%84%E8%AF%8D%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%89%80%E4%BB%A5%E4%BD%BF%E7%94%A8%E5%8F%8C%E5%90%91transformer%E5%8F%AF%E4%BB%A5%E8%80%83%E8%99%91%E5%89%8D%E5%90%8E%E5%8D%95%E8%AF%8D%E4%BF%A1%E6%81%AF%E3%80%82%0A%0A**%E4%B8%BA%E4%BB%80%E4%B9%88%20BERT%20%E9%80%89%E6%8B%A9%20mask%20%E6%8E%89%2015%25%20%E8%BF%99%E4%B8%AA%E6%AF%94%E4%BE%8B%E7%9A%84%E8%AF%8D%EF%BC%8C%E5%8F%AF%E4%BB%A5%E6%98%AF%E5%85%B6%E4%BB%96%E7%9A%84%E6%AF%94%E4%BE%8B%E5%90%97%EF%BC%9F**%0A%E6%95%88%E6%9E%9C%E5%A5%BD%EF%BC%9F%0A%E4%BB%8E%20CBOW%20%E7%9A%84%E6%BB%91%E5%8A%A8%E7%AA%97%E5%8F%A3%E8%A7%92%E5%BA%A6%EF%BC%8C10%25~20%25%20%E9%83%BD%E6%98%AF%E8%BF%98%20ok%20%E7%9A%84%E6%AF%94%E4%BE%8B%E3%80%82%0A%0A**%E4%B8%BA%E4%BB%80%E4%B9%88%20BERT%20%E5%9C%A8%E7%AC%AC%E4%B8%80%E5%8F%A5%E5%89%8D%E4%BC%9A%E5%8A%A0%E4%B8%80%E4%B8%AA%20%5BCLS%5D%20%E6%A0%87%E5%BF%97%3F**%0ABERT%20%E5%9C%A8%E7%AC%AC%E4%B8%80%E5%8F%A5%E5%89%8D%E4%BC%9A%E5%8A%A0%E4%B8%80%E4%B8%AA%20%5BCLS%5D%20%E6%A0%87%E5%BF%97%EF%BC%8C%E6%9C%80%E5%90%8E%E4%B8%80%E5%B1%82**%E8%AF%A5%E4%BD%8D%E5%AF%B9%E5%BA%94%E5%90%91%E9%87%8F%E5%8F%AF%E4%BB%A5%E4%BD%9C%E4%B8%BA%E6%95%B4%E5%8F%A5%E8%AF%9D%E7%9A%84%E8%AF%AD%E4%B9%89%E8%A1%A8%E7%A4%BA**%EF%BC%8C%E4%BB%8E%E8%80%8C%E7%94%A8%E4%BA%8E%E4%B8%8B%E6%B8%B8%E7%9A%84%E5%88%86%E7%B1%BB%E4%BB%BB%E5%8A%A1%E7%AD%89%E3%80%82%E4%B8%BA%E4%BB%80%E4%B9%88%E9%80%89%E5%AE%83%E5%91%A2%EF%BC%8C%E5%9B%A0%E4%B8%BA%E4%B8%8E%E6%96%87%E6%9C%AC%E4%B8%AD%E5%B7%B2%E6%9C%89%E7%9A%84%E5%85%B6%E5%AE%83%E8%AF%8D%E7%9B%B8%E6%AF%94%EF%BC%8C%E8%BF%99%E4%B8%AA%E6%97%A0%E6%98%8E%E6%98%BE%E8%AF%AD%E4%B9%89%E4%BF%A1%E6%81%AF%E7%9A%84%E7%AC%A6%E5%8F%B7%E4%BC%9A%C2%A0%E6%9B%B4%E2%80%9C%E5%85%AC%E5%B9%B3%E2%80%9D%E5%9C%B0%E8%9E%8D%E5%90%88%E6%96%87%E6%9C%AC%E4%B8%AD%E5%90%84%E4%B8%AA%E8%AF%8D%E7%9A%84%E8%AF%AD%E4%B9%89%E4%BF%A1%E6%81%AF%EF%BC%8C%E4%BB%8E%E8%80%8C%E6%9B%B4%E5%A5%BD%E7%9A%84%E8%A1%A8%E7%A4%BA%E6%95%B4%E5%8F%A5%E8%AF%9D%E7%9A%84%E8%AF%AD%E4%B9%89%E3%80%82%0A%0A**%E4%B8%BA%E4%BB%80%E4%B9%88Bert%E6%96%87%E6%9C%AC%E9%95%BF%E5%BA%A6%E6%9C%80%E5%A4%9A%E6%98%AF512%EF%BC%9F**%0ABERT%20%E6%A8%A1%E5%9E%8B%E6%9C%80%E5%A4%9A%E8%83%BD%E5%A4%84%E7%90%86%20512%20%E4%B8%AA%20token%20%E7%9A%84%E6%96%87%E6%9C%AC%E3%80%82%E5%AF%BC%E8%87%B4%E8%BF%99%E4%B8%80%E7%93%B6%E9%A2%88%E7%9A%84%E6%A0%B9%E6%9C%AC%E5%8E%9F%E5%9B%A0%E6%98%AF%20BERT%20%E4%BD%BF%E7%94%A8%E4%BA%86%E4%BB%8E%E9%9A%8F%E6%9C%BA%E5%88%9D%E5%A7%8B%E5%8C%96%E8%AE%AD%E7%BB%83%E5%87%BA%E6%9D%A5%E7%9A%84%E7%BB%9D%E5%AF%B9%E4%BD%8D%E7%BD%AE%E7%BC%96%E7%A0%81%EF%BC%8C%E4%B8%80%E8%88%AC%E7%9A%84%E6%9C%80%E5%A4%A7%E4%BD%8D%E7%BD%AE%E8%AE%BE%E4%B8%BA%E4%BA%86%20512%EF%BC%8C%E5%9B%A0%E6%AD%A4%E9%A1%B6%E5%A4%9A%E5%8F%AA%E8%83%BD%E5%A4%84%E7%90%86%20512%20%E4%B8%AA%20token%EF%BC%8C%E5%A4%9A%E5%87%BA%E6%9D%A5%E7%9A%84%E9%83%A8%E5%88%86%E5%B0%B1%E6%B2%A1%E6%9C%89%E4%BD%8D%E7%BD%AE%E7%BC%96%E7%A0%81%E5%8F%AF%E7%94%A8%E4%BA%86%E3%80%82%E5%BD%93%E7%84%B6%EF%BC%8C%E8%BF%98%E6%9C%89%E4%B8%80%E4%B8%AA%E9%87%8D%E8%A6%81%E7%9A%84%E5%8E%9F%E5%9B%A0%E6%98%AF%20Attention%E7%9A%84%E5%A4%8D%E6%9D%82%E5%BA%A6%EF%BC%8C%E5%AF%BC%E8%87%B4%E9%95%BF%E5%BA%8F%E5%88%97%E6%97%B6%E6%98%BE%E5%AD%98%E7%94%A8%E9%87%8F%E5%A4%A7%E5%A4%A7%E5%A2%9E%E5%8A%A0%EF%BC%8C%E4%B8%80%E8%88%AC%E6%98%BE%E5%8D%A1%E4%B9%9Ffinetune%E4%B8%8D%E4%BA%86%E3%80%82%0A**Bert%20wordpiece%E7%9A%84%E4%B8%8D%E8%B6%B3**%0A%E4%B8%BA%E4%BA%86%E8%A7%A3%E5%86%B3OOV%E7%9A%84%E9%97%AE%E9%A2%98%EF%BC%8C%E6%88%91%E4%BB%AC%E9%80%9A%E5%B8%B8%E4%BC%9A%E6%8A%8A%E4%B8%80%E4%B8%AA%E8%AF%8D%E5%88%87%E5%88%86%E6%88%90%E6%9B%B4%E7%BB%86%E7%B2%92%E5%BA%A6%E7%9A%84WordPiece%E3%80%82BERT%E5%9C%A8Pretraining%E7%9A%84%E6%97%B6%E5%80%99%E6%98%AF%E9%9A%8F%E6%9C%BAMask%E8%BF%99%E4%BA%9BWordPiece%E7%9A%84%EF%BC%8C%E8%BF%99%E5%B0%B1%E5%8F%AF%E8%83%BD%E5%87%BA%E7%8E%B0%E5%8F%AAMask%E4%B8%80%E4%B8%AA%E8%AF%8D%E7%9A%84%E4%B8%80%E9%83%A8%E5%88%86%E7%9A%84%E6%83%85%E5%86%B5%0A**bert%E8%BE%93%E5%85%A5%E8%BE%93%E5%87%BA**%0ABERT%E6%A8%A1%E5%9E%8B%E7%9A%84%E4%B8%BB%E8%A6%81%E8%BE%93%E5%85%A5%E6%98%AF%E6%96%87%E6%9C%AC%E4%B8%AD%E5%90%84%E4%B8%AA%E8%AF%8D%E7%9A%84%E5%8E%9F%E5%A7%8B%E8%AF%8D%E5%90%91%E9%87%8F%EF%BC%8C%E8%AF%A5%E5%90%91%E9%87%8F%E6%97%A2%E5%8F%AF%E4%BB%A5%E9%9A%8F%E6%9C%BA%E5%88%9D%E5%A7%8B%E5%8C%96%EF%BC%8C%E4%B9%9F%E5%8F%AF%E4%BB%A5%E5%88%A9%E7%94%A8Word2Vector%E7%AD%89%E7%AE%97%E6%B3%95%E8%BF%9B%E8%A1%8C%E9%A2%84%E8%AE%AD%E7%BB%83%E4%BB%A5%E4%BD%9C%E4%B8%BA%E5%88%9D%E5%A7%8B%E5%80%BC%EF%BC%9B%E8%BE%93%E5%87%BA%E6%98%AF%E6%96%87%E6%9C%AC%E4%B8%AD%E5%90%84%E4%B8%AA%E8%AF%8D%E8%9E%8D%E5%90%88%E4%BA%86%E5%85%A8%E6%96%87%E8%AF%AD%E4%B9%89%E4%BF%A1%E6%81%AF%E5%90%8E%E7%9A%84%E5%90%91%E9%87%8F%E8%A1%A8%E7%A4%BA%E3%80%82</center></body></html>