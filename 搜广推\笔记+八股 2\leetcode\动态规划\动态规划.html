<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/><meta name="exporter-version" content="Evernote Mac 9.7.14 (474683)"/><meta name="author" content="安能行叹复坐愁"/><meta name="created" content="2022-04-24 08:49:29 +0000"/><meta name="source" content="desktop.mac"/><meta name="source-url" content="about:blank"/><meta name="updated" content="2024-04-10 02:07:53 +0000"/><meta name="application-data:yinxiang.superNote" content="{&quot;aiMode&quot;:false}"/><title>动态规划</title></head><body style="font-size: 16px;"><div><div><span style="font-size: 16px;"><span style="font-size: 12px;">1.爬楼梯</span></span></div><div><span style="font-size: 12px;">2.最小花费爬楼梯</span></div><div><span style="font-size: 12px;">3.不同路径</span></div><div><span style="font-size: 12px;">4.不同路径2（有障碍）</span></div><div><span style="font-size: 12px;">  地下城游戏（反着DP）</span></div><div><span style="font-size: 12px;">  三角形最小路径和</span></div><div><span style="font-size: 12px;">  交错字符串（判断s3能否由s1和s2交错构成）</span></div><div><span style="font-size: 12px;">5.整数拆分</span></div><div><span style="font-size: 12px;">6.剪绳子</span></div><div><span style="font-size: 12px;">7.</span><span style="font-size: 12px; font-weight: bold;">01背包</span><span style="font-size: 12px;">（第二个for逆序）</span></div><div><span style="font-size: 12px;">8.分割等和子集</span></div><div><span style="font-size: 12px;">  （一维，二维，两部分具体是啥）</span></div><div><span style="font-size: 12px;">9.最后一块石头的重量</span></div><div><span style="font-size: 12px;">10.目标和（1+1+1+1+1=5）</span></div><div><span style="font-size: 12px;">11.一和零（双背包）</span></div><div><span style="font-size: 12px;">12.完全背包</span></div><div><span style="font-size: 12px;">13.零钱兑换2（几种方法，求组合） </span></div><div><span style="font-size: 12px;">14.组合总和（求排列）</span></div><div><span style="font-size: 12px;">15.零钱兑换1（最少的硬币个数？dp[0]=0）</span></div><div><span style="font-size: 12px;">16.完全平方数（和零钱兑换1完全一样）</span></div><div><span style="font-size: 12px;">17.单词拆分（一）</span></div><div><span style="font-size: 12px;">   单词拆分（二）</span></div><div><span style="font-size: 12px;">18.</span><span style="font-size: 12px; font-weight: bold;">打家劫舍</span></div><div><span style="font-size: 12px;">19.买卖股票的最佳时机（买卖一次）</span></div><div><span style="font-size: 12px;">   </span><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">买卖股票的最佳时机2（买卖无数次）</span></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">   冷冻期</span></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">   </span><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">买卖股票的最佳时机3（最多完成两笔交易）</span></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">   最多完成k笔交易</span></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">   有手续费</span></div><div><span style="font-size: 12px;">20.最长递增子序列</span></div><div><span style="font-size: 12px;">   具体是啥，字典序最小的</span></div><div><span style="font-size: 12px;">   最长递增子序列有几个</span></div><div><span style="font-size: 12px;">   信封嵌套</span></div><div><span style="font-size: 12px;">   数组中的最长连续子序列</span></div><div><span style="font-size: 12px;">21.最长上升子数组。</span><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">最长递增子数组</span></div><div><span style="font-size: 12px;">   最长严格上升子数组（可以改变一个元素）</span></div><div><span style="font-size: 12px;">22.最长重复子数组/子串， 最长公共子串，最长公共子数组</span></div><div><span style="font-size: 12px;">   最长重复子数组/子串具体是啥</span></div><div><span style="font-size: 12px;">23.最长公共子序列</span></div><div><span style="font-size: 12px;">   最长公共子序列具体是啥，最长公共子序列（二）</span></div><div><span style="font-size: 12px; color: rgb(255, 38, 0);">   最短公共超序列</span></div><div><span style="font-size: 12px;">24.不相交的线</span></div><div><span style="font-size: 12px;">25.最大子数组和</span></div><div><span style="font-size: 12px;">   最大子数组和具体是啥</span></div><div><font style="font-size: 12px;"><span style="font-size: 12px; color: rgb(255, 38, 0);">   </span><span style="font-size: 12px; color: rgb(255, 38, 0); font-weight: bold;">最短</span><span style="font-size: 12px; color: rgb(255, 38, 0); font-weight: bold;">无序</span><span style="font-size: 12px; color: rgb(255, 38, 0); font-weight: bold;">连续子数组，部分排序</span></font></div><div><span style="font-size: 12px;">26.判断子序列</span></div><div><span style="font-size: 12px;">27.不同的子序列</span></div><div><span style="font-size: 12px;">28.两个字符串的删除操作</span></div><div><span style="font-size: 12px;">29.编辑距离</span></div><div><span style="font-size: 12px;">   编辑距离（二）</span></div><div><span style="font-size: 12px;">30.回文子串个数</span></div><div><span style="font-size: 12px;">31.最多删除一个字符得到回文串编辑距离</span></div><div><span style="font-size: 12px;">32.最长回文子序列</span></div><div><span style="font-size: 12px;">33.最长回文子串（多长，是啥）</span></div><div><span style="font-size: 12px;">34.买票问题</span></div><div><font style="font-size: 12px;"><span style="font-size: 12px; font-weight: bold;">36.</span><span style="font-size: 12px; font-weight: bold;">矩阵的最小路径和</span></font></div><div><span style="font-size: 12px; font-weight: bold;">37.把数字翻译成子串，把数字翻译成字符串</span></div><div><span style="font-size: 12px; font-weight: bold;">38.数的划分</span></div><div><span style="font-size: 12px;">39.信封嵌套</span></div><div><span style="font-size: 12px;">40.单词搜索</span></div><div><span style="font-size: 12px;">41.最大正方形</span></div><div><span style="font-size: 12px;">42.丢棋子问题</span></div><div><span style="font-size: 12px;">43.连续子数组的最大乘积</span></div><div><span style="font-size: 12px;">44.自由之路（转轮盘，匹配）</span></div><div><span style="font-size: 12px;">45.高楼扔鸡蛋</span></div><div><span style="font-size: 12px;">46.博弈问题</span></div><div><span style="font-size: 12px;">47.戳气球</span></div><div><span style="font-size: 12px;"><br/></span></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">1.爬楼梯</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">假设你正在爬楼梯。需要 n 阶你才能到达楼顶。</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">每次你可以爬 1 或 2 个台阶。你有多少种不同的方法可以爬到楼顶呢？</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">dp[i]代表到达第i阶，有多少种走法，初始化数组元素为0</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">def climbStairs(self, n):</font></div><div><font style="font-size: 12px;">    dp = [0] * (n + 1)</font></div><div><font style="font-size: 12px;">    if n &lt; 2:</font></div><div><font style="font-size: 12px;">        return n</font></div><div><font style="font-size: 12px;">    dp[0] = 1</font></div><div><font style="font-size: 12px;">    dp[1] = 1</font></div><div><font style="font-size: 12px;">    for i in range(2, n + 1):</font></div><div><font style="font-size: 12px;">        dp[i] = dp[i - 1] + dp[i - 2]</font></div><div><font style="font-size: 12px;">    return dp[-1]</font></div></div><div><span style="font-size: 12px; font-weight: bold;">滚动数组：</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">def climbStairs(self, n):</font></div><div><font style="font-size: 12px;">    if n &lt; 2:</font></div><div><font style="font-size: 12px;">        return n</font></div><div><font style="font-size: 12px;">    dp_i_1 = 1  # 代表原来的dp[i-1]</font></div><div><font style="font-size: 12px;">    dp_i_2 = 1  # 代表原来的dp[i-2]</font></div><div><font style="font-size: 12px;">    for i in range(2, n + 1):</font></div><div><font style="font-size: 12px;">        dp_i = dp_i_1 + dp_i_2  # 更新dp</font></div><div><font style="font-size: 12px;">        dp_i_2 = dp_i_1  # 往前挪</font></div><div><font style="font-size: 12px;">        dp_i_1 = dp_i</font></div><div><font style="font-size: 12px;">    return dp_i</font></div></div><div><span style="font-size: 12px; font-weight: bold;">变形1：不能爬7的倍数</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">def climbStairs( n):</font></div><div><font style="font-size: 12px;">    if n &lt; 2:</font></div><div><font style="font-size: 12px;">        return n</font></div><div><font style="font-size: 12px;">    dp_i_1 = 1  # 代表原来的dp[i-1]</font></div><div><font style="font-size: 12px;">    dp_i_2 = 1  # 代表原来的dp[i-2]</font></div><div><font style="font-size: 12px;">    for i in range(2, n + 1):</font></div><div><font style="font-size: 12px;">        if i%7==0:</font></div><div><font style="font-size: 12px;">            dp_i = 0 # 如果i-1为7的倍数，相当于此时只能用i-2阶跨2阶到i</font></div><div><font style="font-size: 12px;">        else:</font></div><div><font style="font-size: 12px;">            dp_i = dp_i_1 + dp_i_2  # 更新dp</font></div><div><font style="font-size: 12px;">        dp_i_2 = dp_i_1  # 往前挪</font></div><div><font style="font-size: 12px;">        dp_i_1 = dp_i</font></div><div><font style="font-size: 12px;">    return dp_i</font></div><div><font style="font-size: 12px;">for i in range(18):</font></div><div><font style="font-size: 12px;">    print(climbStairs(i))</font></div></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;; font-weight: bold;">变形2：不能连续两次跳2级。</span></div><div><span style="font-size: 12px; color: rgb(255, 0, 0); font-family: &quot;Courier New&quot;;">思路：相当于要么一次跳1级，要么一次跳3级。</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;; font-weight: bold;">变形3：可以一次跳1级、2级...n级</span></div><div><span style="font-size: 12px; color: rgb(255, 0, 0); font-family: &quot;Courier New&quot;;">f[1] = 1</span></div><div><span style="font-size: 12px; color: rgb(255, 0, 0); font-family: &quot;Courier New&quot;;">f[n] = 2^(n-1)</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;; font-weight: bold;">变形4：总共有n级，青蛙一次可以跳1级、2级...m级</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">如果n&lt;=m，则同上，直接返回2^(n-1)。</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">如果n&gt;m，则有： </span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">f[n] = f[n-1] + f[n-2] + f[n-3] + ... + f[n-m] </span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">f[n-1] = f[n-2] + f[n-3] + ... + f[n-1-m] ，两式相减</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">f[n] = 2*f[n-1] - f[n-1-m]</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">import math</font></div><div><font style="font-size: 12px;">def test(n, m):</font></div><div><font style="font-size: 12px;">    dp = [0]*(m+3) # 前m个。   m+1， m+2</font></div><div><font style="font-size: 12px;">    dp[0] = 1</font></div><div><font style="font-size: 12px;">    if n==0:</font></div><div><font style="font-size: 12px;">        return 0</font></div><div><font style="font-size: 12px;">    dp[1] = 1</font></div><div><font style="font-size: 12px;">    if n&lt;=m:</font></div><div><font style="font-size: 12px;">        return math.pow(2, n-1)</font></div><div><font style="font-size: 12px;">    else:</font></div><div><font style="font-size: 12px;">        que = [1, 1] # 把dp[0]和dp[1]加进去</font></div><div><font style="font-size: 12px;">        for i in range(2, m+1):</font></div><div><font style="font-size: 12px;">            dp[i] = math.pow(2, i-1)</font></div><div><font style="font-size: 12px;">            que.append(dp[i])</font></div><div><font style="font-size: 12px;">            </font></div><div><font style="font-size: 12px;">        for i in range(m+1, n+1):</font></div><div><font style="font-size: 12px;">            tmp1 = que[-1]</font></div><div><font style="font-size: 12px;">            tmp2 = que[0]</font></div><div><font style="font-size: 12px;">            que.pop(0)</font></div><div><font style="font-size: 12px;">            que.append(tmp1*2-tmp2) # dp[i]的值只和dp[i-1]、dp[i-m-1]有关，所以维护一个长度为m的队列即可。</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">    return que[-1]</font></div></div><div><span style="font-size: 12px; font-weight: bold;">变形4：输出具体每条路径。（组合总和4）</span></div><div><span style="font-size: 12px;">输入：nums = [1,2,3], target = 4</span></div><div><span style="font-size: 12px;">输出：</span><span style="font-size: 12px; color: unset; font-family: unset;">所有可能的组合为： (1, 1, 1, 1) (1, 1, 2) (1, 2, 1) (1, 3) (2, 1, 1) (2, 2) (3, 1)</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">result = []</font></div><div><font style="font-size: 12px;">path = []</font></div><div><font style="font-size: 12px;">def backtracking(nums, target):</font></div><div><font style="font-size: 12px;">    if target==0:</font></div><div><font style="font-size: 12px;">        result.append(path[:])</font></div><div><font style="font-size: 12px;">        return</font></div><div><font style="font-size: 12px;">    for i in range(0, len(nums)):</font></div><div><font style="font-size: 12px;">        if target-nums[i]&lt;0:</font></div><div><font style="font-size: 12px;">            continue</font></div><div><font style="font-size: 12px;">        path.append(nums[i])</font></div><div><font style="font-size: 12px;">        backtracking(nums, target-nums[i])</font></div><div><font style="font-size: 12px;">        path.pop()</font></div><div><font style="font-size: 12px;">backtracking([1,2,3], 4)</font></div><div><font style="font-size: 12px;">print(result)</font></div></div><div><span style="font-size: 12px; font-weight: bold;">变形5：用快速幂求斐波那契数列</span></div><div><span style="font-size: 12px;"><img src="%E5%8A%A8%E6%80%81%E8%A7%84%E5%88%92.resources/E36E63C0-E94E-4646-856E-81FB14C37F03.png" height="116" width="504"/><img src="%E5%8A%A8%E6%80%81%E8%A7%84%E5%88%92.resources/FBE60E64-0CC9-4E33-A4A3-9F1EAF122D8A.png" height="106" width="618"/><br/></span></div><div><span style="font-size: 12px;"><img src="%E5%8A%A8%E6%80%81%E8%A7%84%E5%88%92.resources/78CBE06F-B318-43F6-B181-9E96AF5C15A9.png" height="112" width="438"/><br/></span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">def fib(self, n):</font></div><div><font style="font-size: 12px;">    MOD = 10 ** 9 + 7</font></div><div><font style="font-size: 12px;">    if n &lt; 2:</font></div><div><font style="font-size: 12px;">        return n</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">    def multiply(a, b):  # 矩阵A和矩阵B相乘</font></div><div><font style="font-size: 12px;">        c = [[0, 0], [0, 0]]</font></div><div><font style="font-size: 12px;">        for i in range(2):</font></div><div><font style="font-size: 12px;">            for j in range(2):</font></div><div><font style="font-size: 12px;">                c[i][j] = (a[i][0] * b[0][j] + a[i][1] * b[1][j]) % MOD</font></div><div><font style="font-size: 12px;">        return c</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">    def matrix_pow(a, n):  # n：数列第n项</font></div><div><font style="font-size: 12px;">        ret = [[1, 0], [0, 1]]  # 矩阵的初始化</font></div><div><font style="font-size: 12px;">        while n &gt; 0:</font></div><div><font style="font-size: 12px;">            if n % 2 == 1:  # 是奇数，就先乘一下当前a</font></div><div><font style="font-size: 12px;">                ret = multiply(ret, a)</font></div><div><font style="font-size: 12px;">            n = n // 2  # 整除2，向下取整</font></div><div><font style="font-size: 12px;">            a = multiply(a, a)</font></div><div><font style="font-size: 12px;">        return ret # 这里的ret是中间的n-1个矩阵相乘，最后还要和[f1,f0相乘]，但是它俩的值是[1,0]，所以直接取ret[0][0]就是fn</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">    res = matrix_pow([[1, 1], [1, 0]], n-1)</font></div><div><font style="font-size: 12px;">    return res[0][0]</font></div></div><div><span style="font-size: 12px; color: rgb(255, 0, 0); font-family: &quot;Courier New&quot;;">2.使用最小花费爬楼梯</span></div><div><font style="font-size: 12px;"><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">给你一个整数数组 cost ，其中 cost[i] 是从楼梯第 i 个台阶向上爬需要支付的费用。一旦你支付此费用，即可选择向上爬一个或者两个台阶。你可以选择从下标为 0 或下标为 1 的台阶开始爬楼梯。</span><span style="font-size: 12px; color: unset; font-family: &quot;Courier New&quot;;">请你计算并返回达到楼梯顶部的最低花费。</span></font></div><div><span style="font-size: 12px;"><img src="%E5%8A%A8%E6%80%81%E8%A7%84%E5%88%92.resources/E6CB5757-3B31-4865-B056-FFD397EB00E5.png" height="213" width="342"/><br/></span></div><div><span style="font-size: 12px; color: unset; font-family: &quot;Courier New&quot;;">思路：就和普通的爬楼梯一样，不用考虑方法种数，考虑花费即可。</span></div><div><span style="font-size: 12px; color: unset; font-family: &quot;Courier New&quot;;">注意：楼梯顶部的下标为n，从0开始，爬上第0和第1个台阶，不需要花钱，从0或者1开始爬，才需要花钱。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">def minCostClimbingStairs(self, cost: List[int]) -&gt; int:</font></div><div><font style="font-size: 12px;">    n = len(cost)</font></div><div><font style="font-size: 12px;">    dp = [0] * (n + 1)  # dp[i]：到达下标i的最小花费</font></div><div><font style="font-size: 12px;">    for i in range(2, n + 1):</font></div><div><font style="font-size: 12px;">        dp[i] = min(dp[i - 1] + cost[i - 1], dp[i - 2] + cost[i - 2])</font></div><div><font style="font-size: 12px;">    return dp[n]</font></div></div><div><span style="font-size: 12px;">滚动数组：</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">def minCostClimbingStairs(self, cost: List[int]) -&gt; int:</font></div><div><font style="font-size: 12px;">    n = len(cost)</font></div><div><font style="font-size: 12px;">    dp_i_1 = 0</font></div><div><font style="font-size: 12px;">    dp_i_2 = 0</font></div><div><font style="font-size: 12px;">    for i in range(2, n + 1):</font></div><div><font style="font-size: 12px;">        dp_i = min(dp_i_1 + cost[i - 1], dp_i_2 + cost[i - 2])</font></div><div><font style="font-size: 12px;">        dp_i_2 = dp_i_1</font></div><div><font style="font-size: 12px;">        dp_i_1 = dp_i</font></div><div><font style="font-size: 12px;">    return dp_i_1</font></div></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">3.不同路径</span></div><div><a href="https://leetcode.cn/problems/unique-paths/" style="font-size: 12px; font-family: &quot;Courier New&quot;;">62. 不同路径 - 力扣（LeetCode）</a></div><div><font style="font-size: 12px;"><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">一个机器人位于一个 m x n 网格的左上角，</span><span style="font-size: 12px; color: unset; font-family: &quot;Courier New&quot;;">机器人每次只能向下或者向右移动一步。机器人试图达到网格的右下角，</span><span style="font-size: 12px; color: unset; font-family: &quot;Courier New&quot;;">问总共有多少条不同的路径？</span></font></div><div><span style="font-size: 12px; color: unset; font-family: &quot;Courier New&quot;;">思路：1.先给最上面的边，和最左边的边，全部赋值为1。  2.dp[i][j]只能由dp[i][j-1]和dp[i-1][j]到达</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def uniquePaths(self, m, n):</font></div><div><font style="font-size: 12px;">        dp = [[0]*n for _ in range(m)]</font></div><div><font style="font-size: 12px;">        for i in range(m):</font></div><div><font style="font-size: 12px;">            dp[i][0] = 1</font></div><div><font style="font-size: 12px;">        for i in range(n):</font></div><div><font style="font-size: 12px;">            dp[0][i] = 1</font></div><div><font style="font-size: 12px;">        for i in range(1, m):</font></div><div><font style="font-size: 12px;">            for j in range(1, n):</font></div><div><font style="font-size: 12px;">                dp[i][j] = dp[i-1][j] + dp[i][j-1]</font></div><div><font style="font-size: 12px;">        return dp[-1][-1]</font></div></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">4.不同路径2</span></div><div><a href="https://leetcode.cn/problems/unique-paths-ii/" style="font-size: 12px; font-family: &quot;Courier New&quot;;">63. 不同路径 II - 力扣（LeetCode）</a></div><div><font style="font-size: 12px;"><span style="font-size: 12px; color: unset; font-family: &quot;Courier New&quot;;">一个机器人位于一个 m x n 网格的左上角。</span><span style="font-size: 12px; color: unset; font-family: &quot;Courier New&quot;;">机器人每次只能向下或者向右移动一步。机器人试图达到网格的右下角。</span><span style="font-size: 12px; color: unset; font-family: &quot;Courier New&quot;;">现在考虑网格中有障碍物。那么从左上角到右下角将会有多少条不同的路径？</span><span style="font-size: 12px; color: unset; font-family: &quot;Courier New&quot;;">网格中的障碍物和空位置分别用1和0来表示。</span></font></div><div><span style="font-size: 12px;"><img src="%E5%8A%A8%E6%80%81%E8%A7%84%E5%88%92.resources/27BE149A-A778-4F8E-BFC1-E769DD68FE2D.png" height="135" width="354"/><br/></span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">思路：1.先给最上面的边和最左边的边赋值为1，如果碰到障碍，就停止。 2.和上一题一样，只不过在给dp[i][j]赋值时，判断一下这个地方有没有障碍即可。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def uniquePathsWithObstacles(self, obstacleGrid):</font></div><div><font style="font-size: 12px;">        m = len(obstacleGrid)</font></div><div><font style="font-size: 12px;">        n = len(obstacleGrid[0])</font></div><div><font style="font-size: 12px;">        dp = [[0]*n for _ in range(m)]</font></div><div><font style="font-size: 12px;">        for i in range(m):</font></div><div><font style="font-size: 12px;">            if obstacleGrid[i][0] == 1:</font></div><div><font style="font-size: 12px;">                break                 # 注意这里要break，因为走不下去了</font></div><div><font style="font-size: 12px;">            dp[i][0] = 1</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">        for i in range(n):</font></div><div><font style="font-size: 12px;">            if obstacleGrid[0][i] == 1:</font></div><div><font style="font-size: 12px;">                break</font></div><div><font style="font-size: 12px;">            dp[0][i] = 1</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">        for i in range(1, m):</font></div><div><font style="font-size: 12px;">            for j in range(1, n):</font></div><div><font style="font-size: 12px;">                if obstacleGrid[i][j] == 0:       # 这步容易忘记，注意！！！</font></div><div><font style="font-size: 12px;">                    dp[i][j] = dp[i-1][j] + dp[i][j-1]</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">        return dp[-1][-1]</font></div></div><div><span style="font-size: 12px;">地下城游戏（反着DP）</span></div><div><span style="font-size: 12px;">从[0,0]开始走，每一步的血量都不能小于0（最少为1）。问，想走到终点的话，最低起始血量是多少？</span></div><div><span style="font-size: 12px;"><img src="%E5%8A%A8%E6%80%81%E8%A7%84%E5%88%92.resources/ED6CD094-3819-4A79-828A-D073E11AFD15.png" height="516" width="514"/><br/></span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">def calculateMinimumHP(self, grid):</font></div><div><font style="font-size: 12px;">    m = len(grid)</font></div><div><font style="font-size: 12px;">    n = len(grid[0])</font></div><div><font style="font-size: 12px;">    dp = [[0] * n for _ in range(m)]  # 到达grid[i][j]所需的最小生命值</font></div><div><font style="font-size: 12px;">    dp[-1][-1] = max(1, 1 - grid[-1][-1])  # 正，生命值1进去也是安全的；负，必须以1-grid[-1][-1]的生命值进去才安全</font></div><div><font style="font-size: 12px;">    for i in range(n - 2, -1, -1):  # 初始化最后一行</font></div><div><font style="font-size: 12px;">        dp[-1][i] = max(1, dp[-1][i + 1] - grid[-1][i])</font></div><div><font style="font-size: 12px;">    for i in range(m - 2, -1, -1):  # 初始化最后一列</font></div><div><font style="font-size: 12px;">        dp[i][-1] = max(1, dp[i + 1][-1] - grid[i][-1])</font></div><div><font style="font-size: 12px;">    for i in range(m - 2, -1, -1):</font></div><div><font style="font-size: 12px;">        for j in range(n - 2, -1, -1):</font></div><div><font style="font-size: 12px;">            dp[i][j] = max(min(dp[i + 1][j], dp[i][j + 1]) - grid[i][j], 1)</font></div><div><font style="font-size: 12px;">    return dp[0][0]</font></div></div><div><span style="font-size: 12px;">三角形最小路径和</span></div><div><span style="font-size: 12px;"><img src="%E5%8A%A8%E6%80%81%E8%A7%84%E5%88%92.resources/32E93E7B-7162-4040-AAEA-2F26D581B643.png" height="804" width="808"/><br/></span></div><div><span style="font-size: 12px;">每一步只能移动到下一行的相邻节点上，相邻节点指下行种下标与之相同或下标加一的两个节点。</span></div><div><span style="font-size: 9pt;">给定一个三角形 triangle ，找出自顶向下的最小路径和。</span></div><div><span style="font-size: 9pt;">每一步只能移动到下一行中相邻的结点上。相邻的结点 在这里指的是 下标 与 上一层结点下标 相同或者等于 上一层结点下标 + 1 的两个结点。也就是说，如果正位于当前行的下标 i ，那么下一步可以移动到下一行的下标 i 或 i + 1。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">def minTrace(self, triangle: List[List[int]]) -&gt; int:</font></div><div><font style="font-size: 12px;">    n = len(triangle)</font></div><div><font style="font-size: 12px;">    dp = [[0] * n for _ in range(n)]</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">    # 动态规划初始化，三角形左右边界值和顶点值</font></div><div><font style="font-size: 12px;">    dp[0][0] = triangle[0][0]</font></div><div><font style="font-size: 12px;">    for i in range(1, n):</font></div><div><font style="font-size: 12px;">        dp[i][0] = dp[i - 1][0] + triangle[i][0] # </font>每一行的第一个数只能是该数的值加上上一行的第一个dp[i-1][0]</div><div><font style="font-size: 12px;">        dp[i][i] = dp[i - 1][i - 1] + triangle[i][i] # </font>每一行的最后一个数只能是该数的值加上上一行的最后一个dp[i-1][i-1]</div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">    # 三角形中间值</font></div><div><font style="font-size: 12px;">    for i in range(1, n):</font></div><div><font style="font-size: 12px;">        for j in range(1, i):</font></div><div><font style="font-size: 12px;">            dp[i][j] = min(dp[i - 1][j - 1], dp[i - 1][j]) + triangle[i][j]</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">    return min(dp[n - 1]) # 最后一行的最小值</font></div></div><div><span style="font-size: 12px;">交错字符串：</span></div><div><span style="font-size: 12px;">判断s3是否能由s1、s2交错组成。</span></div><div><span style="font-size: 12px;"><img src="%E5%8A%A8%E6%80%81%E8%A7%84%E5%88%92.resources/2A15E8EA-BC07-4287-9388-5FE6C49CA081.png" height="382" width="1114"/><br/></span></div><div><span style="font-size: 12px; color: rgb(255, 38, 0);">dp[i][j]：s1的前i个字符和s2的前j个字符能否组成s3的前i+j个字符。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">class Solution:</font></div><div><font style="font-size: 12px;">    def isInterleave(self, s1: str, s2: str, s3: str) -&gt; bool:</font></div><div><font style="font-size: 12px;">        len1=len(s1)</font></div><div><font style="font-size: 12px;">        len2=len(s2)</font></div><div><font style="font-size: 12px;">        len3=len(s3)</font></div><div><font style="font-size: 12px;">        if(len1+len2!=len3):</font></div><div><font style="font-size: 12px;">            return False</font></div><div><font style="font-size: 12px;">        dp=[[False]*(len2+1) for i in range(len1+1)]</font></div><div><font style="font-size: 12px;">        dp[0][0]=True</font></div><div><font style="font-size: 12px;">        for i in range(1,len1+1):</font></div><div><font style="font-size: 12px;">            dp[i][0]=(dp[i-1][0] and s1[i-1]==s3[i-1])</font></div><div><font style="font-size: 12px;">        for i in range(1,len2+1):</font></div><div><font style="font-size: 12px;">            dp[0][i]=(dp[0][i-1] and s2[i-1]==s3[i-1])</font></div><div><font style="font-size: 12px;">        for i in range(1,len1+1):</font></div><div><font style="font-size: 12px;">            for j in range(1,len2+1):</font></div><div><font style="font-size: 12px;">                dp[i][j]=(dp[i][j-1] and s2[j-1]==s3[i+j-1]) or (dp[i-1][j] and s1[i-1]==s3[i+j-1])</font></div><div><font style="font-size: 12px;">        return dp[-1][-1]</font></div></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">5.整数拆分</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">给定一个正整数 n ，将其拆分为 k 个 正整数 的和（ k &gt;= 2 ），并使这些整数的乘积最大化。</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">返回你可以获得的最大乘积 。</span></div><div style="letter-spacing: normal; orphans: 2; white-space: pre-wrap; widows: 2; word-spacing: 0px;"><span style="box-sizing: border-box; font-size: 12px; color: rgba(var(--grey-9-rgb),1); font-family: &quot;Courier New&quot;; font-variant-caps: normal; font-variant-ligatures: normal; font-weight: bold; line-height: 1.6;">输入:</span> <span style="font-size: 12px; color: rgba(var(--grey-9-rgb),1); font-family: &quot;Courier New&quot;; font-variant-caps: normal; font-variant-ligatures: normal; line-height: 1.6;">n = 10</span></div><div style="letter-spacing: normal; orphans: 2; white-space: pre-wrap; widows: 2; word-spacing: 0px;"><span style="box-sizing: border-box; font-size: 12px; color: rgba(var(--grey-9-rgb),1); font-family: &quot;Courier New&quot;; font-variant-caps: normal; font-variant-ligatures: normal; font-weight: bold; line-height: 1.6;">输出:</span> <span style="font-size: 12px; color: rgba(var(--grey-9-rgb),1); font-family: &quot;Courier New&quot;; font-variant-caps: normal; font-variant-ligatures: normal; line-height: 1.6;">36</span></div><div style="letter-spacing: normal; orphans: 2; white-space: pre-wrap; widows: 2; word-spacing: 0px;"><span style="box-sizing: border-box; font-size: 12px; color: rgba(var(--grey-9-rgb),1); font-family: &quot;Courier New&quot;; font-variant-caps: normal; font-variant-ligatures: normal; font-weight: bold; line-height: 1.6;">解释:</span> <span style="font-size: 12px; color: rgba(var(--grey-9-rgb),1); font-family: &quot;Courier New&quot;; font-variant-caps: normal; font-variant-ligatures: normal; line-height: 1.6;">10 = 3 + 3 + 4, 3 × 3 × 4 = 36。</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">思路：分两种情况：1.不拆分：j*(i-j) 2.拆分其中一个数（包含了所有的拆分情况）:j*dp[i-j]。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def integerBreak(self, n):</font></div><div><font style="font-size: 12px;">        dp = [0]*(n+1)</font></div><div><font style="font-size: 12px;">        for i in range(2, n+1):  # 从2开始，1和0无法切分</font></div><div><font style="font-size: 12px;">            for j in range(1, i):   </font></div><div><font style="font-size: 12px;">                dp[i] = max(dp[i], j*(i-j), j*dp[i-j]) # 两种情况，切分或者不切分</font></div><div><font style="font-size: 12px;">        return dp[-1]</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px; font-weight: bold;">6.剪绳子</span></div><div><span style="font-size: 12px;">给你一根长度为 n 的绳子，请把绳子剪成整数长的 m 段（ m 、 n 都是整数， n &gt; 1 并且 m &gt; 1 ， m &lt;= n ），每段绳子的长度记为 k[1],...,k[m] 。请问 k[1]*k[2]*...*k[m] 可能的最大乘积是多少？例如，当绳子的长度是 8 时，我们把它剪成长度分别为 2、3、3 的三段，此时得到的最大乘积是 18 。</span></div><div><span style="font-size: 12px;">和整数拆分一样的代码</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def cutRope(n):</font></div><div><font style="font-size: 12px;">        dp = [0] * (n + 1)</font></div><div><font style="font-size: 12px;">        for i in range(2, n + 1):</font></div><div><font style="font-size: 12px;">            for j in range(i):</font></div><div><font style="font-size: 12px;">                dp[i] = max(dp[i], j * (i - j), j * dp[i - j])  # 两种情况，切分或者不切分</font></div><div><font style="font-size: 12px;">        return dp[n]</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px; color: rgb(4, 51, 255); font-family: &quot;Courier New&quot;;">7.01背包问题</span></div><div><span style="font-size: 12px; color: rgb(4, 51, 255); font-family: &quot;Courier New&quot;; font-weight: bold;">注意：用二维数组解决背包问题：两个for循环要遍历满，在第二个循环里加if。用一维数组遍历问题：第二个循环不是遍历满的，从空间&gt;=物品[i]开始遍历。这是因为，dp[i][j]是由dp[i-1][]推出来的，所以必须给dp[i-1]赋值。</span></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">例一：普通的01背包</span></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;; font-weight: bold;">二维数组的01背包：</span></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">1.单独考虑第一个物品</span></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">2.两个for循环， + if</span></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;; font-weight: bold;">滚动数组的01背包：</span></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">1.两个for循环，第二个for循环倒叙，改变for循环范围-&gt;无需+if</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font face="Courier New" style="font-size: 12px;">weight = [1, 3, 4]</font></div><div><font face="Courier New" style="font-size: 12px;">value = [15, 20, 30]</font></div><div><font face="Courier New" style="font-size: 12px;">def test(bag_size, weight, value):</font></div><div><font face="Courier New" style="font-size: 12px;">    dp = [[0 for _ in range(bag_size+1)] for _ in range(len(weight))]  # 注意，定义数组时，容量0也要考虑，所以要+1</font></div><div><font face="Courier New" style="font-size: 12px;">    # 1.单独考虑第一个物品</font></div><div><font face="Courier New" style="font-size: 12px;">    for i in range(1, bag_size+1): # 容量=0，什么物品都放不下，所以从1开始</font></div><div><font face="Courier New" style="font-size: 12px;">        if weight[0] &lt;= i:</font></div><div><font face="Courier New" style="font-size: 12px;">            dp[0][i] = value[0]</font></div><div><font face="Courier New" style="font-size: 12px;">    # 2.两个for循环+if，先物品，再空间</font></div><div><font face="Courier New" style="font-size: 12px;">    for i in range(1, len(weight)):   # 先遍历物品</font></div><div><font face="Courier New" style="font-size: 12px;">        for j in range(1, bag_size+1):   # 再遍历背包空间,正序和倒序都可以</font></div><div><font face="Courier New" style="font-size: 12px;">            if j &gt;= weight[i]:</font></div><div><font style="font-size: 12px;"><font face="Courier New">                dp[i][j] = max(dp[i-1][j], dp[i-1][j-weight[i]] + value[i]) </font> # dp[i][j]表示从下标为[0-i]的物品里任意取，放进容量为j的背包，价值总和最大是多少</font></div><div><font face="Courier New" style="font-size: 12px;">            else:</font></div><div><font face="Courier New" style="font-size: 12px;">                dp[i][j] = dp[i-1][j]</font></div><div><font face="Courier New" style="font-size: 12px;">    return dp[-1][-1]</font></div><div><font face="Courier New" style="font-size: 12px;"><br/></font></div><div><font face="Courier New" style="font-size: 12px;">print(test(4, weight, value))</font></div></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">例二：普通的01背包，用一维数组解决</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font face="Courier New" style="font-size: 12px;">def bag_problem():</font></div><div><font face="Courier New" style="font-size: 12px;">    weight = [1, 3, 4]</font></div><div><font face="Courier New" style="font-size: 12px;">    value = [15, 20, 30]</font></div><div><font face="Courier New" style="font-size: 12px;">    bag_weight = 4</font></div><div><font face="Courier New" style="font-size: 12px;">    # 初始化: 全为0</font></div><div><font face="Courier New" style="font-size: 12px;">    dp = [0] * (bag_weight + 1)</font></div><div><font face="Courier New" style="font-size: 12px;">    # 先遍历物品重量, 再遍历背包容量。背包容量要逆序</font></div><div><font face="Courier New" style="font-size: 12px;">    for i in range(len(weight)):</font></div><div><font face="Courier New" style="font-size: 12px;">        for j in range(bag_weight, weight[i] - 1, -1):</font></div><div><font face="Courier New" style="font-size: 12px;">            # 递归公式</font></div><div><font face="Courier New" style="font-size: 12px;">            dp[j] = max(dp[j], dp[j - weight[i]] + value[i])</font></div><div><font face="Courier New" style="font-size: 12px;">    Return dp[-1]</font></div></div><div><span style="font-size: 12px;">牛客的01背包</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">def knapsack(self, V: int, n: int, vw: List[List[int]]) -&gt; int:</font></div><div><font style="font-size: 12px;">    dp = [0 for j in range(V + 1)]</font></div><div><font style="font-size: 12px;">    for i in range(1, n + 1):</font></div><div><font style="font-size: 12px;">        for j in range(V, 0, -1):</font></div><div><font style="font-size: 12px;">            if j &gt;= vw[i - 1][0]:</font></div><div><font style="font-size: 12px;">                dp[j] = max(dp[j], vw[i - 1][1] + dp[j - vw[i - 1][0]])</font></div><div><font style="font-size: 12px;">    return dp[-1]</font></div></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">dp[j]：容量为j的背包，最大价值是多少呢？</span></div><div><font style="font-size: 12px;"><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">注意点1：遍历背包容量时，</span><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">为了保证每个物品只被放入一次</span><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">，要倒序遍历。</span></font></div><div><span style="font-size: 12px;"><span style="font-size: 12px; font-family: &quot;Courier New&quot;; font-weight: bold;">原因：因为dp[i][j]是由dp[i-1][j-v[i]]推出来的。变为一维数组后，如果顺序遍历背包空间，就会导致dp[i][j]是由dp[i][j-v[i]]推出来的，而不是dp[i-1][j-v[i]]推的。二维数组正序倒序都可以的原因是，用第一个维度严格区分了i和i-1时的状态。</span></span></div><div><a href="https://www.cnblogs.com/qie-wei/p/10160169.html" style="font-size: 12px; font-family: &quot;Courier New&quot;;">https://www.cnblogs.com/qie-wei/p/10160169.html</a></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">8.分割等和子集</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">给你一个只包含正整数的非空数组 nums 。请你判断是否可以将这个数组分割成两个子集，使得两个子集的元素和相等。</span></div><div><span style="font-size: 12px;"><img src="%E5%8A%A8%E6%80%81%E8%A7%84%E5%88%92.resources/A6275B3F-7AAF-4E51-9252-CAB5DA757B1E.png" height="117" width="337"/><br/></span></div><div><span style="font-size: 12px; color: rgb(51, 51, 51); font-family: &quot;Courier New&quot;;">思路：此时，背包的weight和value是相等的。</span></div><div><span style="font-size: 12px; color: rgb(51, 51, 51);">一维滚动数组：</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def canPartition(nums):</font></div><div><font style="font-size: 12px;">        if sum(nums)%2 == 1:</font></div><div><font style="font-size: 12px;">            return False</font></div><div><font style="font-size: 12px;">        rongl = sum(nums)//2</font></div><div><font style="font-size: 12px;">        dp = [0]*(rongl+1)</font></div><div><font style="font-size: 12px;">        for i in range(len(nums)):</font></div><div><font style="font-size: 12px;">            for j in range(rongl, nums[i]-1, -1):  # 注意是逆序</font></div><div><font style="font-size: 12px;">                dp[j] = max(dp[j], dp[j-nums[i]] + nums[i])</font></div><div><font style="font-size: 12px;">        </font></div><div><font style="font-size: 12px;">        return rongl == dp[rongl]</font></div></div><div><span style="font-size: 12px;">二维数组：</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">def canPartition(self, nums):</font></div><div><font style="font-size: 12px;">    if sum(nums) % 2 == 1:</font></div><div><font style="font-size: 12px;">        return False</font></div><div><font style="font-size: 12px;">    bag_size = sum(nums) // 2</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">    dp = [[0 for _ in range(bag_size + 1)] for _ in range(len(nums))]  # 注意，定义数组时，容量0也要考虑，所以要+1</font></div><div><font style="font-size: 12px;">    # 1.单独考虑第一个物品</font></div><div><font style="font-size: 12px;">    for i in range(1, bag_size + 1):  # 容量=0，什么物品都放不下，所以从1开始</font></div><div><font style="font-size: 12px;">        if nums[0] &lt;= i:</font></div><div><font style="font-size: 12px;">            dp[0][i] = nums[0]</font></div><div><font style="font-size: 12px;">    # 2.两个for循环+if，先物品，再空间</font></div><div><font style="font-size: 12px;">    for i in range(1, len(nums)):  # 先遍历物品</font></div><div><font style="font-size: 12px;">        for j in range(1, bag_size + 1):  # 再遍历背包空间,正序和倒序都可以</font></div><div><font style="font-size: 12px;">            if j &gt;= nums[i]:</font></div><div><font style="font-size: 12px;">                dp[i][j] = max(dp[i - 1][j],</font></div><div><font style="font-size: 12px;">                               dp[i - 1][j - nums[i]] + nums[i])  # dp[i][j]表示从下标为[0-i]的物品里任意取，放进容量为j的背包，价值总和最大是多少</font></div><div><font style="font-size: 12px;">            else:</font></div><div><font style="font-size: 12px;">                dp[i][j] = dp[i - 1][j]</font></div><div><font style="font-size: 12px;">    return dp[-1][-1] == bag_size</font></div></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">二维数组，两部分具体是啥</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">def canPartition(nums):</font></div><div><font style="font-size: 12px;">    if sum(nums) % 2 == 1:</font></div><div><font style="font-size: 12px;">        return False</font></div><div><font style="font-size: 12px;">    bag_size = sum(nums) // 2</font></div><div><font style="font-size: 12px;">    dp = [[0 for _ in range(bag_size + 1)] for _ in range(len(nums))]  # 注意，定义数组时，容量0也要考虑，所以要+1</font></div><div><font style="font-size: 12px;">    path = [[0 for _ in range(bag_size + 1)] for _ in range(len(nums))] # 考虑前i个物品，当背包空间=j时，能被装满，就赋值为1</font></div><div><font style="font-size: 12px;">    # 1.单独考虑第一个物品</font></div><div><font style="font-size: 12px;">    for i in range(1, bag_size + 1):  # 容量=0，什么物品都放不下，所以从1开始</font></div><div><font style="font-size: 12px;">        if nums[0] &lt;= i:</font></div><div><font style="font-size: 12px;">            dp[0][i] = nums[0]</font></div><div><font style="font-size: 12px;">    # 2.两个for循环+if，先物品，再空间</font></div><div><font style="font-size: 12px;">    for i in range(1, len(nums)):  # 先遍历物品</font></div><div><font style="font-size: 12px;">        for j in range(1, bag_size + 1):  # 再遍历背包空间,正序和倒序都可以</font></div><div><font style="font-size: 12px;">            if j &gt;= nums[i]:</font></div><div><font style="font-size: 12px;">                dp[i][j] = max(dp[i - 1][j], dp[i - 1][j - nums[i]] + nums[i])  # dp[i][j]表示从下标为[0-i]的物品里任意取，放进容量为j的背包，价值总和最大是多少</font></div><div><font style="font-size: 12px;">            else:</font></div><div><font style="font-size: 12px;">                dp[i][j] = dp[i - 1][j]</font></div><div><font style="font-size: 12px;">            if dp[i][j] == j: # 考虑前i个物品，当背包空间=j时，能被装满</font></div><div><font style="font-size: 12px;">                path[i][j] = 1</font></div><div><font style="font-size: 12px;">    i = len(nums)-1</font></div><div><font style="font-size: 12px;">    j = bag_size</font></div><div><font style="font-size: 12px;">    while i&gt;0 and j&gt;0:</font></div><div><font style="font-size: 12px;">        if path[i][j]==1: # 背包空间为j的时候能被装满</font></div><div><font style="font-size: 12px;">            print(nums[i-1]) #</font></div><div><font style="font-size: 12px;">            j -= nums[i-1] # 再考察背包空间为j-nums[i-1]的时候，能不能被装满</font></div><div><font style="font-size: 12px;">        i -= 1</font></div><div><font style="font-size: 12px;">    return dp[-1][-1] == bag_size</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">9.最后一块石头的重量</span></div><div><span style="font-size: 12px;"><img src="%E5%8A%A8%E6%80%81%E8%A7%84%E5%88%92.resources/3ADE32A5-69A7-4B77-9D15-5261051DC0E9.png" height="772" width="1300"/><br/></span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">思路：直接套背包思想</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def lastStoneWeightII(self, stones):</font></div><div><font style="font-size: 12px;">        rongl = sum(stones)//2</font></div><div><font style="font-size: 12px;">        dp = [0]*(rongl + 1)    # 注意这里的+1，这样才能计算dp[rongl]</font></div><div><font style="font-size: 12px;">        for i in range(len(stones)):</font></div><div><font style="font-size: 12px;">            for j in range(rongl, stones[i]-1, -1): # 注意这里的-1</font></div><div><font style="font-size: 12px;">                dp[j] = max(dp[j], dp[j-stones[i]] + stones[i])</font></div><div><font style="font-size: 12px;">        </font></div><div><font style="font-size: 12px;">        return sum(stones)-2*dp[-1]</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">10.目标和 （求有几种组合问题）</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">给你一个非负整数数组 nums 和一个整数 target。</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">向数组中的每个整数前添加 '+' 或 '-' ，然后串联起所有整数，可以构造一个 表达式 ：</span></div><div><font style="font-size: 12px;"><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">例如，nums = [2, 1] ，可以在 2 之前添加 '+' ，在 1 之前添加 '-' ，然后串联起来得到表达式 "+2-1" 。</span><span style="font-size: 12px; color: unset; font-family: &quot;Courier New&quot;;">返回可以通过上述方法构造的、运算结果等于 target 的不同 表达式 的数目。</span></font></div><div><span style="font-size: 12px;"><img src="%E5%8A%A8%E6%80%81%E8%A7%84%E5%88%92.resources/C9CCAE29-355D-4E19-BF64-66155587A791.png" height="422" width="614"/><br/></span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">思路：正数和-负数和=target， x-(sum-x)=target，x=(sum+target)//2，转换为背包问题。</span></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">注意：求有几种方式，dp[j]=其他dp数组的和（这个和上楼梯的思想一样），如果是装满背包，就是dp[j]=max()。</span></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">本题还要注意边界条件，S+sum必须是偶数，才有解。改变num中的一个元素的符号，sum的变化肯定是偶数。所以sum是奇数的话，只能应对奇数的target，偶数的sum只能应对偶数的target。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def findTargetSumWays(self, nums, target):</font></div><div><font style="font-size: 12px;">        if (target+sum(nums))%2==1:      # 根据推导，这里必须为偶数</font></div><div><font style="font-size: 12px;">            return 0</font></div><div><font style="font-size: 12px;">        if abs(target)&gt;sum(nums):        # 如果target的绝对值&gt;sum(nums)，无论符号是什么都无法满足</font></div><div><font style="font-size: 12px;">            return 0</font></div><div><font style="font-size: 12px;">        sum_z = (target+sum(nums))//2</font></div><div><font style="font-size: 12px;">        bag_weight = sum_z</font></div><div><font style="font-size: 12px;">        dp = [0]*(bag_weight+1)</font></div><div><font style="font-size: 12px;">        dp[0] = 1                 # 求组合数，必须定义dp[0]=1</font></div><div><font style="font-size: 12px;">        for i in range(len(nums)):</font></div><div><font style="font-size: 12px;">            for j in range(bag_weight, nums[i]-1, -1):</font></div><div><font style="font-size: 12px;">                dp[j] = dp[j] + dp[j-nums[i]]             # 求有多少组合的固定公式： dp[j] = dp[j] + dp[j-nums[i]]</font></div><div><font style="font-size: 12px;">        return dp[-1]</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">11.一和零（一组物品，两个背包，忽略）</span></div><div><font style="font-size: 12px;"><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">给你一个二进制字符串数组 strs 和两个整数 m 和 n 。</span><span style="font-size: 12px; color: unset; font-family: &quot;Courier New&quot;;">请你找出并返回 strs 的最大子集的长度，该子集中 最多 有 m 个 0 和 n 个 1 。</span><span style="font-size: 12px; color: unset; font-family: &quot;Courier New&quot;;">如果 x 的所有元素也是 y 的元素，集合 x 是集合 y 的 子集 。</span></font></div><div><span style="font-size: 12px;"><img src="%E5%8A%A8%E6%80%81%E8%A7%84%E5%88%92.resources/65B22286-226F-44D3-A116-4CF53801046D.png" height="296" width="1230"/><br/></span></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">二维滚动数组：</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font face="Courier New" style="font-size: 12px;">def findMaxForm(strs, m, n):</font></div><div><font face="Courier New" style="font-size: 12px;">    zero_nums = []</font></div><div><font face="Courier New" style="font-size: 12px;">    one_nums = []</font></div><div><font face="Courier New" style="font-size: 12px;">    for i in range(len(strs)): # 1.统计每个字符串0和1的出现次数</font></div><div><font face="Courier New" style="font-size: 12px;">        zero_nums.append(strs[i].count('0'))</font></div><div><font face="Courier New" style="font-size: 12px;">        one_nums.append(strs[i].count('1'))</font></div><div><font face="Courier New" style="font-size: 12px;"><br/></font></div><div><font face="Courier New" style="font-size: 12px;">    dp = [[0 for _ in range(n + 1)] for _ in range(m + 1)]  # 背包0容量为j，背包1容量为k的情况下，最大子集数</font></div><div><font face="Courier New" style="font-size: 12px;">    for i in range(len(strs)):       # 遍历物品</font></div><div><font face="Courier New" style="font-size: 12px;">        for j in range(m, zero_nums[i]-1, -1):  # 遍历背包1</font></div><div><font face="Courier New" style="font-size: 12px;">            for k in range(n, one_nums[i]-1, -1):   # 遍历背包2</font></div><div><font face="Courier New" style="font-size: 12px;">                dp[j][k] = max(dp[j][k], dp[j - zero_nums[i]][k - one_nums[i]] + 1)</font></div><div><font face="Courier New" style="font-size: 12px;">    return dp[m][n]</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;"><span style="caret-color: rgb(4, 51, 255);"><span style="font-size: 12px; color: rgb(255, 0, 0); font-family: &quot;Courier New&quot;;">12.</span></span><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">经典完全背包</span></font></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">先遍历物品，再遍历容量。 遍历背包时不用逆序了，因为物品可以重复使用。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font face="Courier New" style="font-size: 12px;">def test_complete_pack1():</font></div><div><font face="Courier New" style="font-size: 12px;">    weight = [3, 1, 4]</font></div><div><font face="Courier New" style="font-size: 12px;">    value = [20, 15, 30]</font></div><div><font face="Courier New" style="font-size: 12px;">    bag_weight = 4</font></div><div><font face="Courier New" style="font-size: 12px;">    dp = [0] * (bag_weight + 1)  </font></div><div><font face="Courier New" style="font-size: 12px;"><br/></font></div><div><font face="Courier New" style="font-size: 12px;">    for i in range(len(weight)):</font></div><div><font face="Courier New" style="font-size: 12px;">        for j in range(weight[i], bag_weight + 1):</font></div><div><font face="Courier New" style="font-size: 12px;">            dp[j] = max(dp[j], dp[j - weight[i]] + value[i])</font></div><div><font face="Courier New" style="font-size: 12px;">    return dp[bag_weight]</font></div></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">13.零钱兑换2（组合，组合和排列的区别是：[1,2]=[2,1]，算组合。[1,2]和[2,1]是两种不同的情况，算排列）</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">给你一个整数数组 coins 表示不同面额的硬币，另给一个整数 amount 表示总金额。</span></div><div><span style="font-size: 12px; color: unset; font-family: &quot;Courier New&quot;;">请你计算并返回可以凑成总金额的硬币组合数。如果任何硬币组合都无法凑出总金额，返回 0 。</span></div><div><span style="font-size: 12px; color: unset; font-family: &quot;Courier New&quot;;">假设每一种面额的硬币有无限个。</span></div><div><span style="font-size: 12px;"><img src="%E5%8A%A8%E6%80%81%E8%A7%84%E5%88%92.resources/F603A3EA-B484-4072-B178-0472F126411A.png" height="370" width="568"/><br/></span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">思路：套用完全背包的两个for循环。dp[0]赋值为1，dp[j]=dp[j]+dp[j-第i个物品的价值]，求有几种组合方式，都是这个方法。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def change(amount, coins):</font></div><div><font style="font-size: 12px;">        bag_weight = amount</font></div><div><font style="font-size: 12px;">        dp = [0]*(bag_weight+1)</font></div><div><font style="font-size: 12px;">        dp[0] = 1       # 所有数据的起源</font></div><div><font style="font-size: 12px;">        for i in range(len(coins)):       # 先物品</font></div><div><font style="font-size: 12px;">            for j in range(coins[i], bag_weight+1):      # 再背包</font></div><div><font style="font-size: 12px;">                dp[j] = dp[j] + dp[j-coins[i]]     # 求组合固定格式</font></div><div><font style="font-size: 12px;">        return dp[-1]</font></div></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">二维方法：</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">def change(self, amount, coins):</font></div><div><font style="font-size: 12px;">    n = len(coins)</font></div><div><font style="font-size: 12px;">    dp = [[0] * (amount + 1) for _ in range(n + 1)]</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">    for i in range(n + 1):</font></div><div><font style="font-size: 12px;">        dp[i][0] = 1</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">    for i in range(1, n + 1):</font></div><div><font style="font-size: 12px;">        for j in range(1, amount + 1):</font></div><div><font style="font-size: 12px;">            if j - coins[i - 1] &gt;= 0:</font></div><div><font style="font-size: 12px;">                dp[i][j] = dp[i - 1][j] + dp[i][j - coins[i - 1]]</font></div><div><font style="font-size: 12px;">            else:</font></div><div><font style="font-size: 12px;">                dp[i][j] = dp[i - 1][j]</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">    return dp[n][amount]</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">14.组合总和4（求排列）</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">给你一个由不同整数组成的数组 nums ，和一个目标整数 target 。请你从 nums 中找出并返回总和为 target 的元素组合的个数。</span></div><div><span style="font-size: 12px;"><img src="%E5%8A%A8%E6%80%81%E8%A7%84%E5%88%92.resources/9D717153-8828-4C0C-B23F-CFF93B90988C.png" height="586" width="728"/><br/></span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">思路：</span></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;; font-weight: bold;">如果求组合数就是外层for循环遍历物品，内层for遍历背包。（组合就是正常的）</span></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;; font-weight: bold;">如果求排列数就是外层for遍历背包，内层for循环遍历物品。</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">如果把遍历nums（物品）放在外循环，遍历target的作为内循环的话，举一个例子：计算dp[4]的时候，结果集只有 {1,3} 这样的集合，不会有{3,1}这样的集合，因为nums遍历放在外层，3只能出现在1后面！</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">所以本题遍历顺序最终遍历顺序：target（背包）放在外循环，将nums（物品）放在内循环，内循环从前到后遍历。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def combinationSum4(self, nums, target):</font></div><div><font style="font-size: 12px;">        dp = [0]*(target+1)</font></div><div><font style="font-size: 12px;">        dp[0] = 1</font></div><div><font style="font-size: 12px;">        for i in range(1, target+1): # dp[0]已经被赋值了</font></div><div><font style="font-size: 12px;">            for j in range(len(nums)):</font></div><div><font style="font-size: 12px;">                if i&gt;=nums[j]:    # 注意这里是大于等于</font></div><div><font style="font-size: 12px;">                    dp[i] = dp[i] + dp[i-nums[j]]</font></div><div><font style="font-size: 12px;">        return dp[-1]</font></div></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">15.零钱兑换1</span></div><div><font style="font-size: 12px;"><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">给你一个整数数组 coins ，表示不同面额的硬币；以及一个整数 amount ，表示总金额。</span><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">计算并返回可以凑成总金额所需的 最少的硬币个数 。如果没有任何一种硬币组合能组成总金额，返回 -1 。</span><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">你可以认为每种硬币的数量是无限的。</span></font></div><div><span style="font-size: 12px;"><img src="%E5%8A%A8%E6%80%81%E8%A7%84%E5%88%92.resources/73014DD2-A939-4508-A62F-4D7B21CF25A8.png" height="228" width="662"/><br/></span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">思路：</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font face="Courier New" style="font-size: 12px;">def coinChange(coins, amount):</font></div><div><font face="Courier New" style="font-size: 12px;">    dp = [1000] * (amount + 1) <b># 不能设为0，因为求的是最小个数</b></font></div><div><font face="Courier New" style="font-size: 12px;">    dp[0] = 0  <b><font color="#FF0000"># 这是所有数据的发源地</font></b></font></div><div><font face="Courier New" style="font-size: 12px;">    for i in range(len(coins)):</font></div><div><font face="Courier New" style="font-size: 12px;">        for j in range(coins[i], amount + 1):  # 完全背包不用从0开始，不用逆序</font></div><div><font style="font-family: &quot;Courier New&quot;; font-size: 12px;">            <b>dp[j] = min(dp[j], dp[j - coins[i]] + 1)</b>  # 凑足背包容量为j时，最少的硬币数</font></div><div><font face="Courier New" style="font-size: 12px;">    if dp[amount] == 1000: # 对应无解的情况amount=3，coins=[2]<b>,无解</b></font></div><div><font face="Courier New" style="font-size: 12px;">        return -1</font></div><div><font face="Courier New" style="font-size: 12px;">    return dp[amount]</font></div></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">16.完全平方数</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">是给你一个整数 n ，返回 和为 n 的完全平方数的最少数量 。</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">完全平方数 是一个整数，其值等于另一个整数的平方；换句话说，其值等于一个整数自乘的积。例如，1、4、9 和 16 都是完全平方数，而 3 和 11 不是。</span></div><div><span style="font-size: 12px;"><img src="%E5%8A%A8%E6%80%81%E8%A7%84%E5%88%92.resources/8BC5B728-2A8F-46DD-B05A-8C799C5DF637.png" height="228" width="692"/><br/></span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">思路：零钱兑换（最少的硬币）</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">def numSquares(self, n):</font></div><div><font style="font-size: 12px;">        tmp = []</font></div><div><font style="font-size: 12px;">        for i in range(n+1):</font></div><div><font style="font-size: 12px;">            tmp.append(i*i)</font></div><div><font style="font-size: 12px;">       </font></div><div><font style="font-size: 12px;">        bag_weight = n</font></div><div><font style="font-size: 12px;">        dp = [100]*(bag_weight+1)</font></div><div><font style="font-size: 12px;">        dp[0] = 0</font></div><div><font style="font-size: 12px;">        for i in range(len(tmp)):</font></div><div><font style="font-size: 12px;">            for j in range(tmp[i], bag_weight+1):</font></div><div><font style="font-size: 12px;">                dp[j] = min(dp[j], dp[j-tmp[i]]+1)</font></div><div><font style="font-size: 12px;">               </font></div><div><font style="font-size: 12px;">        return dp[-1]</font></div></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">17.单词拆分（一）</span></div><div><span style="font-size: 12px;">给你一个字符串 s 和一个字符串列表 wordDict 作为字典。请你判断是否可以利用字典中出现的单词拼接出s。</span><span style="font-size: 12px; font-family: unset;">注意：不要求字典中出现的单词全部都使用，并且字典中的单词可以重复使用。</span></div><div><span style="font-size: 12px;"><img src="%E5%8A%A8%E6%80%81%E8%A7%84%E5%88%92.resources/BDE0A9DE-6CBC-4C82-A708-173968F99945.png" height="212" width="954"/><br/></span></div><div><span style="font-size: 12px; color: rgb(4, 51, 255);">1.完全背包（因为单词可以重复使用），先背包再物品（因为是排列问题，不是组合问题）。</span></div><div><span style="font-size: 12px;"># 两种情况，1.dp[i]本身就是True（同一个i下，先第一个单词匹配成功，再判断第二个单词时，dp[i]就是True） 2.之前的dp[i-len(wordDict[j])]是True，新单词刚好和s中的对应位置匹配</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def wordBreak(self, s, wordDict):</font></div><div><font style="font-size: 12px;">        bag_weight = len(s)</font></div><div><font style="font-size: 12px;">        dp = [False]*(bag_weight+1)</font></div><div><font style="font-size: 12px;">        dp[0] = True              # 匹配0个字符，当然为True</font></div><div><font style="font-size: 12px;">        for i in range(1, bag_weight+1):</font></div><div><font style="font-size: 12px;">            for j in range(len(wordDict)):</font></div><div><font style="font-size: 12px;">                if i&gt;=len(wordDict[j]):</font></div><div><font style="font-size: 12px;">                    dp[i] = dp[i] or (dp[i-len(wordDict[j])] and wordDict[j]==s[i-len(wordDict[j]):i]) </font></div><div><font style="font-size: 12px;">        return dp[-1]</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px;">单词拆分（二）</span></div><div><span style="font-size: 12px;">给定一个字符串 s 和一个字符串数组 dic ，在字符串 s 的任意位置添加任意多个空格后得到的字符串集合是给定字符串数组 dic 的子集（即拆分后的字符串集合中的所有字符串都在 dic 数组中）</span></div><div><span style="font-size: 12px;">输入："nowcoder",["now","coder","no","wcoder"]</span></div><div><span style="font-size: 12px;">返回值：["no wcoder","now coder"]</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def wordDiv(s, wordDict):</font></div><div><font style="font-size: 12px;">        path = []</font></div><div><font style="font-size: 12px;">        result = []</font></div><div><font style="font-size: 12px;">        wordSet = set(wordDict)</font></div><div><font style="font-size: 12px;">        def backtracking(startindex):</font></div><div><font style="font-size: 12px;">            if startindex == len(s):</font></div><div><font style="font-size: 12px;">                result.append(' '.join(path))</font></div><div><font style="font-size: 12px;">                return</font></div><div><font style="font-size: 12px;">            for i in range(startindex, len(s)):</font></div><div><font style="font-size: 12px;">                word = s[startindex:i + 1]</font></div><div><font style="font-size: 12px;">                if word in wordSet:</font></div><div><font style="font-size: 12px;">                    path.append(word)</font></div><div><font style="font-size: 12px;">                    backtracking(i + 1)  # startindex前移到i+1</font></div><div><font style="font-size: 12px;">                    path.pop()</font></div><div><font style="font-size: 12px;">                else:</font></div><div><font style="font-size: 12px;">                    continue</font></div><div><font style="font-size: 12px;">        backtracking(0)</font></div><div><font style="font-size: 12px;">        return result</font></div></div><div><span style="font-size: 12px; color: rgb(4, 51, 255); font-family: &quot;Courier New&quot;;">18.打家劫舍</span></div><div><font style="font-size: 12px;"><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">例一</span><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">：相邻的房屋装有相互连通的防盗系统，如果两间相邻的房屋在同一晚上被小偷闯入，系统会自动报警。</span><span style="font-size: 12px; color: unset; font-family: &quot;Courier New&quot;;">给定一个代表每个房屋存放金额的非负整数数组，计算你不触动警报装置的情况下 ，一夜之内能够偷窃到的最高金额。</span></font></div><div><span style="font-size: 12px; color: unset; font-family: &quot;Courier New&quot;;">注意：定义dp[0]和dp[1]</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def rob(self, nums):</font></div><div><font style="font-size: 12px;">        dp = [0]*len(nums)</font></div><div><font style="font-size: 12px;">        if len(nums) == 1:</font></div><div><font style="font-size: 12px;">            return nums[0]</font></div><div><font style="font-size: 12px;">        dp[0] = nums[0]</font></div><div><font style="font-size: 12px;">        dp[1] = max(nums[0], nums[1])</font></div><div><font style="font-size: 12px;">        for i in range(2, len(nums)):</font></div><div><font style="font-size: 12px;">            dp[i] = max(dp[i-1], dp[i-2] + nums[i])</font></div><div><font style="font-size: 12px;">        return dp[-1]</font></div></div><div><font style="font-size: 12px;"><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">变化一：输出路径</span></font></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">def rob(nums):</font></div><div><font style="font-size: 12px;">    dp = [0] * len(nums)</font></div><div><font style="font-size: 12px;">    if len(nums) == 1:</font></div><div><font style="font-size: 12px;">        return nums[0]</font></div><div><font style="font-size: 12px;">    dp[0] = nums[0]</font></div><div><font style="font-size: 12px;">    dp[1] = max(nums[0], nums[1])</font></div><div><font style="font-size: 12px;">    for i in range(2, len(nums)):</font></div><div><font style="font-size: 12px;">        dp[i] = max(dp[i - 2] + nums[i], dp[i - 1])</font></div><div><font style="font-size: 12px;">    # 二分查找</font></div><div><font style="font-size: 12px;">    def erfen(tmp, target):</font></div><div><font style="font-size: 12px;">        left = 0</font></div><div><font style="font-size: 12px;">        right = len(tmp)-1</font></div><div><font style="font-size: 12px;">        while left&lt;right:</font></div><div><font style="font-size: 12px;">            mid = (left+right)//2</font></div><div><font style="font-size: 12px;">            if tmp[mid]&lt;target:</font></div><div><font style="font-size: 12px;">                left = mid + 1</font></div><div><font style="font-size: 12px;">            else:</font></div><div><font style="font-size: 12px;">                right = mid</font></div><div><font style="font-size: 12px;">        return left</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;"><b>    '''</b></font></div><div><font style="font-size: 12px;"><b>    例子：nums=[2,7,9,3,1]，dp=[2,7,11,11,12]。</b></font></div><div><font style="font-size: 12px;"><b>    找12第一次出现的地方：4，那么nums[4]肯定偷了。 再找12-nums[4]第一次出现的地方：2，那么nums[2]肯定偷了</b></font></div><div><font style="font-size: 12px;"><b>    '''</b></font></div><div><font style="font-size: 12px;">    res = []</font></div><div><font style="font-size: 12px;">    cur_dp = dp[-1] # 要找的dp值</font></div><div><font style="font-size: 12px;">    fir_max = 99</font></div><div><font style="font-size: 12px;">    while cur_dp&gt;=0 and fir_max&gt;0:</font></div><div><font style="font-size: 12px;">        fir_max = erfen(dp, cur_dp) # 找到cur_dp第一个出现的位置</font></div><div><font style="font-size: 12px;">        res.append(nums[fir_max])      # 将这个位置的nums值加入res</font></div><div><font style="font-size: 12px;">        cur_dp = cur_dp-nums[fir_max]     # 下一个要找的dp</font></div><div><font style="font-size: 12px;">    return res</font></div></div><div><font style="font-size: 12px;"><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">例二</span><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">：所有的房屋都围成一圈 ，这意味着第一个房屋和最后一个房屋是紧挨着的，再进行打家劫舍。</span></font></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">思路：第一家和最后一家，不能同时偷。所以分成两部分，对[0:n-1], [1:n]分别进行计算，再取最大值。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def rob(self, nums):</font></div><div><font style="font-size: 12px;">        if len(nums)==1:</font></div><div><font style="font-size: 12px;">            return nums[0]</font></div><div><font style="font-size: 12px;">        # 普通的打家劫舍函数</font></div><div><font style="font-size: 12px;">        def rob_(tmp):</font></div><div><font style="font-size: 12px;">            if len(tmp)==1:</font></div><div><font style="font-size: 12px;">                return tmp[0]</font></div><div><font style="font-size: 12px;">            dp = [0]*(len(tmp))</font></div><div><font style="font-size: 12px;">            dp[0] = tmp[0]</font></div><div><font style="font-size: 12px;">            dp[1] = max(tmp[0], tmp[1])</font></div><div><font style="font-size: 12px;">            for i in range(2, len(tmp)):</font></div><div><font style="font-size: 12px;">                dp[i] = max(dp[i-1], dp[i-2]+tmp[i])</font></div><div><font style="font-size: 12px;">            return dp[-1]</font></div><div><font style="font-size: 12px;">       # 去除最后一个元素， 去除第一个元素，分别带入函数。 取结果最大的</font></div><div><font style="font-size: 12px;">        res1 = rob_(nums[:len(nums)-1])</font></div><div><font style="font-size: 12px;">        res2 = rob_(nums[1:])</font></div><div><font style="font-size: 12px;">        return max(res1, res2)</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px; color: rgb(54, 101, 238); font-weight: bold;">例三：打家劫舍3</span></div><div><span style="font-size: 12px;">相连的房子不能被同时打劫。</span></div><div><span style="font-size: 12px;"><img src="%E5%8A%A8%E6%80%81%E8%A7%84%E5%88%92.resources/50D0A67E-C7B6-4928-A9FA-7827ECDF4BF0.png" height="298" width="288"/><br/></span></div><div><span style="font-size: 12px;">后序遍历。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def rob(self, root):</font></div><div><font style="font-size: 12px;">        def hx(root):</font></div><div><font style="font-size: 12px;">            if root==None:</font></div><div><font style="font-size: 12px;">                return (0, 0)</font></div><div><font style="font-size: 12px;">            left = hx(root.left)</font></div><div><font style="font-size: 12px;">            right = hx(root.right)</font></div><div><font style="font-size: 12px;">            val0 = max(left[0], left[1])+max(right[0], right[1]) # 不偷当前root</font></div><div><font style="font-size: 12px;">            val1 = left[0] + right[0] + root.val                 # 偷当前节点</font></div><div><font style="font-size: 12px;">            return (val0, val1)</font></div><div><font style="font-size: 12px;">        tmp = hx(root)</font></div><div><font style="font-size: 12px;">        return max(tmp)</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px; color: rgb(4, 51, 255); font-family: &quot;Courier New&quot;;">19.股票问题</span></div><div><span style="font-size: 12px;">模板：股票问题的状态有三个：</span><span style="font-size: 12px; color: rgb(255, 38, 0);">天数、允许交易的最大次数、当前是否持有</span><span style="font-size: 12px;">（0：不持有，1：持有）</span></div><div><span style="font-size: 12px;">dp[3][2][1] 的含义：今天是第三天，</span><span style="font-size: 12px;">至今最多进行 2 次交易，</span><span style="font-size: 12px;">现在手上持有着股票。</span></div><div><span style="font-size: 12px;">最后求的：</span><span style="font-size: 12px;">dp[n - 1][K][0]，即最后一天，最多允许 K 次交易，最多获得多少利润。</span></div><div><span style="font-size: 12px;"><br/></span></div><div><span style="font-size: 12px;">base case：</span></div><div><span style="font-size: 12px;">dp[-1][...][0] = dp[...][0][0] = 0         # i=-1或k=0，意味着交易还没开始，所以是0。</span></div><div><span style="font-size: 12px;">dp[-1][...][1] = dp[...][0][1] = -infinity # </span><span style="font-size: 12px;">i=-1或k=0，不可能持有股票，所以设为负无穷。</span></div><div><span style="font-size: 12px;">一般会使用dp[-1]推导出dp[0]</span></div><div><span style="font-size: 12px;">状态转移方程：</span></div><div><span style="font-size: 12px;">dp[i][k][0] = max(dp[i-1][k][0], dp[i-1][k][1] + prices[i])</span></div><div><span style="font-size: 12px;">dp[i][k][1] = max(dp[i-1][k][1], dp[i-1][k-1][0] - prices[I])</span></div><div><span style="font-size: 12px;">只有买的时候，才会计入交易次数，卖的时候不会。</span></div><div><span style="font-size: 12px;"><br/></span></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">例一：买卖股票的最佳时机（只能买卖一次，k=1）</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">给定一个数组 prices ，它的第i个元素 prices[i] 表示一支给定股票第 i 天的价格。</span></div><div><font style="font-size: 12px;"><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">只能买卖一次，求最大利润。</span><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">如果你不能获取任何利润，返回 0 。</span></font></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">输入：[7,1,5,3,6,4]</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">输出：5</span></div><div><font style="font-size: 12px;"><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">解释：在第 2 天（股票价格 = 1）的时候买入，在第 5 天（股票价格 = 6）的时候卖出，最大利润 = 6-1 = 5 。</span><span style="font-size: 12px; color: unset; font-family: &quot;Courier New&quot;;">注意利润不能是 7-1 = 6, 因为卖出价格需要大于买入价格；同时，你不能在买入前卖出股票。</span></font></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">解法1：贪心</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def maxProfit(self, prices):</font></div><div><font style="font-size: 12px;">        min_price = prices[0]</font></div><div><font style="font-size: 12px;">        result = 0</font></div><div><font style="font-size: 12px;">        for i in range(1, len(prices)):</font></div><div><font style="font-size: 12px;">            if prices[i] &lt; min_price:</font></div><div><font style="font-size: 12px;">                min_price = prices[i]</font></div><div><font style="font-size: 12px;">            else:</font></div><div><font style="font-size: 12px;">                result = max(result, prices[i] - min_price)</font></div><div><font style="font-size: 12px;">        return result</font></div></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">解法2：动态规划</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">对于所有情况来说，都有k=1，所以省略k。</span></div><div><span style="font-size: 12px;">dp[0][0]和dp[0][1]是由base case的dp[-1]和状态转移方程推导出来的。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div>def maxProfit(self, prices):</div><div>    n = len(prices)</div><div>    dp = [[0] * 2 for _ in range(n)]</div><div>    dp[0][0] = 0</div><div>    dp[0][1] = -prices[0]</div><div>    for i in range(1, len(prices)):</div><div>        dp[i][0] = max(dp[i - 1][0], dp[i - 1][1] + prices[i])</div><div>        dp[i][1] = max(dp[i - 1][1], -prices[i])</div><div>    return dp[n - 1][0]</div></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">解法3：动态规划的优化</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div>def maxProfit(self, prices):</div><div>    n = len(prices)</div><div>    dp_i_0 = 0</div><div>    dp_i_1 = -prices[0]</div><div>    for i in range(1, len(prices)):</div><div>        dp_i_0 = max(dp_i_0, dp_i_1 + prices[i])</div><div>        dp_i_1 = max(dp_i_1, -prices[i])</div><div>    return dp_i_0</div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">例二：买卖股票的最佳时机2（买卖无数次）</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">最多持有一支股票。</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">解法1：贪心：把所有利润大于0的买一遍。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div>def maxProfit(self, prices):</div><div>    res = 0</div><div>    for i in range(1, len(prices)):</div><div>        tmp = prices[i] - prices[i - 1] # 只加正数</div><div>        if tmp &gt; 0:</div><div>            res += tmp</div><div>    return res</div></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">解法2：动态规划解法：</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div># 普通解法</div><div>def maxProfit(self, prices):</div><div>    dp = [[0] * 2 for _ in range(len(prices))]</div><div>    dp[0][0] = 0</div><div>    dp[0][1] = -prices[0]</div><div>    for i in range(1, len(prices)):</div><div>        dp[i][0] = max(dp[i - 1][0], dp[i - 1][1] + prices[i])</div><div>        dp[i][1] = max(dp[i - 1][1], dp[i - 1][0] - prices[i])</div><div>    return dp[-1][0]</div><div><br/></div><div># 滚动数组解法：</div><div>def maxProfit(self, prices):</div><div>    dp_i_0 = 0</div><div>    dp_i_1 = -prices[0]</div><div>    for i in range(1, len(prices)):</div><div>        tmp = dp_i_0</div><div>        dp_i_0 = max(dp_i_0, dp_i_1 + prices[i])</div><div>        dp_i_1 = max(dp_i_1, dp_i_0 - prices[i])</div><div>    return dp_i_0</div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">例三：最佳买卖股票时机含冷冻期（无数次买卖，冷冻期1天）</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">可以多次买卖，但卖出股票后，你无法在第二天买入股票 (即冷冻期为1天)。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div>def maxProfit(self, prices):</div><div>    dp = [[0] * 2 for _ in range(len(prices))]</div><div>    for i in range(len(prices)):</div><div>        if i == 0:</div><div>            dp[i][0] = 0</div><div>            dp[i][1] = -prices[i]</div><div>        elif i == 1:</div><div>            dp[i][0] = max(dp[i - 1][0], dp[i - 1][1] + prices[i])</div><div>            dp[i][1] = max(dp[i - 1][1], -prices[i])</div><div>        else:</div><div>            dp[i][0] = max(dp[i - 1][0], dp[i - 1][1] + prices[i])</div><div>            dp[i][1] = max(dp[i - 1][1], dp[i - 2][0] - prices[i])</div><div>            <font color="#FF2600"># 如果没有冷冻期应该是max(dp[i-1][1], dp[i-1][0] - prices[i])，dp[i-1][0]=max(dp[i-2][0], dp[i-2][1] + prices[i]),但是dp[i-1][0]不能等于dp[i-2][1]+price[i]，因为这样就代表i-1天的时候，卖了股票。所以dp[i-1][0]只能等于dp[i-2][0]。</font></div><div>    return dp[-1][0]</div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">例四：买卖股票的最佳时机3（最多完成两笔交易）</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">最多进行两次交易。注意：你不能同时参与多笔交易。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div>def maxProfit(self, prices):</div><div>    max_k = 2</div><div>    n = len(prices)</div><div>    dp = [[[0] * 2 for _ in range(max_k + 1)] for _ in range(n)]</div><div>    for i in range(n):  # 天数</div><div>        for k in range(max_k, 0, -1):  # 交易次数上限</div><div>            if i == 0:</div><div>                dp[i][k][0] = 0</div><div>                dp[i][k][1] = -prices[i]</div><div>            else:</div><div>                dp[i][k][0] = max(dp[i - 1][k][0], dp[i - 1][k][1] + prices[i])</div><div>                dp[i][k][1] = max(dp[i - 1][k][1], dp[i - 1][k - 1][0] - prices[i])  # dp[i][k][...]是由dp[i-1][k-1][...]推导出来的，所以k的遍历顺序无所谓</div><div>    return dp[n - 1][max_k][0]</div></div><div><span style="font-size: 12px;"><br/></span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def maxProfit(self, prices):</font></div><div><font style="font-size: 12px;">        dp = [[0]*4 for _ in range(len(prices))]</font></div><div><font style="font-size: 12px;">        dp[0][0] = -prices[0]        # 持有股票</font></div><div><font style="font-size: 12px;">        dp[0][1] = 0                 # 不持股票</font></div><div><font style="font-size: 12px;">        dp[0][2] = -prices[0]        # 第二次持有股票</font></div><div><font style="font-size: 12px;">        dp[0][3] = 0                 # 第二次不持股票</font></div><div><font style="font-size: 12px;">        for i in range(1, len(prices)):</font></div><div><font style="font-size: 12px;">            dp[i][0] = max(dp[i-1][0], -prices[i]) # 第一次买</font></div><div><font style="font-size: 12px;">            dp[i][1] = max(dp[i-1][1], dp[i-1][0]+prices[i])  # 第一次卖</font></div><div><font style="font-size: 12px;">            dp[i][2] = max(dp[i-1][2], dp[i-1][1]-prices[i])  # 第二次买</font></div><div><font style="font-size: 12px;">            dp[i][3] = max(dp[i-1][3], dp[i-1][2]+prices[i])  # 第二次卖 </font></div><div><font style="font-size: 12px;">        return dp[-1][3]</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><font color="#FF2600" style="font-size: 12px;"><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">最多完成k笔交易。</span><span style="letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; white-space: normal; widows: 2; word-spacing: 0px; font-size: 12px; color: rgb(255, 38, 0); font-family: -apple-system, system-ui, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;; font-variant-caps: normal; font-variant-ligatures: normal;">你不能同时参与多笔交易。</span></font></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div>def maxProfit(self, k, prices):</div><div>    max_k = k</div><div>    n = len(prices)</div><div>    dp = [[[0] * 2 for _ in range(max_k + 1)] for _ in range(n)]</div><div>    for i in range(n):  # 天数</div><div>        for k in range(max_k, 0, -1):  # 交易次数上限</div><div>            if i == 0:</div><div>                dp[i][k][0] = 0</div><div>                dp[i][k][1] = -prices[i]</div><div>            else:</div><div>                dp[i][k][0] = max(dp[i - 1][k][0], dp[i - 1][k][1] + prices[i])</div><div>                dp[i][k][1] = max(dp[i - 1][k][1], dp[i - 1][k - 1][0] - prices[i])  # dp[i][k][...]是由dp[i-1][k-1][...]推导出来的，所以k的遍历顺序无所谓</div><div>    return dp[n - 1][max_k][0]</div></div><div><span style="font-size: 12px;"><br/></span></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">例五：买卖股票的最佳时机，含手续费</span></div><div><font style="font-size: 12px;"><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">你可以无限次地完成交易，但是你每笔交易都需要付手续费。如果你已经购买了一个股票，在卖出它之前你就不能再继续购买股票了。</span><span style="font-size: 12px; color: unset; font-family: &quot;Courier New&quot;;">返回获得利润的最大值。</span></font></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">输入：prices = [1, 3, 2, 8, 4, 9], fee = 2</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">输出：8</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">解释：能够达到的最大利润:  </span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">在此处买入 prices[0] = 1</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">在此处卖出 prices[3] = 8</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">在此处买入 prices[4] = 4</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">在此处卖出 prices[5] = 9</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">总利润: ((8 - 1) - 2) + ((9 - 4) - 2) = 8</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">思路：在卖出时，减去手续费即可。和上面的题一样。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font face="Courier New" style="font-size: 12px;">def maxProfit(prices, fee):</font></div><div><font face="Courier New" style="font-size: 12px;">    dp = [[0]*2 for _ in range(len(prices))]</font></div><div><font face="Courier New" style="font-size: 12px;">    dp[0][0] = -prices[0]</font></div><div><font face="Courier New" style="font-size: 12px;">    dp[0][1] = 0</font></div><div><font face="Courier New" style="font-size: 12px;">    for i in range(1, len(prices)):</font></div><div><font face="Courier New" style="font-size: 12px;">        dp[i][0] = max(dp[i-1][1] - prices[i], dp[i-1][0]) # 持有股票</font></div><div><font face="Courier New" style="font-size: 12px;">        dp[i][1] = max(dp[i-1][1], dp[i-1][0] + prices[i] - fee)  # 不持股票</font></div><div><font face="Courier New" style="font-size: 12px;">    return dp[-1][1]</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px; color: rgb(4, 51, 255); font-family: &quot;Courier New&quot;;">六、子序列问题</span></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">20.最长递增子序列：贪心+二分。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">def lengthOfLIS(nums):</font></div><div><font style="font-size: 12px;">    def lengthOfLIS(self, nums):</font></div><div><font style="font-size: 12px;"><font>        stack = [nums[0]]  # </font>表示长度为i的最长上升子序列的末尾数字的最小值</font></div><div><font style="font-size: 12px;">        # 普通的二分查找</font></div><div><font style="font-size: 12px;">        def erfen(nums, target):</font></div><div><font style="font-size: 12px;">            left = 0</font></div><div><font style="font-size: 12px;">            right = len(nums)-1</font></div><div><font style="font-size: 12px;">            while left&lt;right:</font></div><div><font style="font-size: 12px;">                mid = (left+right)//2</font></div><div><font style="font-size: 12px;">                if nums[mid]&lt;target:</font></div><div><font style="font-size: 12px;">                    left = mid +1</font></div><div><font style="font-size: 12px;">                else:</font></div><div><font style="font-size: 12px;">                    right = mid</font></div><div><font style="font-size: 12px;">            return left</font></div><div><font style="font-size: 12px;">       </font></div><div><font style="font-size: 12px;">        for i in range(1, len(nums)):</font></div><div><font style="font-size: 12px;"><font>            if nums[i]&gt;</font>stack<font>[-1]:          # 把大数加末尾</font></font></div><div><font style="font-size: 12px;"><font>               </font>stack<font>.append(nums[i])</font></font></div><div><font style="font-size: 12px;">            else:                    # 否则，就把小元素替换进去</font></div><div><font style="font-size: 12px;"><font>               index = erfen(</font>stack<font>, nums[i])</font></font></div><div><font style="font-size: 12px;"><font>               </font>stack<font>[index] = nums[i]</font></font></div><div><font style="font-size: 12px;"><font>        return len(</font>stack<font>)</font></font></div></div><div><font style="font-size: 12px;"><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">思路2：O(n^2)的思路，动态规划</span></font></div><div><font style="font-size: 12px;"><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">dp[i]：第i个元素为最大值的话，最长递增子序列是多少。遍历i之前的元素，</span><span style="font-size: 12px; font-family: &quot;Courier New&quot;; font-weight: bold;">挑出来一个dp值最大的，然后加1即可（用for循环挑）</span><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">。</span></font></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def lengthOfLIS(self, nums):</font></div><div><font style="font-size: 12px;">        dp = [1]*len(nums)</font></div><div><font style="font-size: 12px;">        for i in range(len(nums)):</font></div><div><font style="font-size: 12px;">            for j in range(i):</font></div><div><font style="font-size: 12px;">                if nums[i]&gt;nums[j]:</font></div><div><font style="font-size: 12px;">                    dp[i] = max(dp[i], dp[j] + 1)</font></div><div><font style="font-size: 12px;">        return max(dp)</font></div></div><div><span style="font-size: 12px;">最长递增子序列具体是啥/</span><span style="font-size: 12px; font-weight: bold;">字典序最小的</span><span style="font-size: 12px;">：从后向前遍历dp，记录第一次出现最大值的位置。eg:[1,2,8,6,4]，dp=[1,2,3,3,3]，那么[1,2,4]必然是字典序最小的。因为dp值相同，越往后面，数字肯定越小。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">def LIS( nums):</font></div><div><font style="font-size: 12px;">    dp = [1] * len(nums)</font></div><div><font style="font-size: 12px;">    for i in range(1, len(nums)):</font></div><div><font style="font-size: 12px;">        for j in range(i):</font></div><div><font style="font-size: 12px;">            if nums[i] &gt; nums[j]:</font></div><div><font style="font-size: 12px;">                dp[i] = max(dp[i], dp[j] + 1)</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">    max_len = max(dp)</font></div><div><font style="font-size: 12px;">    res = [] </font></div><div><font style="font-size: 12px;">    for i in range(len(dp) - 1, -1, -1):</font></div><div><font style="font-size: 12px;">        if dp[i] == max_len:</font></div><div><font style="font-size: 12px;">            res.append(nums[i])</font></div><div><font style="font-size: 12px;">            max_len = max_len - 1</font></div><div><font style="font-size: 12px;">    res.reverse()</font></div><div><font style="font-size: 12px;">    return res</font></div></div><div><span style="font-size: 12px;">最长递增子序列具体是啥（二分）：先算出最长序列长度。用数组dp[i]记录"如果第i个元素在最终结果里，到第i位置的序列长度"，从后向前遍历数组，如果dp[i]=当前最长长度，说明nums[i]肯定在最终答案里，把nums[i]加入结果，最长长度=最长长度-1。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def LIS(self, nums):</font></div><div><font style="font-size: 12px;">        stack = [nums[0]]  # 表示长度为i的最长上升子序列的末尾数字的最小值</font></div><div><font style="font-size: 12px;">        dp = [1] * len(nums) # dp[i]表示，如果以nums[i]结尾，最长上升子序列的长度</font></div><div><font style="font-size: 12px;">        # 普通的二分查找</font></div><div><font style="font-size: 12px;">        def erfen(nums, target):</font></div><div><font style="font-size: 12px;">            left = 0</font></div><div><font style="font-size: 12px;">            right = len(nums) - 1</font></div><div><font style="font-size: 12px;">            while left &lt; right:</font></div><div><font style="font-size: 12px;">                mid = (left + right) // 2</font></div><div><font style="font-size: 12px;">                if nums[mid] &lt; target:</font></div><div><font style="font-size: 12px;">                    left = mid + 1</font></div><div><font style="font-size: 12px;">                else:</font></div><div><font style="font-size: 12px;">                    right = mid</font></div><div><font style="font-size: 12px;">            return left</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">        for i in range(1, len(nums)):</font></div><div><font style="font-size: 12px;">            if nums[i] &gt; stack[-1]:  # 把大数加末尾</font></div><div><font style="font-size: 12px;">                stack.append(nums[i])</font></div><div><font style="font-size: 12px;">                dp[i] = len(stack)</font></div><div><font style="font-size: 12px;">            else:  # 否则，就把小元素替换进去</font></div><div><font style="font-size: 12px;">                index = erfen(stack, nums[i])</font></div><div><font style="font-size: 12px;">                stack[index] = nums[i]</font></div><div><font style="font-size: 12px;">                dp[i] = index+1 </font></div><div><font style="font-size: 12px;">        ans = []  # 记录结果的数组</font></div><div><font style="font-size: 12px;">        l = len(stack) # 最长序列长度</font></div><div><font style="font-size: 12px;">        for i in range(len(nums) - 1, -1, -1):</font></div><div><font style="font-size: 12px;">            if dp[i] == l: # 如果不等，说明答案里没有nums[i]     </font></div><div><font style="font-size: 12px;">                ans.append(nums[i])</font></div><div><font style="font-size: 12px;">                l = l - 1</font></div><div><font style="font-size: 12px;">        ans.reverse()</font></div><div><font style="font-size: 12px;">        return ans</font></div></div><div><span style="font-size: 12px;">输出所有最长递增子序列</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">def lengthOfLIS(nums):</font></div><div><font style="font-size: 12px;">    dp = [1] * len(nums)</font></div><div><font style="font-size: 12px;">    for i in range(len(nums)):</font></div><div><font style="font-size: 12px;">        for j in range(i):</font></div><div><font style="font-size: 12px;">            if nums[i] &gt; nums[j]:</font></div><div><font style="font-size: 12px;">                dp[i] = max(dp[i], dp[j] + 1)</font></div><div><font style="font-size: 12px;">    result = []</font></div><div><font style="font-size: 12px;">    path = []</font></div><div><font style="font-size: 12px;">    def backtracking(index, pre): # 在dp的值减小的同时，要保证序列是下降的</font></div><div><font style="font-size: 12px;">        if index==0:</font></div><div><font style="font-size: 12px;">            result.append(path[::-1])</font></div><div><font style="font-size: 12px;">        for i in range(len(nums)):</font></div><div><font style="font-size: 12px;">            if dp[i]==index and nums[i]&lt;pre:</font></div><div><font style="font-size: 12px;">                path.append(nums[i])</font></div><div><font style="font-size: 12px;">                pre = nums[i]</font></div><div><font style="font-size: 12px;">                backtracking(index-1, pre)</font></div><div><font style="font-size: 12px;">                path.pop()</font></div><div><font style="font-size: 12px;">    backtracking(max(dp), 999)</font></div><div><font style="font-size: 12px;">    return result</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px;">最长递增子序列的个数有几个 ：</span></div><div><span style="font-size: 12px;">那么在nums[i] &gt; nums[j]前提下，如果在[0, i-1]的范围内，找到了j，使得dp[j] + 1 &gt; dp[i]，说明找到了一个更长的递增子序列。</span></div><div><span style="font-size: 12px;">那么以j为结尾的子串的最长递增子序列的个数，就是最新的以i为结尾的子串的最长递增子序列的个数，即：count[i] = count[j]。</span></div><div><span style="font-size: 12px;">在nums[i] &gt; nums[j]前提下，如果在[0, i-1]的范围内，找到了j，使得dp[j] + 1 == dp[i]，说明找到了两个相同长度的递增子序列。</span></div><div><span style="font-size: 12px;">那么以i为结尾的子串的最长递增子序列的个数 就应该加上以j为结尾的子串的最长递增子序列的个数，即：count[i] += count[j];</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def findNumberOfLIS(nums):</font></div><div><font style="font-size: 12px;">        size = len(nums)</font></div><div><font style="font-size: 12px;">        if size&lt;= 1: </font></div><div><font style="font-size: 12px;">            return size</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">        dp = [1 for i in range(size)] # 以i结尾，最长递增子序列的长度</font></div><div><font style="font-size: 12px;">        count = [1 for i in range(size)] # 以i结尾，最长递增子序列的个数</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">        maxCount = 0</font></div><div><font style="font-size: 12px;">        for i in range(1, size):</font></div><div><font style="font-size: 12px;">            for j in range(i):</font></div><div><font style="font-size: 12px;">                if nums[i] &gt; nums[j]:</font></div><div><font style="font-size: 12px;">                    if dp[j] + 1 &gt; dp[i]: # 找到了更长的递增子序列</font></div><div><font style="font-size: 12px;">                        dp[i] = dp[j] + 1</font></div><div><font style="font-size: 12px;">                        count[i] = count[j]</font></div><div><font style="font-size: 12px;">                    elif dp[j] + 1 == dp[i]: # 找到了相同长度的递增子序列</font></div><div><font style="font-size: 12px;">                        count[i] += count[j]</font></div><div><font style="font-size: 12px;">                if dp[i] &gt; maxCount: # 更新最大值</font></div><div><font style="font-size: 12px;">                    maxCount = dp[i];</font></div><div><font style="font-size: 12px;">        result = 0</font></div><div><font style="font-size: 12px;">        for i in range(size):</font></div><div><font style="font-size: 12px;">            if maxCount == dp[i]:</font></div><div><font style="font-size: 12px;">                result += count[i]</font></div><div><font style="font-size: 12px;">        return result</font></div></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;; font-weight: bold;">信封嵌套</span></div><div><span style="font-size: 12px;"><img src="%E5%8A%A8%E6%80%81%E8%A7%84%E5%88%92.resources/4C268E07-F468-423F-A407-AAC9712EAB3C.png" height="628" width="1330"/><br/></span></div><div><span style="font-size: 12px;">思路：1.先对宽度w进行升序排序，保证宽度一定可以满足条件。</span></div><div><span style="font-size: 12px;">2.如果遇到w相同的情况，则按照高度h降序排序(保证了宽度相同的两个信封不能相互嵌套)。</span></div><div><span style="font-size: 12px; color: unset;">3.在h维度上计算最长递增子序列，就是最终答案。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">def maxEnvelopes(self, letters):</font></div><div><font style="font-size: 12px;">    # 1.按宽升序排序，如果宽度一样，就按高降序。</font></div><div><font style="font-size: 12px;">    letters.sort(key=lambda x: (x[0], -x[1]))</font></div><div><font style="font-size: 12px;">    height = [letter[1] for letter in letters]</font></div><div><font style="font-size: 12px;">    # 2.用height求最长上升子序列，这里也可以用贪心+二分</font></div><div><font style="font-size: 12px;">    dp = [1] * len(letters)</font></div><div><font style="font-size: 12px;">    for i in range(1, len(height)):</font></div><div><font style="font-size: 12px;">        for j in range(i):</font></div><div><font style="font-size: 12px;">            if height[i] &gt; height[j]:</font></div><div><font style="font-size: 12px;">                dp[i] = max(dp[i], dp[j] + 1)</font></div><div><font style="font-size: 12px;">    return max(dp)</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px;">数组中的最长连续子序列（无序数组）</span></div><div><span style="font-size: 12px;">给定无序数组arr，返回其中最长的连续序列的长度(要求值连续，位置可以不连续,例如 3,4,5,6为连续的自然数）</span></div><div><span style="font-size: 12px;">输入：</span><span style="font-size: 12px; color: unset;">[100,4,200,1,3,2]</span></div><div><span style="font-size: 12px;">返回值：</span><span style="font-size: 12px; color: unset;">4</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">def MLS(arr):</font></div><div><font style="font-size: 12px;">    hs = {}</font></div><div><font style="font-size: 12px;">    ans = 0</font></div><div><font style="font-size: 12px;">    for i in arr: # 1.将所有元素存到hs中，这样查找复杂度变为O(1)</font></div><div><font style="font-size: 12px;">        hs[i] = hs.get(i, 0)+1</font></div><div><font style="font-size: 12px;">    for i in arr: # 2.找到头</font></div><div><font style="font-size: 12px;">        if i-1 in hs:</font></div><div><font style="font-size: 12px;">            continue</font></div><div><font style="font-size: 12px;">        else:    </font></div><div><font style="font-size: 12px;">            res = 0</font></div><div><font style="font-size: 12px;">            tmp = i</font></div><div><font style="font-size: 12px;">            while tmp in hs: # 从头开始遍历，看看有多长</font></div><div><font style="font-size: 12px;">                res += 1</font></div><div><font style="font-size: 12px;">                tmp += 1</font></div><div><font style="font-size: 12px;">            ans = max(res, ans)</font></div><div><font style="font-size: 12px;">    return ans</font></div></div><div><span style="font-size: 12px;">时间复杂度：O(N)</span></div><div><span style="font-size: 12px;">空间复杂度：O(N)</span></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px;">1671. 得到山形数组的最少删除次数</span></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">21.</span><span style="font-size: 12px;">最长上升子数组，</span><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">最长递增子数组：一个for循环</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">这里的dp数组不是“递增”的。 中间只要中断一次，就立刻从0开始</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font face="Courier New" style="font-size: 12px;">def findLengthOfLCIS(nums):</font></div><div><font face="Courier New" style="font-size: 12px;">    dp = [1] * len(nums)</font></div><div><font face="Courier New" style="font-size: 12px;">    for i in range(1, len(nums)):</font></div><div><font face="Courier New" style="font-size: 12px;">        if nums[i] &gt; nums[i - 1]:</font></div><div><font face="Courier New" style="font-size: 12px;">            dp[i] = dp[i - 1] + 1</font></div><div><font face="Courier New" style="font-size: 12px;">    return max(dp)</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px;">最长严格上升子数组</span></div><div><span style="font-size: 12px;">改变一个元素后，最长上升子数组是多长</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">def maxSubArrayLengthTwo(self, nums):</font></div><div><font style="font-size: 12px;">    if len(nums) == 1:</font></div><div><font style="font-size: 12px;">        return 1</font></div><div><font style="font-size: 12px;">    dpleft = [1] * len(nums)</font></div><div><font style="font-size: 12px;">    for i in range(1, len(nums)): # 正着求最长递增子数组</font></div><div><font style="font-size: 12px;">        if nums[i] &gt; nums[i - 1]:</font></div><div><font style="font-size: 12px;">            dpleft[i] = dpleft[i - 1] + 1</font></div><div><font style="font-size: 12px;">        else:</font></div><div><font style="font-size: 12px;">            dpleft[i] = 1</font></div><div><font style="font-size: 12px;">    dpright = [1] * len(nums) </font></div><div><font style="font-size: 12px;">    for i in range(len(nums) - 2, -1, -1): # 倒着求最长递减子数组</font></div><div><font style="font-size: 12px;">        if nums[i] &lt; nums[i + 1]:</font></div><div><font style="font-size: 12px;">            dpright[i] = dpright[i + 1] + 1</font></div><div><font style="font-size: 12px;">        else:</font></div><div><font style="font-size: 12px;">            dpright[i] = 1</font></div><div><font style="font-size: 12px;">    maxlen = 1</font></div><div><font style="font-size: 12px;">    for i in range(len(nums)):</font></div><div><font style="font-size: 12px;">        if i &gt; 0 and i &lt; len(nums) - 1 and nums[i + 1] - nums[i - 1] &gt; 1: # 左右数组可以连起来</font></div><div><font style="font-size: 12px;">            maxlen = max(maxlen, dpleft[i - 1] + dpright[i + 1] + 1)</font></div><div><font style="font-size: 12px;">        else:</font></div><div><font style="font-size: 12px;">            if i &gt; 0:</font></div><div><font style="font-size: 12px;">                maxlen = max(maxlen, dpleft[i - 1] + 1) # 连不起来就看接前面更长，还是接后面更长</font></div><div><font style="font-size: 12px;">            if i &lt; len(nums) - 1 and nums[i + 1] &gt; 1:   # 因为是正数数组，后一个数比1大，才有可能接起来</font></div><div><font style="font-size: 12px;">                maxlen = max(maxlen, dpright[i + 1] + 1)</font></div><div><font style="font-size: 12px;">    return maxlen</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">22.最长重复子数组：两个for循环，遍历两个数组</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">给两个整数数组 nums1 和 nums2 ，返回两个数组中公共的、长度最长的子数组的长度 。</span></div><div><font style="font-size: 12px;"><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">思路：nums正常处理，dp要多定义1位。dp[i][j]，nums1数组的前i个元素，nums2数组的前j个元素。 所以dp[0][0]，dp[i][0]，dp[0][j]是无意义的。 只要碰到一个相等的，它们的碰到</span><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">nums1[i-1] == nums2[j-1]:</span></font></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">那么就dp[i][j] = dp[i-1][j-1] + 1。</span></div><div><span style="font-size: 12px; color: unset; font-family: &quot;Courier New&quot;;">这里的dp[i][j]数组不是“递增”的，最大值不一定在dp[-1][-1]。看代码的if，只要中断一次，即使后面再接上，也是从0开始。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def findLength(self, nums1, nums2):</font></div><div><font style="font-size: 12px;">        dp = [[0]*(len(nums2)+1) for _ in range(len(nums1)+1)]</font></div><div><font style="font-size: 12px;">        max_num = 0</font></div><div><font style="font-size: 12px;">        for i in range(1, len(nums1)+1):</font></div><div><font style="font-size: 12px;">            for j in range(1, len(nums2)+1):</font></div><div><font style="font-size: 12px;">                if nums1[i-1]==nums2[j-1]:</font></div><div><font style="font-size: 12px;">                    dp[i][j] = dp[i-1][j-1] + 1</font></div><div><font style="font-size: 12px;">                max_num = max(max_num, dp[i][j])</font></div><div><font style="font-size: 12px;">        return max_num</font></div></div><div><span style="font-size: 12px; font-weight: bold;">最长公共子串</span> <span style="font-size: 12px;">是啥</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def LCS(self , str1, str2):</font></div><div><font style="font-size: 12px;">        dp = [[0]*(len(str2)+1) for _ in range(len(str1)+1)]</font></div><div><font style="font-size: 12px;">        max_len = 0</font></div><div><font style="font-size: 12px;">        pos = 0</font></div><div><font style="font-size: 12px;">        for i in range(1, len(str1)+1):</font></div><div><font style="font-size: 12px;">            for j in range(1, len(str2)+1):</font></div><div><font style="font-size: 12px;">                if str1[i-1]==str2[j-1]:</font></div><div><font style="font-size: 12px;">                    dp[i][j] = dp[i-1][j-1]+1</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">                if max_len&lt;dp[i][j]:  # 更新最大长度结束位置</font></div><div><font style="font-size: 12px;">                    pos = i-1</font></div><div><font style="font-size: 12px;">                    max_len = dp[i][j]</font></div><div><font style="font-size: 12px;">        return str1[pos+1-max_len:pos+1]</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">23.最长公共子序列（经典题，必须会背）  </span> <span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">最长重复子数组的变形，当不相等时，</span><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">从dp[i - 1][j]和dp[i][j - 1]中找个最大的顶上。最长公共子序列（二）</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">给定两个字符串text1和text2，返回这两个字符串的最长公共子序列的长度。如果不存在公共子序列，返回0。</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">公共子序列，dp数组就是递增的。即使中断，也要从dp[i - 1][j]和dp[i][j - 1]中找个最大的顶上。而上一题，中断直接就归0.</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">        dp = [[0]*(len(text2)+1) for _ in range(len(text1)+1)]</font></div><div><font style="font-size: 12px;">        for i in range(1, len(text1)+1):</font></div><div><font style="font-size: 12px;">            for j in range(1, len(text2)+1):</font></div><div><font style="font-size: 12px;">                if text1[i-1]==text2[j-1]:</font></div><div><font style="font-size: 12px;">                    dp[i][j] = dp[i-1][j-1] + 1</font></div><div><font style="font-size: 12px;">                else:</font></div><div><font style="font-size: 12px;">                    dp[i][j] = max(dp[i-1][j], dp[i][j-1])</font></div><div><font style="font-size: 12px;">        return dp[-1][-1]</font></div></div><div><span style="font-size: 12px;">将空间复杂度优化到O(n)： 直接在i后面对2取余即可。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def longestCommonSubsequence(self, text1, text2):</font></div><div><font style="font-size: 12px;">        dp = [[0]*(len(text2)+1) for _ in range(2)]</font></div><div><font style="font-size: 12px;">        for i in range(1, len(text1)+1):</font></div><div><font style="font-size: 12px;">            for j in range(1, len(text2)+1):</font></div><div><font style="font-size: 12px;">                if text1[i-1]==text2[j-1]:</font></div><div><font style="font-size: 12px;">                    dp[i%2][j] = dp[(i-1)%2][j-1]+1</font></div><div><font style="font-size: 12px;">                else:</font></div><div><font style="font-size: 12px;">                    dp[i%2][j] = max(dp[i%2][j-1], dp[(i-1)%2][j])</font></div><div><font style="font-size: 12px;">        return dp[len(text1)% 2][-1]</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px;">最长公共子序列具体是啥</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">def LCS(text1, text2):</font></div><div><font style="font-size: 12px;">    dp = [[0] * (len(text2) + 1) for _ in range(len(text1) + 1)]</font></div><div><font style="font-size: 12px;">    for i in range(1, len(text1) + 1):</font></div><div><font style="font-size: 12px;">        for j in range(1, len(text2) + 1):</font></div><div><font style="font-size: 12px;">            if text1[i - 1] == text2[j - 1]:</font></div><div><font style="font-size: 12px;">                dp[i][j] = dp[i - 1][j - 1] + 1</font></div><div><font style="font-size: 12px;">            else:</font></div><div><font style="font-size: 12px;">                dp[i][j] = max(dp[i - 1][j], dp[i][j - 1])</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">    zhizhen_1 = len(text1)-1</font></div><div><font style="font-size: 12px;">    zhizhen_2 = len(text2)-1</font></div><div><font style="font-size: 12px;">    res = ''</font></div><div><font style="font-size: 12px;">    while zhizhen_1&gt;=0 and zhizhen_2&gt;=0:          # 从两个text的末尾开始比较</font></div><div><font style="font-size: 12px;">        if text1[zhizhen_1] == text2[zhizhen_2]:  # 1.相等，当前字符加入res</font></div><div><font style="font-size: 12px;">            res = res + text1[zhizhen_1]</font></div><div><font style="font-size: 12px;">            zhizhen_1 = zhizhen_1 - 1</font></div><div><font style="font-size: 12px;">            zhizhen_2 = zhizhen_2 - 1</font></div><div><font style="font-size: 12px;">        # 如果不相等，dp[i-1][j]&gt;dp[i][j-1]，说明当时dp[i][j]取的是前者的值，所以dp[i][j]=d[i-1][j]，所以i可以往后退一步</font></div><div><font style="font-size: 12px;">        # 当初定义dp的时候，就比text多一位，所以现在要+1</font></div><div><font style="font-size: 12px;">        elif dp[zhizhen_1+1 -1][zhizhen_2+1]&gt;dp[zhizhen_1+1][zhizhen_2+1 -1]:</font></div><div><font style="font-size: 12px;">            zhizhen_1 = zhizhen_1-1</font></div><div><font style="font-size: 12px;">        else:</font></div><div><font style="font-size: 12px;">            zhizhen_2 = zhizhen_2-1</font></div><div><font style="font-size: 12px;">    return res[::-1]</font></div></div><div><span style="font-size: 12px;">最长公共子序列具体是啥，用dp做</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def LCS(self , s1 , s2 ):</font></div><div><font style="font-size: 12px;">        dp = [[0]*(len(s2)+1) for _ in range(len(s1)+1)]</font></div><div><font style="font-size: 12px;">        for i in range(1, len(s1)+1):</font></div><div><font style="font-size: 12px;">            for j in range(1, len(s2)+1):</font></div><div><font style="font-size: 12px;">                if s1[i-1]==s2[j-1]:</font></div><div><font style="font-size: 12px;">                    dp[i][j] = dp[i-1][j-1]+1</font></div><div><font style="font-size: 12px;">                else:</font></div><div><font style="font-size: 12px;">                    dp[i][j] = max(dp[i-1][j], dp[i][j-1])</font></div><div><font style="font-size: 12px;">       </font></div><div><font style="font-size: 12px;">        i = len(s1)</font></div><div><font style="font-size: 12px;">        j = len(s2)</font></div><div><font style="font-size: 12px;">        res = []</font></div><div><font style="font-size: 12px;">        while i&gt;0 and j&gt;0:</font></div><div><font style="font-size: 12px;">            if dp[i][j] == dp[i-1][j]: # 左</font></div><div><font style="font-size: 12px;">                i = i - 1</font></div><div><font style="font-size: 12px;">            elif dp[i][j] == dp[i][j-1]: # 上</font></div><div><font style="font-size: 12px;">                j = j - 1</font></div><div><font style="font-size: 12px;">            elif  dp[i][j] == dp[i-1][j-1]+1: # 左上。一定要先判断左，再判断上，再判断左上</font></div><div><font style="font-size: 12px;">                res.append(s1[i-1])</font></div><div><font style="font-size: 12px;">                i = i - 1</font></div><div><font style="font-size: 12px;">                j = j - 1</font></div><div><font style="font-size: 12px;">        res.reverse()</font></div><div><font style="font-size: 12px;">        if len(res)==0:</font></div><div><font style="font-size: 12px;">            return -1</font></div><div><font style="font-size: 12px;">        else:</font></div><div><font style="font-size: 12px;">            return ''.join(res)</font></div></div><div><span style="font-size: 12px; color: rgb(255, 38, 0);">输出所有最长公共子序列</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">text1 = "ABCBDAB"</font></div><div><font style="font-size: 12px;">text2 = "BDCABA"</font></div><div><font style="font-size: 12px;">ans = set()</font></div><div><font style="font-size: 12px;">def longestPalindromeSubseq(text1, text2):</font></div><div><font style="font-size: 12px;">    dp = [[0] * (len(text2) + 1) for _ in range(len(text1) + 1)]</font></div><div><font style="font-size: 12px;">    for i in range(1, len(text1) + 1):</font></div><div><font style="font-size: 12px;">        for j in range(1, len(text2) + 1):</font></div><div><font style="font-size: 12px;">            if text1[i - 1] == text2[j - 1]:</font></div><div><font style="font-size: 12px;">                dp[i][j] = dp[i - 1][j - 1] + 1</font></div><div><font style="font-size: 12px;">            else:</font></div><div><font style="font-size: 12px;">                dp[i][j] = max(dp[i - 1][j], dp[i][j - 1])</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">    lcs_str = ''</font></div><div><font style="font-size: 12px;">    backtrack(len(text1), len(text2), lcs_str, dp[-1][-1], text1, text2, dp)</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">    return list(ans)</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">def backtrack(i, j, lcs_str, lcs_len, text1, text2, dp):</font></div><div><font style="font-size: 12px;">    while i &gt; 0 and j &gt; 0:</font></div><div><font style="font-size: 12px;">        if text1[i - 1] == text2[j - 1]:</font></div><div><font style="font-size: 12px;">            lcs_str += text1[i - 1]</font></div><div><font style="font-size: 12px;">            i -= 1</font></div><div><font style="font-size: 12px;">            j -= 1</font></div><div><font style="font-size: 12px;">        else:</font></div><div><font style="font-size: 12px;">            if dp[i - 1][j] &gt; dp[i][j - 1]:</font></div><div><font style="font-size: 12px;">                i -= 1</font></div><div><font style="font-size: 12px;">            elif dp[i - 1][j] &lt; dp[i][j - 1]:</font></div><div><font style="font-size: 12px;">                j -= 1</font></div><div><font style="font-size: 12px;">            else:</font></div><div><font style="font-size: 12px;">                backtrack(i-1, j, lcs_str, lcs_len, text1, text2, dp)</font></div><div><font style="font-size: 12px;">                backtrack(i, j-1, lcs_str, lcs_len, text1, text2, dp)</font></div><div><font style="font-size: 12px;">                return</font></div><div><font style="font-size: 12px;">    if len(lcs_str)==lcs_len:</font></div><div><font style="font-size: 12px;">        tmp = lcs_str[::-1]</font></div><div><font style="font-size: 12px;">        ans.add(tmp)</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">print(longestPalindromeSubseq(text1, text2))</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px; color: rgb(255, 38, 0);">最短公共超序列</span></div><div><span style="font-size: 12px;">给你两个字符串 str1 和 str2，返回同时以 str1 和 str2 作为 子序列 的最短字符串。</span></div><div><span style="font-size: 12px;">输入：str1 = "abac", str2 = "cab"</span></div><div><span style="font-size: 12px;">输出："cabac"</span></div><div><span style="font-size: 12px;">解释：</span></div><div><span style="font-size: 12px;">str1 = "abac" 是 "cabac" 的一个子串，因为我们可以删去 "cabac" 的第一个 "c"得到 "abac"。</span></div><div><span style="font-size: 12px;">str2 = "cab" 是 "cabac" 的一个子串，因为我们可以删去 "cabac" 末尾的 "ac" 得到 "cab"。</span></div><div><span style="font-size: 12px;">最终我们给出的答案是满足上述属性的最短字符串。</span></div><div><span style="font-size: 12px;">思路：求最长公共子序列+两个序列各自独有的部分</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">class Solution:</font></div><div><font style="font-size: 12px;">    def shortestCommonSupersequence(self, s1: str, s2: str) -&gt; str:</font></div><div><font style="font-size: 12px;">        n, m = len(s1), len(s2)</font></div><div><font style="font-size: 12px;">        s1 = " " + s1</font></div><div><font style="font-size: 12px;">        s2 = " " + s2</font></div><div><font style="font-size: 12px;">        f = [[0] * (m + 10) for _ in range(n + 10)]</font></div><div><font style="font-size: 12px;">        for i in range(1, n + 1):</font></div><div><font style="font-size: 12px;">            for j in range(1, m + 1):</font></div><div><font style="font-size: 12px;">                f[i][j] = f[i - 1][j - 1] + 1 if s1[i] == s2[j] else max(f[i - 1][j], f[i][j - 1])</font></div><div><font style="font-size: 12px;">        ans = ""</font></div><div><font style="font-size: 12px;">        i, j = n, m</font></div><div><font style="font-size: 12px;">        while i &gt; 0 or j &gt; 0:</font></div><div><font style="font-size: 12px;">            if i == 0:</font></div><div><font style="font-size: 12px;">                ans += s2[j]</font></div><div><font style="font-size: 12px;">                j -= 1</font></div><div><font style="font-size: 12px;">            elif j == 0:</font></div><div><font style="font-size: 12px;">                ans += s1[i]</font></div><div><font style="font-size: 12px;">                i -= 1</font></div><div><font style="font-size: 12px;">            else:</font></div><div><font style="font-size: 12px;">                if s1[i] == s2[j]:</font></div><div><font style="font-size: 12px;">                    ans += s1[i]</font></div><div><font style="font-size: 12px;">                    i -= 1</font></div><div><font style="font-size: 12px;">                    j -= 1</font></div><div><font style="font-size: 12px;">                elif f[i][j] == f[i - 1][j]:</font></div><div><font style="font-size: 12px;">                    ans += s1[i]</font></div><div><font style="font-size: 12px;">                    i -= 1</font></div><div><font style="font-size: 12px;">                else:</font></div><div><font style="font-size: 12px;">                    ans += s2[j]</font></div><div><font style="font-size: 12px;">                    j -= 1</font></div><div><font style="font-size: 12px;">        return ans[::-1]</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">24.不相交的线（最大公共子序列）</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">在两条独立的水平线上按给定的顺序写下 nums1 和 nums2 中的整数。</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">现在，可以绘制一些连接两个数字 nums1[i] 和 nums2[j] 的直线，这些直线需要同时满足满足： </span></div><ul><li><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">nums1[i] == nums2[j]</span></div></li><li><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">且绘制的直线不与任何其他连线（非水平线）相交。</span></div></li></ul><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">请注意，连线即使在端点也不能相交：每个数字只能属于一条连线。</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">以这种方法绘制线条，并返回可以绘制的最大连线数。</span></div><div><span style="font-size: 12px;"><img src="%E5%8A%A8%E6%80%81%E8%A7%84%E5%88%92.resources/5A857405-6CB5-4150-9BEC-2F1F88C2C472.png" height="872" width="1252"/><br/></span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">思路：实际上，就是求两个数组的最长公共子序列。</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">dp[i][j]：num1的前i个字符和num2的前j个字符的最长公共子序列。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font face="Courier New" style="font-size: 12px;">def maxUncrossedLines(nums1, nums2):</font></div><div><font face="Courier New" style="font-size: 12px;">    len1 = len(nums1) + 1</font></div><div><font face="Courier New" style="font-size: 12px;">    len2 = len(nums2) + 1</font></div><div><font face="Courier New" style="font-size: 12px;">    dp = [[0] * len2 for _ in range(len1)]  # 注意这里的顺序要和for循环一致。for循环是先行再列，所以这里要把len1当成行</font></div><div><font face="Courier New" style="font-size: 12px;">    for i in range(1, len2):</font></div><div><font face="Courier New" style="font-size: 12px;">        for j in range(1, len1):</font></div><div><font face="Courier New" style="font-size: 12px;">            if nums2[i - 1] == nums1[j - 1]:</font></div><div><font face="Courier New" style="font-size: 12px;">                dp[i][j] = dp[i - 1][j - 1] + 1  # 这个+1，可以看作是：加一个“”</font></div><div><font face="Courier New" style="font-size: 12px;">            else:</font></div><div><font face="Courier New" style="font-size: 12px;">                dp[i][j] = max(dp[i][j - 1], dp[i - 1][j]) # 这里取最大，是因为不知道num1和num2的长度哪个长。如果确定             len(nums1)&lt;len(nums2)，则改为dp[i][j]=dp[i][j-1]</font></div><div><font face="Courier New" style="font-size: 12px;">    return dp[-1][-1]</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">25.最大子数组和（简单题）</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">给你一个整数数组 nums ，请你找出一个具有最大和的连续子数组（子数组最少包含一个元素），返回其最大和。</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">子数组是数组中的一个连续部分。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def FindGreatestSumOfSubArray(array):</font></div><div><font style="font-size: 12px;">        dp = [0]*len(array)</font></div><div><font style="font-size: 12px;">        dp[0] = array[0]</font></div><div><font style="font-size: 12px;">        for i in range(1, len(array)):</font></div><div><font style="font-size: 12px;">            dp[i] = max(dp[i-1]+array[i], array[i])</font></div><div><font style="font-size: 12px;">        return max(dp)  # 注意！！这里是max</font></div></div><div><span style="font-size: 12px;">压缩空间：</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">def maxSubArray(self, nums):</font></div><div><font style="font-size: 12px;">    ans = dp_i_1</font></div><div><font style="font-size: 12px;">    dp_i_1 = nums[0]</font></div><div><font style="font-size: 12px;">    for i in range(1, len(nums)):</font></div><div><font style="font-size: 12px;">        dp_i = max(dp_i_1 + nums[i], nums[i])</font></div><div><font style="font-size: 12px;">        dp_i_1 = dp_i</font></div><div><font style="font-size: 12px;">        ans = max(ans, dp_i_1)</font></div><div><font style="font-size: 12px;">    return ans</font></div></div><div><font style="font-size: 12px;"><span style="font-size: 12px; color: unset;">最大子数组和进阶版：<span style="font-size: 12px; color: unset; font-weight: bold;">子数组具体是啥？</span></span><span style="font-size: 12px; color: unset; font-weight: bold;">可能有多个最大和子数组，返回最长的那个</span></font></div><div><span style="font-size: 12px;">思路：定义四个指针。当前left，当前right。最长数组的left，最长的right，记录当前最大和。 </span></div><div><span style="font-size: 12px;">1.按照正常的算dp[i]，然后更新right。 </span></div><div><span style="font-size: 12px;">2.如果当前和已经为负数，更新left=right。   </span></div><div><span style="font-size: 12px;">3.每次循环都判断，当前的right-left是否超越了最大长度的resr-resl，超越了就更新。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">def FindGreatestSumOfSubArray(array):</font></div><div><font style="font-size: 12px;">    dp = [0]*len(array)</font></div><div><font style="font-size: 12px;">    dp[0] = array[0]</font></div><div><font style="font-size: 12px;">    maxsum = dp[0]</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">    left = 0 </font></div><div><font style="font-size: 12px;">    right = 0  </font></div><div><font style="font-size: 12px;">    resl = 0 # 最长的区间的left</font></div><div><font style="font-size: 12px;">    resr = 0 # 最长的区间的right</font></div><div><font style="font-size: 12px;">    for i in range(1, len(array)):</font></div><div><font style="font-size: 12px;">        dp[i] = max(dp[i - 1] + array[i], array[i])</font></div><div><font style="font-size: 12px;">        # 1.当前右指针右移动</font></div><div><font style="font-size: 12px;">        right = right + 1</font></div><div><font style="font-size: 12px;">        # 2.如果当前和小于0，left=right区间归0</font></div><div><font style="font-size: 12px;">        if dp[i - 1] + array[i] &lt; array[i]:</font></div><div><font style="font-size: 12px;">            left = right</font></div><div><font style="font-size: 12px;">        # 出现了更大的和 或 （和相等，当前right-left长度是否超过了resr-resl）</font></div><div><font style="font-size: 12px;">        if dp[i] &gt; maxsum or (dp[i] == maxsum and (right - left + 1) &gt; (resr - resl + 1)):</font></div><div><font style="font-size: 12px;">            maxsum = dp[i]</font></div><div><font style="font-size: 12px;">            resl = left</font></div><div><font style="font-size: 12px;">            resr = right</font></div><div><font style="font-size: 12px;">    # 取结果</font></div><div><font style="font-size: 12px;">    res = []</font></div><div><font style="font-size: 12px;">    for i in range(resl, resr + 1):</font></div><div><font style="font-size: 12px;">        res.append(array[i])</font></div><div><font style="font-size: 12px;">    return res</font></div></div><div><span style="font-size: 12px;">拓展思路：最大数组和也能用前缀和子路解决。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">def maxSubArray(self, array):</font></div><div><font style="font-size: 12px;">    n = len(array)</font></div><div><font style="font-size: 12px;">    pre_sum = [0] * (n + 1)  # 前i个元素的和为pre_sum[i]</font></div><div><font style="font-size: 12px;">    pre_sum[0] = 0  # 前0个元素的和为0</font></div><div><font style="font-size: 12px;">    for i in range(1, n + 1):</font></div><div><font style="font-size: 12px;">        pre_sum[i] = pre_sum[i - 1] + array[i - 1]</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">    min_pre = float('inf')  # 前缀和的最小值</font></div><div><font style="font-size: 12px;">    ans = float('-inf')</font></div><div><font style="font-size: 12px;">    for i in range(n):</font></div><div><font style="font-size: 12px;">        min_pre = min(min_pre, pre_sum[i])  # 更新最小前缀和</font></div><div><font style="font-size: 12px;">        ans = max(ans, pre_sum[i + 1] - min_pre)  #</font></div><div><font style="font-size: 12px;">    return ans</font></div></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">最短无序连续子数组：部分排序</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">给你一个整数数组 nums ，你需要找出一个连续子数组 ，如果对这个子数组进行升序排序，那么整个数组都会变为升序排序。</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">输入：nums = [2,6,4,8,10,9,15]</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">输出：5 </span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">解释：你只需要对 [6, 4, 8, 10, 9] 进行升序排序，那么整个表都会变为升序排序。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def findUnsortedSubarray(self, nums):</font></div><div><font style="font-size: 12px;">        max_num = nums[0]</font></div><div><font style="font-size: 12px;">        min_num = nums[-1]</font></div><div><font style="font-size: 12px;">        right = -1</font></div><div><font style="font-size: 12px;">        left = 0</font></div><div><font style="font-size: 12px;">        for i in range(len(nums)): # 寻找上升序列的源头</font></div><div><font style="font-size: 12px;">            if nums[i]&gt;=max_num: # 说明在上升</font></div><div><font style="font-size: 12px;">                max_num = nums[i]</font></div><div><font style="font-size: 12px;">            else:                # 重新设定源头</font></div><div><font style="font-size: 12px;">                right = i</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">        for i in range(len(nums)-1, -1, -1): # 寻找下降序列的源头</font></div><div><font style="font-size: 12px;">            if nums[i]&lt;=min_num:</font></div><div><font style="font-size: 12px;">                min_num = nums[i]</font></div><div><font style="font-size: 12px;">            else:</font></div><div><font style="font-size: 12px;">                left = i</font></div><div><font style="font-size: 12px;">        return right-left+1</font></div></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">26.判断子序列（删除/跳过元素）：最长公共子序列 是否的等于 短的序列</span></div><div><font style="font-size: 12px;"><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">给定字符串 s 和 t ，判断 s 是否为 t 的子序列。</span><span style="font-size: 12px; color: unset; font-family: &quot;Courier New&quot;;">字符串的一个子序列是原始字符串删除一些（也可以不删除）字符而不改变剩余字符相对位置形成的新字符串。（例如，"ace"是"abcde"的一个子序列，而"aec"不是）。</span></font></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">示例1： 输入：s = "abc", t = "ahbgdc" 输出：true</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def isSubsequence(self, s, t):</font></div><div><font style="font-size: 12px;">        # s是短的，t是长的</font></div><div><font style="font-size: 12px;">        dp = [[0]*(len(t)+1) for _ in range(len(s)+1)]</font></div><div><font style="font-size: 12px;">        for i in range(1, len(s)+1):</font></div><div><font style="font-size: 12px;">            for j in range(1, len(t)+1):</font></div><div><font style="font-size: 12px;">                if s[i-1]==t[j-1]:</font></div><div><font style="font-size: 12px;">                    dp[i][j] = dp[i-1][j-1] + 1</font></div><div><font style="font-size: 12px;">                else:</font></div><div><font style="font-size: 12px;">                    dp[i][j] = dp[i][j-1]  <b># 代表删除/跳过t[j-1],剩下的继续匹配。这是一个重点！</b></font></div><div><font style="font-size: 12px;">        return dp[-1][-1]==len(s)</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">27.不同的子序列 rabbbit, rabbit</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">给定一个字符串 s 和一个字符串 t ，计算在 s 的子序列中 t 出现的个数。</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">字符串的一个 子序列 是指，通过删除一些（也可以不删除）字符且不干扰剩余字符相对位置所组成的新字符串。（例如，"ACE" 是 "ABCDE" 的一个子序列，而 "AEC" 不是）</span></div><div><span style="font-size: 12px;"><img src="%E5%8A%A8%E6%80%81%E8%A7%84%E5%88%92.resources/7F509349-7F5C-45C9-BECF-9A08E8BD8D3D.png" height="288" width="724"/><br/></span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">dp[i][j] = dp[i-1][j-1] + dp[i-1][j]：因为统计的是“有几种情况”，所以不会和上面的题一样+1。如果s[i-1]=t[j-1]，说明当前的s[i-1]可用，这时考虑两种情况：1.用 2.弃用（就是删除），让别的字符和t[j-1]匹配。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font face="Courier New" style="font-size: 12px;">def numDistinct(s, t):</font></div><div><font face="Courier New" style="font-size: 12px;">    len1 = len(s) + 1</font></div><div><font face="Courier New" style="font-size: 12px;">    len2 = len(t) + 1</font></div><div><font face="Courier New" style="font-size: 12px;">    dp = [[0]*len2 for _ in range(len1)]</font></div><div><font face="Courier New" style="font-size: 12px;">    for i in range(len1):</font></div><div><font face="Courier New" style="font-size: 12px;">        dp[i][0] = 1</font></div><div><font face="Courier New" style="font-size: 12px;">    for i in range(1, len1):</font></div><div><font face="Courier New" style="font-size: 12px;">        for j in range(1, len2):</font></div><div><font face="Courier New" style="font-size: 12px;">            if s[i-1] == t[j-1]:</font></div><div><font face="Courier New" style="font-size: 12px;">                dp[i][j] = dp[i-1][j-1] + dp[i-1][j]   </font></div><div><font face="Courier New" style="font-size: 12px;">            else:</font></div><div><font face="Courier New" style="font-size: 12px;">                dp[i][j] = dp[i-1][j]</font></div><div><font face="Courier New" style="font-size: 12px;">    return dp[-1][-1]</font></div></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">28.两个字符串的删除操作</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">给定两个单词 word1 和 word2 ，返回使得 word1 和  word2 相同所需的最小步数。</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">每步可以删除任意一个字符串中的一个字符。</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">输入: word1 = "sea", word2 = "eat"</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">输出: 2</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">解释: 第一步将 "sea" 变为 "ea" ，第二步将 "eat "变为 "ea"</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">思路：转化为最长公共子序列问题求解</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font face="Courier New" style="font-size: 12px;">def minDistance(self, word1, word2):</font></div><div><font face="Courier New" style="font-size: 12px;">    len1 = len(word1) + 1</font></div><div><font face="Courier New" style="font-size: 12px;">    len2 = len(word2) + 1</font></div><div><font face="Courier New" style="font-size: 12px;">    dp = [[0] * len2 for _ in range(len1)]</font></div><div><font face="Courier New" style="font-size: 12px;">    for i in range(1, len1):</font></div><div><font face="Courier New" style="font-size: 12px;">        for j in range(1, len2):</font></div><div><font face="Courier New" style="font-size: 12px;">            if word1[i - 1] == word2[j - 1]:</font></div><div><font face="Courier New" style="font-size: 12px;">                dp[i][j] = dp[i - 1][j - 1] + 1</font></div><div><font face="Courier New" style="font-size: 12px;">            else:</font></div><div><font face="Courier New" style="font-size: 12px;">                dp[i][j] = max(dp[i - 1][j], dp[i][j - 1])</font></div><div><font face="Courier New" style="font-size: 12px;">    return len1 + len2 - 2 * dp[-1][-1] - 2</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">29.编辑距离</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">是给你两个单词 word1 和 word2， 请返回将 word1 转换成 word2 所使用的最少操作数。</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">你可以对一个单词进行如下三种操作：</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">插入一个字符</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">删除一个字符</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">替换一个字符</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">示例 1： 输入：word1 = "horse", word2 = "ros" 输出：3 解释： horse -&gt; rorse (将 'h' 替换为 'r') rorse -&gt; rose (删除 'r') rose -&gt; ros (删除 'e’)。</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">dp[i][j]：表示以下标i-1为结尾的字符串word1，和以下标j-1为结尾的字符串word2，最近编辑距离为dp[i][j]。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font face="Courier New" style="font-size: 12px;">def minDistance(word1, word2):</font></div><div><font style="font-size: 12px;"><font face="Courier New">    dp = [[0] * (len(word2)+1) for _ in range(len(word1)+1)] # </font>表示以下标i-1为结尾的字符串word1，和以下标j-1为结尾的字符串word2，最近编辑距离为dp[i][j]。</font></div><div><font face="Courier New" style="font-size: 12px;">    for i in range(len(word1)+1):</font></div><div><font face="Courier New" style="font-size: 12px;">        dp[i][0] = i</font></div><div><font face="Courier New" style="font-size: 12px;">    for j in range(len(word2)+1):</font></div><div><font face="Courier New" style="font-size: 12px;">        dp[0][j] = j</font></div><div><font face="Courier New" style="font-size: 12px;">    for i in range(1, len(word1)+1):</font></div><div><font face="Courier New" style="font-size: 12px;">        for j in range(1, len(word2)+1):</font></div><div><font face="Courier New" style="font-size: 12px;">            if word1[i-1] == word2[j-1]:</font></div><div><font face="Courier New" style="font-size: 12px;">                dp[i][j] = dp[i-1][j-1]</font></div><div><font face="Courier New" style="font-size: 12px;">            else:</font></div><div><font face="Courier New" style="font-size: 12px;">                dp[i][j] = min(dp[i-1][j-1], dp[i-1][j], dp[i][j-1]) + 1 # 替换，word1删除1个元素，word2删除1个元素</font></div><div><font face="Courier New" style="font-size: 12px;">                <font color="#FF2600"># 解释：dp[i][j]=dp[i-1][j]+1，意思是直接删除word1的第i个元素，所以匹配word1的前i个元素就等于匹配word1的前i-1个元素+1</font></font></div><div><font face="Courier New" style="font-size: 12px;">    return dp[-1][-1]</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px;">编辑距离（二）：插入、删除、替换，有不同的代价</span></div><div><span style="font-size: 12px;">输入："abc","adc",5,3,2。 插入的代价是5，删除是3，替换是2。求最小代价。</span></div><div><span style="font-size: 12px;">返回值：2</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def minEditCost(self , word1, word2, ic, dc, rc):</font></div><div><font style="font-size: 12px;">        dp = [[0] * (len(word2)+1) for _ in range(len(word1)+1)]</font></div><div><font style="font-size: 12px;">        for i in range(len(word1)+1):</font></div><div><font style="font-size: 12px;">            dp[i][0] = i*dc</font></div><div><font style="font-size: 12px;">        for j in range(len(word2)+1):</font></div><div><font style="font-size: 12px;">            dp[0][j] = j*ic</font></div><div><font style="font-size: 12px;">        for i in range(1, len(word1)+1):</font></div><div><font style="font-size: 12px;">            for j in range(1, len(word2)+1):</font></div><div><font style="font-size: 12px;">                if word1[i-1] == word2[j-1]:</font></div><div><font style="font-size: 12px;">                    dp[i][j] = dp[i-1][j-1]</font></div><div><font style="font-size: 12px;">                else:</font></div><div><font style="font-size: 12px;">                    # 理解：dp[i][j]删除一个元素，变为dp[i-1][j]</font></div><div><font style="font-size: 12px;">                    dp[i][j] = min(dp[i-1][j-1]+rc, dp[i-1][j]+dc, dp[i][j-1]+ic)</font></div><div><font style="font-size: 12px;">        return dp[-1][-1]</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">30.回文子串的个数，</span><span style="font-size: 12px;">回文子串个数</span></div><div><font style="font-size: 12px;"><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">给你一个字符串 s ，请你统计并返回这个字符串中回文子串的数目。</span><span style="font-size: 12px; color: unset; font-family: &quot;Courier New&quot;;">具有不同开始位置或结束位置的子串，即使是由相同的字符组成，也会被视作不同的子串。</span></font></div><div><span style="font-size: 12px; color: unset; font-family: &quot;Courier New&quot;;">思路：</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">情况一：下标i 与 j相同，同一个字符例如a，当然是回文子串</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">情况二：下标i 与 j相差为1，例如aa，也是回文子串 </span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">情况三：下标：i 与 j相差大于1的时候，例如cabac，此时s[i]与s[j]已经相同了，我们看i到j区间是不是回文子串就看aba是不是回文就可以了，那么aba的区间就是 i+1 与 j-1区间，这个区间是不是回文就看dp[i + 1][j - 1]是否为true。</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">dp[i][j]表示s在i到j是不是回文串</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">技巧：判断遍历顺序：因为dp[i][j]是要由dp[i+1][j-1]来得出，所以dp[i+1][j-1]必须提前计算过，所以i逆序遍历，j正序遍历。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font face="Courier New" style="font-size: 12px;">def countSubstrings(s):</font></div><div><font face="Courier New" style="font-size: 12px;">    dp = [[False] * len(s) for _ in range(len(s))]</font></div><div><font face="Courier New" style="font-size: 12px;">    result = 0</font></div><div><font face="Courier New" style="font-size: 12px;">    for i in range(len(s) - 1, -1, -1):  # 注意遍历顺序</font></div><div><font face="Courier New" style="font-size: 12px;">        for j in range(i, len(s)):</font></div><div><font face="Courier New" style="font-size: 12px;">            if s[i] == s[j]:</font></div><div><font face="Courier New" style="font-size: 12px;">                if j - i &lt;= 1:  # 情况一 和 情况二</font></div><div><font face="Courier New" style="font-size: 12px;">                    result += 1</font></div><div><font face="Courier New" style="font-size: 12px;">                    dp[i][j] = True</font></div><div><font face="Courier New" style="font-size: 12px;">                elif dp[i + 1][j - 1] == True:  # 情况三</font></div><div><font face="Courier New" style="font-size: 12px;">                    result += 1</font></div><div><font face="Courier New" style="font-size: 12px;">                    dp[i][j] = True</font></div><div><font face="Courier New" style="font-size: 12px;">    return result</font></div></div><div><span style="font-size: 12px; color: rgb(255, 38, 0);">31.最多删除一个字符能否得到回文</span></div><div><font style="font-size: 12px;"><span style="font-size: 12px; color: rgb(255, 38, 0);">思路：双指针。如果low和high相等，就继续走；如果不等，选择删左或者删右，即判断s[</span><span style="font-size: 12px; color: rgb(255, 38, 0);">low+1:high</span><span style="font-size: 12px; color: rgb(255, 38, 0);">]或者s[low,high+1]是不是回文，只要有一个是，就返回True</span></font></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">def validPalindrome(s):</font></div><div><font style="font-size: 12px;">    def check(low, high): # 正常的双指针判断回文</font></div><div><font style="font-size: 12px;">        i, j = low, high</font></div><div><font style="font-size: 12px;">        while i &lt; j:</font></div><div><font style="font-size: 12px;">            if s[i] != s[j]:</font></div><div><font style="font-size: 12px;">                return False</font></div><div><font style="font-size: 12px;">            i += 1</font></div><div><font style="font-size: 12px;">            j -= 1</font></div><div><font style="font-size: 12px;">        return True</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">    low, high = 0, len(s) - 1</font></div><div><font style="font-size: 12px;">    while low &lt; high:</font></div><div><font style="font-size: 12px;">        if s[low] == s[high]:</font></div><div><font style="font-size: 12px;">            low += 1</font></div><div><font style="font-size: 12px;">            high -= 1</font></div><div><font style="font-size: 12px;">        else:</font></div><div><font style="font-size: 12px;">            return check(low + 1, high) or check(low, high - 1)</font></div><div><font style="font-size: 12px;">    return True</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">32.最长回文子序列</span></div><div><span style="font-size: 12px; color: rgb(255, 38, 0); font-family: &quot;Courier New&quot;;">注意：本题是要求最长回文子序列，不是回文子串，不要求连续。 最长回文子串可以轻松由上题代码修改得到</span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">给你一个字符串 s ，找出其中最长的回文子序列，并返回该序列的长度。</span></div><div><span style="font-size: 12px; color: unset; font-family: &quot;Courier New&quot;;">子序列定义为：不改变剩余字符顺序的情况下，删除某些字符或者不删除任何字符形成的一个序列。</span></div><div><span style="font-size: 12px;"><img src="%E5%8A%A8%E6%80%81%E8%A7%84%E5%88%92.resources/2DFDB888-569A-4428-BEAD-3707C559CC69.png" height="220" width="622"/><br/></span></div><div><span style="font-size: 12px; font-family: &quot;Courier New&quot;;">思路：如果s[i]==s[j]，两个同时加入； 如果不相等，加入一个最大的。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font face="Courier New" style="font-size: 12px;">def longestPalindromeSubseq(s):</font></div><div><font face="Courier New" style="font-size: 12px;">    dp = [[0] * len(s) for _ in range(len(s))]</font></div><div><font face="Courier New" style="font-size: 12px;">    for i in range(len(s)):   # 一个元素的情况，全赋值为1</font></div><div><font face="Courier New" style="font-size: 12px;">        dp[i][i] = 1</font></div><div><font face="Courier New" style="font-size: 12px;">    for i in range(len(s)-1, -1, -1): </font></div><div><font face="Courier New" style="font-size: 12px;">        for j in range(i+1, len(s)):</font></div><div><font face="Courier New" style="font-size: 12px;">            if s[i] == s[j]:</font></div><div><font face="Courier New" style="font-size: 12px;">                dp[i][j] = dp[i+1][j-1] + 2  # 因为这里是+2，所以s[i]==s[i]的情况要单独考虑</font></div><div><font face="Courier New" style="font-size: 12px;">            else:</font></div><div><font face="Courier New" style="font-size: 12px;">                dp[i][j] = max(dp[i+1][j], dp[i][j-1])</font></div><div><font face="Courier New" style="font-size: 12px;">    return dp[0][-1]</font></div></div><div><span style="font-size: 12px; font-weight: bold;">方法二：将s反转，判断s和反转后的s，最长公共子序列。这样做不光能求长度，还能求具体是啥</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">def longestPalindromeSubseq(s):</font></div><div><font style="font-size: 12px;">    dp = [[0] * (len(s) + 1) for _ in range(len(s) + 1)]</font></div><div><font style="font-size: 12px;">    text1 = list(s)</font></div><div><font style="font-size: 12px;">    text2 = text1[::-1]</font></div><div><font style="font-size: 12px;">    for i in range(1, len(text1) + 1):</font></div><div><font style="font-size: 12px;">        for j in range(1, len(text2) + 1):</font></div><div><font style="font-size: 12px;">            if text1[i - 1] == text2[j - 1]:</font></div><div><font style="font-size: 12px;">                dp[i][j] = dp[i - 1][j - 1] + 1</font></div><div><font style="font-size: 12px;">            else:</font></div><div><font style="font-size: 12px;">                dp[i][j] = max(dp[i - 1][j], dp[i][j - 1])</font></div><div><font style="font-size: 12px;">    return dp[-1][-1]</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px; font-weight: bold;">33.最长回文子串的长度（中心开花法）</span></div><div><span style="font-size: 12px; font-weight: bold;">1.遍历字符串的每个字符，从当前字符中心开始扩展，记录回文子串长度。  2.要注意，字符串长度为奇数、偶数的情况不一样</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def getLongestPalindrome(self , A):</font></div><div><font style="font-size: 12px;">        def fun(s, left, right): # 注意，是中心开花(求最远的left和right)</font></div><div><font style="font-size: 12px;">            while left&gt;=0 and right&lt;len(s): </font></div><div><font style="font-size: 12px;">                if s[left]==s[right]:</font></div><div><font style="font-size: 12px;">                    left = left-1</font></div><div><font style="font-size: 12px;">                    right = right+1</font></div><div><font style="font-size: 12px;">                else:</font></div><div><font style="font-size: 12px;">                    break</font></div><div><font style="font-size: 12px;">            return right-left-1 # 因为此时的s[left]不等于s[right]</font></div><div><font style="font-size: 12px;">        </font></div><div><font style="font-size: 12px;">        maxlen = 1</font></div><div><font style="font-size: 12px;">        for i in range(len(A)-1):</font></div><div><font style="font-size: 12px;">            maxlen = max(maxlen, max(fun(A, i, i), fun(A, i, i+1))) # 字符串长度为奇数、偶数都要考虑</font></div><div><font style="font-size: 12px;">        return maxlen</font></div></div><div><span style="font-size: 12px;">最长回文子串是啥（变个返回值即可）</span></div></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def longestPalindrome(self, nums):</font></div><div><font style="font-size: 12px;">        # 中心开花法</font></div><div><font style="font-size: 12px;">        def fun(s, left, right):</font></div><div><font style="font-size: 12px;">            while left&gt;=0 and right&lt;len(s):</font></div><div><font style="font-size: 12px;">                if s[left]==s[right]:</font></div><div><font style="font-size: 12px;">                    left = left-1</font></div><div><font style="font-size: 12px;">                    right = right+1</font></div><div><font style="font-size: 12px;">                else:</font></div><div><font style="font-size: 12px;">                    break</font></div><div><font style="font-size: 12px;">            return left+1, right-1</font></div><div><font style="font-size: 12px;">       </font></div><div><font style="font-size: 12px;">        maxlen = 1</font></div><div><font style="font-size: 12px;">        true_start = 0</font></div><div><font style="font-size: 12px;">        true_end = 0</font></div><div><font style="font-size: 12px;">        for i in range(len(nums)-1):</font></div><div><font style="font-size: 12px;">            left1, right1 = fun(nums, i, i)</font></div><div><font style="font-size: 12px;">            left2, right2 = fun(nums, i, i+1)</font></div><div><font style="font-size: 12px;">            if right1-left1&gt;true_end-true_start:</font></div><div><font style="font-size: 12px;">                true_start = left1</font></div><div><font style="font-size: 12px;">                true_end = right1</font></div><div><font style="font-size: 12px;">            if right2-left2&gt;true_end-true_start:</font></div><div><font style="font-size: 12px;">                true_start = left2</font></div><div><font style="font-size: 12px;">                true_end = right2</font></div><div><font style="font-size: 12px;">        return nums[true_start:true_end+1]</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px;">34.买票问题</span></div><div><span style="font-size: 12px; color: unset; font-family: unset;">现在有n个人排队买票，已知是早上8点开始卖票，这n个人买票有两种方式：</span></div><div><span style="font-size: 12px; color: unset; font-family: unset;">第一种是每一个人都可以单独去买自己的票，第 i 个人花费 a[i] 秒。</span></div><div><span style="font-size: 12px; color: unset; font-family: unset;">第二种是每一个人都可以选择和自己后面的人一起买票，第 i 个人和第 i+1 个人一共花费 b[i] 秒。</span></div><div><span style="font-size: 12px; color: unset; font-family: unset;">最后一个人只能和前面的人一起买票或单独买票。</span></div><div><span style="font-size: 12px; color: unset; font-family: unset;">由于卖票的地方想早些关门，所以他想知道他最早几点可以关门，请输出一个时间格式形如：08:00:40 am/pm</span></div><div><a href="https://www.nowcoder.com/questionTerminal/33086563178441bbaa9b502518cbc536" style="font-size: 12px;">https://www.nowcoder.com/questionTerminal/33086563178441bbaa9b502518cbc536</a></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">T = int(input()) # 测试用例个数</font></div><div><font style="font-size: 12px;">while T:</font></div><div><font style="font-size: 12px;">    str_n = input()</font></div><div><font style="font-size: 12px;">    if len(str_n)==0:</font></div><div><font style="font-size: 12px;">        continue</font></div><div><font style="font-size: 12px;">    n = int(str_n)</font></div><div><font style="font-size: 12px;">    a = list(map(int, input().split()))   # 自己买票的时间</font></div><div><font style="font-size: 12px;">    if len(a) == 1:</font></div><div><font style="font-size: 12px;">        h = a[0] // 3600 + 8       # 小时</font></div><div><font style="font-size: 12px;">        m = a[0] % 3600 // 60      # 分钟</font></div><div><font style="font-size: 12px;">        s = a[0] % 60              # 秒</font></div><div><font style="font-size: 12px;">    else:</font></div><div><font style="font-size: 12px;">        b = list(map(int, input().split())) # 和前面的人一起买的时间</font></div><div><font style="font-size: 12px;">        # f[i]为到第i个人为止，一共花费了多长时间</font></div><div><font style="font-size: 12px;">        f = [0]*n</font></div><div><font style="font-size: 12px;">        f[0] = a[0]</font></div><div><font style="font-size: 12px;">        f[1] = min(f[0] + a[1], b[0])</font></div><div><font style="font-size: 12px;">        for i in range(2, n):</font></div><div><font style="font-size: 12px;">            # 比较第i个人单独买票和与第i-1个人一起买票的时间，选择用时少的</font></div><div><font style="font-size: 12px;">            f[i] = min(f[i - 1] + a[i], f[i - 2] + b[i - 1])</font></div><div><font style="font-size: 12px;">        h = f[-1] // 3600 + 8       # 小时</font></div><div><font style="font-size: 12px;">        m = f[-1] % 3600 // 60      # 分钟</font></div><div><font style="font-size: 12px;">        s = f[-1] % 60              # 秒</font></div><div><font style="font-size: 12px;">    is_am = True</font></div><div><font style="font-size: 12px;">    if h &gt; 12:</font></div><div><font style="font-size: 12px;">        h -= 12</font></div><div><font style="font-size: 12px;">        is_am = False</font></div><div><font style="font-size: 12px;">    am_or_pm = "am" if is_am else "pm"</font></div><div><font style="font-size: 12px;">    #print("%02d:%02d:%02d %s" % (h, m, s, am_or_pm))</font></div><div><font style="font-size: 12px;">    T -= 1</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px;">35.美团笔试</span></div><div><span style="font-size: 12px;">链接：</span><a href="https://www.nowcoder.com/questionTerminal/1fa6acea537e499a9555cfe76ec24719" style="font-size: 12px;">https://www.nowcoder.com/questionTerminal/1fa6acea537e499a9555cfe76ec24719</a></div><div><span style="font-size: 12px;">来源：牛客网</span></div><div><font style="font-size: 12px;"><br/></font></div><div style="margin-top: 1em; margin-bottom: 1em;"><font style="font-size: 12px;"><span style="font-size: 12px;-en-paragraph:true;">小团正在装饰自己的书桌，他的书桌上从左到右有</span><span style="font-size: 12px;-en-paragraph:true;">m</span><span style="font-size: 12px;-en-paragraph:true;">个空位需要放上装饰物。</span><span style="font-size: 12px; color: red;-en-paragraph:true;">商店中每个整数价格的装饰物恰好有一种，且每种装饰物的数量无限多。</span></font></div><div style="margin-top: 1em; margin-bottom: 1em;"><font style="font-size: 12px;"><span style="font-size: 12px;-en-paragraph:true;">小团去商店的时候，想到了一个购买方案，他要让右边的装饰物价格是左边的倍数。用数学语言来说，假设小团的</span><span style="font-size: 12px;-en-paragraph:true;">m</span><span style="font-size: 12px;-en-paragraph:true;">个装饰物价格为</span><span style="font-size: 12px;-en-paragraph:true;">a1,a2,...,ama_1,a_2,...,a_m</span><span style="font-size: 12px;-en-paragraph:true;">a</span><span style="height: 0.301108em;top: -2.55em;font-size: 12px;-en-paragraph:true;">1</span><span style="font-size: 12px;-en-paragraph:true;">,</span><span style="font-size: 12px;-en-paragraph:true;">a</span><span style="height: 0.301108em;top: -2.55em;font-size: 12px;-en-paragraph:true;">2</span><span style="font-size: 12px;-en-paragraph:true;">,</span><span style="font-size: 12px;-en-paragraph:true;">.</span><span style="font-size: 12px;-en-paragraph:true;">.</span><span style="font-size: 12px;-en-paragraph:true;">.</span><span style="font-size: 12px;-en-paragraph:true;">,</span><span style="font-size: 12px;-en-paragraph:true;">a</span><span style="height: 0.151392em;top: -2.55em;font-size: 12px;-en-paragraph:true;">m</span><span style="font-size: 12px;-en-paragraph:true;">，那么对于任意的</span><span style="font-size: 12px;-en-paragraph:true;">1</span><span style="font-size: 12px;-en-paragraph:true;">≤</span><span style="font-size: 12px;-en-paragraph:true;">i</span><span style="font-size: 12px;-en-paragraph:true;">≤</span><span style="font-size: 12px;-en-paragraph:true;">j</span><span style="font-size: 12px;-en-paragraph:true;">≤</span><span style="font-size: 12px;-en-paragraph:true;">m</span><span style="font-size: 12px;-en-paragraph:true;">，</span><span style="font-size: 12px;-en-paragraph:true;">aja_j</span><span style="font-size: 12px;-en-paragraph:true;">a</span><span style="height: 0.311664em;top: -2.55em;font-size: 12px;-en-paragraph:true;">j</span><span style="font-size: 12px;-en-paragraph:true;">是</span><span style="font-size: 12px;-en-paragraph:true;">aia_i</span><span style="font-size: 12px;-en-paragraph:true;">a</span><span style="height: 0.311664em;top: -2.55em;font-size: 12px;-en-paragraph:true;">i</span><span style="font-size: 12px;-en-paragraph:true;">的倍数。</span></font></div><div style="margin-top: 1em; margin-bottom: 1em;"><font style="font-size: 12px;"><span style="font-size: 12px;-en-paragraph:true;">小团是一个节约的人，他希望最贵的装饰物不超过</span><span style="font-size: 12px;-en-paragraph:true;">n</span><span style="font-size: 12px;-en-paragraph:true;">元。现在，请你计算小团有多少种购买的方案？</span></font></div><div style="margin-top: 1em; margin-bottom: 1em;"><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    n,m=map(int, input().strip().split())</font></div><div><font style="font-size: 12px;">    # 使得第i个物品，价格为j，有几种方案。</font></div><div><font style="font-size: 12px;">    # 比如第二个物品dp[2][4]=dp[1][1]+dp[1][2]+dp[1][4]</font></div><div><font style="font-size: 12px;">    dp=[[0 for _ in range(n+1)] for _ in range(m+1)]</font></div><div><font style="font-size: 12px;">    res=0</font></div><div><font style="font-size: 12px;">    for j in range(n+1):  # 第一个物品，价格无论为多少，都是一种方案</font></div><div><font style="font-size: 12px;">        dp[1][j]=1</font></div><div><font style="font-size: 12px;">    for i in range(2,m+1):</font></div><div><font style="font-size: 12px;">        for j in range(1,n+1):</font></div><div><font style="font-size: 12px;">            for time in range(1,n//j+1):</font></div><div><font style="font-size: 12px;">                dp[i][j*time]+=dp[i-1][j]</font></div><div><font style="font-size: 12px;">    for j in range(1,n+1): # 对于第m个物品来说，有几种</font></div><div><font style="font-size: 12px;">        res+=dp[m][j]</font></div><div><font style="font-size: 12px;">    print(res)</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px;">36.矩阵的最小路径和</span></div><div><span style="font-size: 12px;">给定一个 n * m 的矩阵 a，从左上角开始每次只能向右或者向下走，最后到达右下角的位置，路径上所有的数字累加起来就是路径和，输出所有的路径中最小的路径和。</span></div><div><span style="font-size: 12px;"><img src="%E5%8A%A8%E6%80%81%E8%A7%84%E5%88%92.resources/3E7AAEF2-F8A0-493C-9508-FC8A7E470172.png" height="393" width="392"/><br/></span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def minPathSum(self , grid):</font></div><div><font style="font-size: 12px;">        if len(grid)==0 or len(grid[0])==0:</font></div><div><font style="font-size: 12px;">            return 0</font></div><div><font style="font-size: 12px;">       </font></div><div><font style="font-size: 12px;">        rows, columns = len(grid), len(grid[0])</font></div><div><font style="font-size: 12px;">        dp = [[0] * columns for _ in range(rows)]</font></div><div><font style="font-size: 12px;">        dp[0][0] = grid[0][0]</font></div><div><font style="font-size: 12px;">        for i in range(1, rows):</font></div><div><font style="font-size: 12px;">            dp[i][0] = dp[i - 1][0] + grid[i][0]</font></div><div><font style="font-size: 12px;">        for j in range(1, columns):</font></div><div><font style="font-size: 12px;">            dp[0][j] = dp[0][j - 1] + grid[0][j]</font></div><div><font style="font-size: 12px;">        for i in range(1, rows):</font></div><div><font style="font-size: 12px;">            for j in range(1, columns):</font></div><div><font style="font-size: 12px;">                dp[i][j] = min(dp[i - 1][j], dp[i][j - 1]) + grid[i][j]</font></div><div><font style="font-size: 12px;">       </font></div><div><font style="font-size: 12px;">        return dp[-1][-1]</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px; font-weight: bold;">37.把数字翻译成子串</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def solve(self , nums: str) -&gt; int:</font></div><div><font style="font-size: 12px;">        #排除0</font></div><div><font style="font-size: 12px;">        if nums == "0":</font></div><div><font style="font-size: 12px;">            return 0</font></div><div><font style="font-size: 12px;">        #排除只有一种可能的10 和 20</font></div><div><font style="font-size: 12px;">        if nums == "10" or nums == "20":  </font></div><div><font style="font-size: 12px;">            return 1</font></div><div><font style="font-size: 12px;">        #当0的前面不是1或2时，无法译码，0种</font></div><div><font style="font-size: 12px;">        for i in range(1, len(nums)):</font></div><div><font style="font-size: 12px;">            if nums[i] == '0':</font></div><div><font style="font-size: 12px;">                if nums[i - 1] != '1' and nums[i - 1] != '2':</font></div><div><font style="font-size: 12px;">                    return 0</font></div><div><font style="font-size: 12px;">        #辅助数组初始化为1</font></div><div><font style="font-size: 12px;">        dp = [1 for i in range(len(nums) + 1)]</font></div><div><font style="font-size: 12px;">        for i in range(2, len(nums) + 1):</font></div><div><font style="font-size: 12px;">            #在11-19，21-26之间的情况</font></div><div><font style="font-size: 12px;">            if (nums[i - 2] == '1' and nums[i - 1] != '0') or (nums[i - 2] == '2' and nums[i - 1] &gt; '0' and nums[i - 1] &lt; '7'):</font></div><div><font style="font-size: 12px;">                dp[i] = dp[i - 1] + dp[i - 2]</font></div><div><font style="font-size: 12px;">            else:</font></div><div><font style="font-size: 12px;">                dp[i] = dp[i - 1]</font></div><div><font style="font-size: 12px;">        return dp[len(nums)]</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px; font-weight: bold;">38.数的划分</span></div><div><span style="font-size: 12px;">将整数n分成k份，且每份不能为空，任意两个方案不能相同(不考虑顺序)。1,1,5和1,5,1被认为是相同的。</span></div><div><span style="font-size: 12px;">有多少种划分方式？</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def divideNumber(self , n, k):  # n个数分成k份</font></div><div><font style="font-size: 12px;">        dp = [[0]*(k+1) for _ in range(n+1)]</font></div><div><font style="font-size: 12px;">        for i in range(k+1):</font></div><div><font style="font-size: 12px;">            dp[i][i] = 1</font></div><div><font style="font-size: 12px;">       </font></div><div><font style="font-size: 12px;">        for i in range(1, n+1):</font></div><div><font style="font-size: 12px;">            for j in range(1, min(k, i)+1):</font></div><div><font style="font-size: 12px;">                # 情况1：有一份是1，剩下k-1份任意，方案数=dp[i-1][j-1]</font></div><div><font style="font-size: 12px;">                # 情况2：先给j份全放一个数字1,保证不空，剩下的再分成j份，方案数==dp[i-j][j]</font></div><div><font style="font-size: 12px;">                dp[i][j] = (dp[i-1][j-1] + dp[i-j][j])% 1000000007</font></div><div><font style="font-size: 12px;">        return dp[n][k]</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;"><span style="font-size: 12px; font-weight: bold;">39.信封嵌套</span></font></div><div><span style="font-size: 12px;">给 n 个信封的长度和宽度。如果信封 a 的长和宽都小于信封 b ，那么信封 a 可以放到信封 b 里，请求出信封最多可以嵌套多少层。</span></div></div><div style="margin-top: 1em; margin-bottom: 1em;"/><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def maxLetters(self , letters):</font></div><div><font style="font-size: 12px;">        letters.sort() # 按照第一个维度排序，定死一个维度</font></div><div><font style="font-size: 12px;">        dp = [1]*len(letters)</font></div><div><font style="font-size: 12px;">        for i in range(1, len(letters)):</font></div><div><font style="font-size: 12px;">            for j in range(i):</font></div><div><font style="font-size: 12px;">                # 最长递增子序列。找到前面长和宽都小于当前元素的</font></div><div><font style="font-size: 12px;">                if letters[i][0] &gt; letters[j][0] and letters[i][1] &gt; letters[j][1]:</font></div><div><font style="font-size: 12px;">                    dp[i] = max(dp[i], dp[j] + 1)</font></div><div><font style="font-size: 12px;">        return max(dp)</font></div></div><div><span style="font-size: 12px;">40.单词搜索</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">    def exist(self , board, word):</font></div><div><font style="font-size: 12px;">        flag = [[0]*len(board[0]) for _ in range(len(board))]</font></div><div><font style="font-size: 12px;">        def dfs(x, y, k):   # k代表word中下标为k的字符</font></div><div><font style="font-size: 12px;">            if x&lt;0 or x&gt;=len(board) or y&lt;0 or y&gt;=len(board[0]) or board[x][y]!=word[k] or flag[x][y]!=0:</font></div><div><font style="font-size: 12px;">                return False</font></div><div><font style="font-size: 12px;">            if k==len(word)-1:</font></div><div><font style="font-size: 12px;">                return True</font></div><div><font style="font-size: 12px;">            flag[x][y] = 1    # 为了防止走重复的路</font></div><div><font style="font-size: 12px;">            result = dfs(x-1, y, k+1) or dfs(x+1, y, k+1) or dfs(x, y-1, k+1) or dfs(x, y+1, k+1)</font></div><div><font style="font-size: 12px;">            flag[x][y] = 0       # 回溯</font></div><div><font style="font-size: 12px;">            return result</font></div><div><font style="font-size: 12px;">        for i in range(len(board)):</font></div><div><font style="font-size: 12px;">            for j in range(len(board[0])):</font></div><div><font style="font-size: 12px;">                if dfs(i, j, 0)==True:</font></div><div><font style="font-size: 12px;">                    return True</font></div><div><font style="font-size: 12px;">        return False</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px;">41.最大正方形</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">def solve(self, matrix: List[List[str]]) -&gt; int:</font></div><div><font style="font-size: 12px;">    if len(matrix) == 0 or len(matrix[0]) == 0:</font></div><div><font style="font-size: 12px;">        return 0</font></div><div><font style="font-size: 12px;">    maxSide = 0  # 最大正方形的边长</font></div><div><font style="font-size: 12px;">    rows, columns = len(matrix), len(matrix[0])</font></div><div><font style="font-size: 12px;">    dp = [[0] * columns for _ in range(rows)]</font></div><div><font style="font-size: 12px;">    for i in range(rows):</font></div><div><font style="font-size: 12px;">        for j in range(columns):</font></div><div><font style="font-size: 12px;">            if matrix[i][j] == '1':</font></div><div><font style="font-size: 12px;">                if i == 0 or j == 0:</font></div><div><font style="font-size: 12px;">                    dp[i][j] = 1</font></div><div><font style="font-size: 12px;">                else:</font></div><div><font style="font-size: 12px;">                    dp[i][j] = min(dp[i - 1][j], dp[i][j - 1], dp[i - 1][j - 1]) + 1  # 上、左、左上，三个值只要有一个是0，就不行</font></div><div><font style="font-size: 12px;">                maxSide = max(maxSide, dp[i][j])</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">    maxSquare = maxSide * maxSide  # 面积=边长的平方</font></div><div><font style="font-size: 12px;">    return maxSquare</font></div></div><div><span style="font-size: 12px;">42.丢棋子问题</span></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px;">43.连续子数组的最大乘积</span></div><div><span style="font-size: 12px;">1. 有偶数个负数：</span></div><div><span style="font-size: 12px;">在这种情况下最大乘积即为所有数的乘积（除去0的情况）</span></div><div><span style="font-size: 12px;">2. 有奇数个负数：</span></div><div><span style="font-size: 12px;">在这种情况下某一个未知的负数会成为分界点，将整个数组分成左右两个部分，最大乘积可能同时在左边或者右边部分出现。我们只需要同时求解左右两边乘积最大值即为所求</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">if not nums:</font></div><div><font style="font-size: 12px;">    return 0</font></div><div><font style="font-size: 12px;">n = len(nums)</font></div><div><font style="font-size: 12px;">left = 0</font></div><div><font style="font-size: 12px;">right = 0</font></div><div><font style="font-size: 12px;">max_product = float("-inf")</font></div><div><font style="font-size: 12px;">for i in range(0, n):</font></div><div><font style="font-size: 12px;">    if left == 0:</font></div><div><font style="font-size: 12px;">        left = nums[i]</font></div><div><font style="font-size: 12px;">    else:</font></div><div><font style="font-size: 12px;">        left *= nums[i]</font></div><div><font style="font-size: 12px;">    if right == 0:</font></div><div><font style="font-size: 12px;">        right = nums[n - 1 - i]</font></div><div><font style="font-size: 12px;">    else:</font></div><div><font style="font-size: 12px;">        right *= nums[n - 1 - i]           </font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">    max_product = max(max_product, max(left, right))</font></div><div><font style="font-size: 12px;">return max_product</font></div><div><font style="font-size: 12px;"><br/></font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px;">44.自由之路</span></div><div><span style="font-size: 12px;"><img src="%E5%8A%A8%E6%80%81%E8%A7%84%E5%88%92.resources/14717761-6A5F-4CD2-AACD-48A419C22D21.png" height="688" width="602"/><br/></span></div><div><span style="font-size: 12px;">ring是轮盘上的字符，要用轮盘匹配key。把转盘转到箭头处，才能匹配。转轮盘算一次操作，匹配也算一次操作。求最少操作次数？</span></div><div><span style="font-size: 12px;">输入: ring = “gdonidg", key = "gd"</span></div><div><span style="font-size: 12px;">输出: 3</span></div><div><span style="font-size: 12px;">解释：无需转动，先匹配g。再转一次，转到d，匹配d。所以总次数是3。</span></div><div><span style="font-size: 12px;">思路：先用defaultdict记录每个字符的下标。再用dp函数，进行递归。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">from collections import defaultdict</font></div><div><font style="font-size: 12px;">def findRotateSteps(ring, key):</font></div><div><font style="font-size: 12px;">    # 记录每个字符的index，可能有多个，所以用list</font></div><div><font style="font-size: 12px;">    charToIndex = defaultdict(list)</font></div><div><font style="font-size: 12px;">    # 备忘录</font></div><div><font style="font-size: 12px;">    memo = [[0] * len(key) for _ in range(len(ring))]</font></div><div><font style="font-size: 12px;">    # 记录每个字符的index，可能有多个，所以用list。eg:str=goo，那么charToIndex={'g':0,'o':[1,2]}</font></div><div><font style="font-size: 12px;">    for i, c in enumerate(ring):</font></div><div><font style="font-size: 12px;">        if c not in charToIndex:</font></div><div><font style="font-size: 12px;">            charToIndex[c] = []</font></div><div><font style="font-size: 12px;">        charToIndex[c].append(i)</font></div><div><font style="font-size: 12px;">    # 圆盘指针最初指向 12 点钟方向，</font></div><div><font style="font-size: 12px;">    # 从第一个字符开始输入 key</font></div><div><font style="font-size: 12px;">    return dp(ring, 0, key, 0, charToIndex, memo)</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;"># 计算圆盘指针在 ring[i]，匹配key[j..] 的最少操作数</font></div><div><font style="font-size: 12px;">def dp(ring, i, key, j, charToIndex, memo):</font></div><div><font style="font-size: 12px;">    # key的所有元素都匹配成功</font></div><div><font style="font-size: 12px;">    if j == len(key):</font></div><div><font style="font-size: 12px;">        return 0</font></div><div><font style="font-size: 12px;">    # 查找备忘录，避免重叠子问题</font></div><div><font style="font-size: 12px;">    if memo[i][j]:</font></div><div><font style="font-size: 12px;">        return memo[i][j]</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">    n = len(ring)</font></div><div><font style="font-size: 12px;">    # 做选择</font></div><div><font style="font-size: 12px;">    res = float('inf')</font></div><div><font style="font-size: 12px;">    # ring 上可能有多个字符 key[j]</font></div><div><font style="font-size: 12px;">    for k in charToIndex[key[j]]:</font></div><div><font style="font-size: 12px;">        # 拨动指针的次数</font></div><div><font style="font-size: 12px;">        delta = abs(k - i)</font></div><div><font style="font-size: 12px;">        # 选择顺时针还是逆时针</font></div><div><font style="font-size: 12px;">        delta = min(delta, n - delta)</font></div><div><font style="font-size: 12px;">        # 将指针拨到 ring[k]，key的第j个字符匹配完成，继续匹配key[j+1..]</font></div><div><font style="font-size: 12px;">        subProblem = dp(ring, k, key, j + 1, charToIndex, memo)</font></div><div><font style="font-size: 12px;">        # 选择「整体」操作次数最少的</font></div><div><font style="font-size: 12px;">        # 加一是因为按动按钮也是一次操作</font></div><div><font style="font-size: 12px;">        res = min(res, 1 + delta + subProblem)</font></div><div><font style="font-size: 12px;">    # 将结果存入备忘录</font></div><div><font style="font-size: 12px;">    memo[i][j] = res</font></div><div><font style="font-size: 12px;">    return res</font></div><div><font style="font-size: 12px;">print(findRotateSteps("godding", "gd"))</font></div></div><div><font style="font-size: 12px;"><br/></font></div><div><span style="font-size: 12px;">45.高楼扔鸡蛋</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 12px;">def superEggDrop(self, K, N):</font></div><div><font style="font-size: 12px;">    memo = dict()</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">    # k个鸡蛋，n层楼，最少需要几次</font></div><div><font style="font-size: 12px;">    def dp(K, N):</font></div><div><font style="font-size: 12px;">        if K == 1: return N  # 只有一个鸡蛋，只能从第一层开始试</font></div><div><font style="font-size: 12px;">        if N == 0: return 0  # 0层楼，不用试</font></div><div><font style="font-size: 12px;">        if (K, N) in memo:</font></div><div><font style="font-size: 12px;">            return memo[(K, N)]</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">        # 线性搜索的做法：复杂度高，用二分替代</font></div><div><font style="font-size: 12px;">        # 在1到N中，选一层扔鸡蛋，取最小次数的那一层。 max的意思：题目要求考虑最坏情况             </font></div><div><font style="font-size: 12px;">        # for 1 &lt;= i &lt;= N:</font></div><div><font style="font-size: 12px;">        #     res = min(res, max(dp(K - 1, i - 1), dp(K, N - i)) + 1)</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">        res = float('INF')</font></div><div><font style="font-size: 12px;">        # 用二分搜索代替线性搜索</font></div><div><font style="font-size: 12px;">        lo, hi = 1, N</font></div><div><font style="font-size: 12px;">        while lo &lt;= hi:</font></div><div><font style="font-size: 12px;">            mid = (lo + hi) // 2</font></div><div><font style="font-size: 12px;">            broken = dp(K - 1, mid - 1)  # 碎</font></div><div><font style="font-size: 12px;">            not_broken = dp(K, N - mid)  # 没碎</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">            if broken &gt; not_broken:</font></div><div><font style="font-size: 12px;">                hi = mid - 1</font></div><div><font style="font-size: 12px;">                res = min(res, broken + 1)</font></div><div><font style="font-size: 12px;">            else:</font></div><div><font style="font-size: 12px;">                lo = mid + 1</font></div><div><font style="font-size: 12px;">                res = min(res, not_broken + 1)</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">        memo[(K, N)] = res</font></div><div><font style="font-size: 12px;">        return res</font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;"><br/></font></div><div><font style="font-size: 12px;">    return dp(K, N)</font></div></div><div><span style="font-size: 12px;">时间复杂度：子问题个数*子问题时间复杂度=O(KN)*O(logN)=O(KN*logN)。子问题个数：dp[k][n]总共有k*n种情况，计算每一个dp[i][j]都要花费logn的时间。</span></div><div><span style="font-size: 12px;"><br/></span></div><div><span style="font-size: 12px;">46.博弈问题</span></div><div><span style="font-size: 12px;">预测赢家</span></div><div><span style="font-size: 12px;">nums = [1,5,2]，玩家1和玩家2可以从数组头/尾拿数字，拿完为止。最后分高的获胜，如果是平局，算玩家1胜。假设玩家1和玩家2都足够聪明，问：玩家1能取胜吗？</span></div><div><span style="font-size: 12px;"><br/></span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div>def PredictTheWinner(self, nums):</div><div>    length = len(nums)</div><div>    if length == 1:</div><div>        return True</div><div>    # dp[i][j]：面对区间[i,j]，先手，拿完后，能领先对手多少分</div><div>    dp = [[0] * length for i in range(length)]</div><div>    for i in range(length):</div><div>        dp[i][i] = nums[i]</div><div>    for i in range(length - 2, -1, -1):</div><div>        for j in range(i + 1, length):</div><div>            # 判断拿左边、拿右边哪个收益高</div><div>            dp[i][j] = max(nums[i] - dp[i + 1][j], nums[j] - dp[i][j - 1])</div><div>    return dp[0][length - 1] &gt;= 0</div></div><div><span style="font-size: 12px;"><br/></span></div><div><span style="font-size: 12px;">47.戳气球：</span></div><div><span style="font-size: 12px;">有 n 个气球，编号为0 到 n - 1，每个气球上都标有一个数字，这些数字存在数组 nums 中。</span></div><div><span style="font-size: 12px;">现在要求你戳破所有的气球。戳破第 i 个气球，你可以获得 nums[i - 1] * nums[i] * nums[i + 1] 枚硬币。 这里的 i - 1 和 i + 1 代表和 i 相邻的两个气球的序号。如果 i - 1或 i + 1 超出了数组的边界，那么就当它是一个数字为 1 的气球。</span><span style="font-size: 12px; color: unset;">求所能获得硬币的最大数量。</span></div><div><span style="font-size: 12px;">输入：nums = [3,1,5,8]</span></div><div><span style="font-size: 12px;">输出：167</span></div><div><span style="font-size: 12px;">解释：</span></div><div><span style="font-size: 12px;">nums = [3,1,5,8] --&gt; [3,5,8] --&gt; [3,8] --&gt; [8] --&gt; []</span></div><div><span style="font-size: 12px;">coins =  3*1*5    +   3*5*8   +  1*3*8  + 1*8*1 = 167</span></div><div><span style="font-size: 12px;">思路：遍历顺序</span></div><div><span style="font-size: 12px;"><img src="%E5%8A%A8%E6%80%81%E8%A7%84%E5%88%92.resources/F4B6D6D0-8164-4EF8-8194-EBAAAA378B83.png" height="764" width="1032"/><br/></span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div>def maxCoins(self, nums):</div><div>    n = len(nums)</div><div>    # 添加两侧的虚拟气球</div><div>    points = [0] * (n + 2)</div><div>    points[0] = points[n + 1] = 1  # 头和尾的虚拟气球的分数设为1</div><div>    for i in range(1, n + 1):</div><div>        points[i] = nums[i - 1]</div><div><br/></div><div>    dp = [[0] * (n + 2) for _ in range(n + 2)]  # 戳破(i,j)的所有气球，获得的得分</div><div><br/></div><div>    # i 应该从下往上</div><div>    for i in range(n, -1, -1):</div><div>        # j 应该从左往右</div><div>        for j in range(i + 1, n + 2):</div><div>            # 最后戳破的气球是哪个？</div><div>            for k in range(i + 1, j):</div><div>                # 择优做选择</div><div>                dp[i][j] = max(dp[i][j], dp[i][k] + dp[k][j] + points[i] * points[j] * points[k])</div><div>    return dp[0][n + 1]</div></div><div><span style="font-size: 12px;"><br/></span></div><div><span style="font-size: 12px;"><br/></span></div><div><span style="font-size: 12px;"><br/></span></div><div><br/></div></body></html>