<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/><meta name="exporter-version" content="Evernote Mac 9.7.14 (474683)"/><meta name="created" content="2021-11-29 03:29:10 +0000"/><meta name="updated" content="2021-11-29 03:29:10 +0000"/><meta name="content-class" content="yinxiang.markdown"/><title>Transformer</title></head><body><div style="font-size: 14px; margin: 0; padding: 0; width: 100%;"><p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">transformer</strong><br clear="none"/>
<img src="https://app.yinxiang.com/FileSharing.action?hash=1/9f4d0763dd6eb77a1f1fae4785ba8bf6-629125" width="1415" height="804"/><br clear="none"/>
<img src="https://app.yinxiang.com/FileSharing.action?hash=1/44f1705bf232fc8f7b613b2b7d9bb937-108821" width="611" height="869"/><br clear="none"/>
transformer的本质上是一个Encoder-Decoder的结构，Transformer没有采用cnn或rnn结构,仅由self-Attenion和Feed Forward Neural Network组成。</p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">Add&amp;Normalize模块</strong><br clear="none"/>
Add操作残差模块，借鉴了ResNet模型的结构，其主要作用是使得transformer的多层叠加而效果不退化<br clear="none"/>
Normalize使用Layer Normalization操作对向量进行标准化，把每一个样本拉回到标准高斯分布。<br clear="none"/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">为什么不用batch normalization?</strong><br clear="none"/>
batch是“竖”着来的，各个维度做归一化，所以与batch size有关系。<br clear="none"/>
layer是“横”着来的，对一个样本，不同的神经元neuron间做归一化。<br clear="none"/>
更适合，nlp中多用layer normalization，因为每个句子长度不一样，用batch的话需要做padding<br clear="none"/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">什么是Batch Normalization？</strong><br clear="none"/>
深度神经网络在做非线性变换前的激活输入值随着网络深度加深或者在训练过程中，其<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">分布逐渐发生偏移或者变动</strong>，之所以训练收敛满慢，一般是整体分布逐渐往非线性函数的取值空间的上下限两端，即饱和区靠近，导致<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">梯度消失</strong>问题。<br clear="none"/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">Batch Normalization</strong>：对于每个隐层神经元，把逐渐向线性函数映射后向取值空间极限饱和区靠近的输入分布强行拉回到均值为0方差为1的标准正态分布，使得非线性函数的输入值落入对输入比较敏感的区域，以此避免梯度消失问题。<br clear="none"/>
假设没有经过BN调整前x的原先正态分布均值是-6，方差是1，那么意味着95%的值落在了[-8,-4]之间，那么对应的Sigmoid（x）函数的值明显接近于0，这是典型的梯度饱和区，在这个区域里梯度变化很慢，<br clear="none"/>
<img src="https://app.yinxiang.com/FileSharing.action?hash=1/6614700790f1ffeb8d986ec728b31b4e-58863" width="746" height="416"/><br clear="none"/>
<img src="https://app.yinxiang.com/FileSharing.action?hash=1/27c025b19d1e40a6c492e024e3ec1aa2-49605" width="496" height="339"/><br clear="none"/>
均值为0，方差为1的标准正态分布64%的概率x其值落在[-1,1]的范围内，在两个标准差范围内，也就是说95%的概率x其值落在了[-2,2]的范围内，很明显这一段是sigmoid(x)函数<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">接近于线性变换</strong>的区域，意味着x的小变化会导致非线性函数值较大的变化，也即是梯度变化较大，对应导数函数图中明显大于0的区域，就是梯度非饱和区。</p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;">如果都通过BN，那么不就跟把非线性函数替换成线性函数效果相同了？<br clear="none"/>
BN为了保证非线性的获得，对变换后的均值为0方差为1的分布x又进行了scale加上shift操作y=scale x+shift，意思是通过scale和shift把这两个值从标准正态分布左移或右移一点使得分布变胖或瘦一点，每个x移动的程度不一样，这等价于非线性函数的值从正中心周围的先行区往非线性区动了动。核心思想是找到一个线性和非线性的较好的平衡点。<br clear="none"/>
优点：</p>
<ul style="line-height: 160%; box-sizing: content-box; display: block; list-style-type: disc; padding-left: 30px; margin: 6px 0 10px; color: #333;"><li style="line-height: 160%; box-sizing: content-box; position: relative;">提升训练速度，加快收敛</li><li style="line-height: 160%; box-sizing: content-box; position: relative;">防止过拟合的正则化</li><li style="line-height: 160%; box-sizing: content-box; position: relative;">调参简单<br clear="none"/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">Feed-Forward Layer</strong><br clear="none"/>
就是两个全连接层<br clear="none"/>
FFN的加入引入了<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">非线性(ReLu激活函数)，变换了attention output的空间, 从而增强了模型的表现能力</strong>。把FFN去掉模型也是可以用的，但是效果差了很多。<br clear="none"/>
将Multi-Head Attention得到的提炼好的向量再投影到一个更大的空间（论文里将空间放大了4倍）在那个大空间里可以更方便地提取需要的信息（使用Relu激活函数），最后再投影回token向量原来的空间。<br clear="none"/>
<img src="https://app.yinxiang.com/FileSharing.action?hash=1/2be26569d10697fba60b25fa7a091187-21443" width="352" height="214"/><br clear="none"/>
<img src="https://app.yinxiang.com/FileSharing.action?hash=1/38ef94ca6ae2f013a918ff2f5e274b11-4640" width="369" height="43"/><br clear="none"/>
可以借鉴SVM来理解：SVM对于比较复杂的问题通过将特征其投影到更高维的空间使得问题简单到一个超平面就能解决。这里token向量里的信息通过Feed Forward Layer被投影到更高维的空间，在高维空间里向量的各类信息彼此之间更容易区别。<br clear="none"/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">Position Embedding</strong><br clear="none"/>
transformer使用的是<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">函数式绝对位置编码</strong><br clear="none"/>
通过将token按次序依次将其传入RNN，RNN可以隐含的将token所在的次序位置信息编码进其隐藏状态里而从Multi-Head Attention结构可以看出来token向量的信息提取是通过Attention机制完成的，无法像RNN一样去隐含的将次序位置信息编码进去。<br clear="none"/>
<img src="https://app.yinxiang.com/FileSharing.action?hash=1/1e6ac2e408e77a5a5cd098cdba62e1f5-25277" width="654" height="367"/><br clear="none"/>
RNN中，第一个"I"与第二个"I"的输出表征不同，因为用于生成这两个单词的hidden states是不同的。对于第一个"I"，其hidden state是初始化的状态；对于第二个"I"，其hidden state是编码了"I think therefore"的hidden state。所以RNN的hidden state保证了在同一个输入序列中，不同位置的同样的单词的output representation是不同的。<br clear="none"/>
<img src="https://app.yinxiang.com/FileSharing.action?hash=1/c89bfac1c0207a3e0d4235202f7c4cb1-54103" width="700" height="277"/><br clear="none"/>
在self-attention中，第一个"I"与第二个"I"的输出将完全相同。因为它们用于产生输出的“input”是完全相同的。即在同一个输入序列中，不同位置的相同的单词的output representation完全相同，这样就不能体现单词之间的时序关系。--所以要对单词的时序位置进行编码表征。<br clear="none"/>
将次序位置信息编码成向量并直接加到词token向量上，使用三角函数计算位置编码。<br clear="none"/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">为什么使用三角函数编码：</strong><br clear="none"/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">周期函数能引入位置信息</strong><br clear="none"/>
为了体现某个字在句子中的绝对位置，使用了一个单调的函数，使得任意后续的词的位置编码都大于前面的词，如果我们放弃对绝对位置的追求，转而要求位置编码仅仅关注一定范围内的相对次序关系，那么使用一个sin/cos函数就是很好的选择，因为sin/cos函数的<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">周期</strong>变化规律非常稳定，所以编码具有一定的不变性。</li></ul>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">Attention</strong><br clear="none"/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">transformer中的attention其实是multi-head attention</strong>（类似于cnn中的多个channel,可以去capture不同维度和的特征）。该机制理解起来很简单，就是说不仅仅只初始化一组Q、K、V的矩阵，而是初始化多组，tranformer是使用了8组，所以最后得到的结果是8个矩阵。<br clear="none"/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">multi-head attention好处</strong></p>
<ul style="line-height: 160%; box-sizing: content-box; display: block; list-style-type: disc; padding-left: 30px; margin: 6px 0 10px; color: #333;"><li style="line-height: 160%; box-sizing: content-box; position: relative;">并行，速度快</li><li style="line-height: 160%; box-sizing: content-box; position: relative;">学到更多的信息</li></ul>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">transformer用到的mask：</strong><br clear="none"/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">encoder中的attention</strong><br clear="none"/>
使用mask让在一个batch中长度较短序列的padding位置不参与attention计算。<br clear="none"/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">decoder中的两个attention:</strong><br clear="none"/>
Self-Attention：key, query, value均来自前一层decoder的输出，但<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">加入了Mask操作</strong>，即不能看见未来的信息，我们只能观测到前面的词和当前的词之间的关系<br clear="none"/>
<img src="https://app.yinxiang.com/FileSharing.action?hash=1/e32d0a0e363b596e766f76312105f575-60097" width="674" height="432"/></p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;">Encoder-Decoder Attention：query来自于之前一级的decoder层的输出（文本输出向量），但其key和value来自于encoder的输出，这使得decoder的每一个位置都可以attend到输入序列的每一个位置。计算当前词和query之间的关系。</p>
<ul style="line-height: 160%; box-sizing: content-box; display: block; list-style-type: disc; padding-left: 30px; margin: 6px 0 10px; color: #333;"><li style="line-height: 160%; box-sizing: content-box; position: relative;">为什么用mask attention<br clear="none"/>
需要防止标签泄露，Transformer的任务是seq2seq，即序列第i个位置要用到前i-1时刻的信息，而与其后面位置的其它信息无关。</li></ul>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">transformer的权重共享体现在哪</strong></p>
<ul style="line-height: 160%; box-sizing: content-box; display: block; list-style-type: disc; padding-left: 30px; margin: 6px 0 10px; color: #333;"><li style="line-height: 160%; box-sizing: content-box; position: relative;">encoder和decoder层之间的embedding层权重共享</li><li style="line-height: 160%; box-sizing: content-box; position: relative;">decoder中embedding和全连接层权重共享<br clear="none"/>
transformer词表使用bpe来处理，所以最小的单元是subword，如英语和德语之间的转换，原文本和目标文本可以共享类似的语义，而像中英这样相差较大的语系，权重共享作用可能不是很大。</li></ul>
</div><center style="display:none !important;visibility:collapse !important;height:0 !important;white-space:nowrap;width:100%;overflow:hidden">**transformer**%0A!%5B9f4d0763dd6eb77a1f1fae4785ba8bf6.png%5D(evernotecid%3A%2F%2F50E31125-9E8E-4F15-8430-8CF4D64A8B72%2Fappyinxiangcom%2F30104613%2FENResource%2Fp376)%0A!%5B44f1705bf232fc8f7b613b2b7d9bb937.png%5D(evernotecid%3A%2F%2F50E31125-9E8E-4F15-8430-8CF4D64A8B72%2Fappyinxiangcom%2F30104613%2FENResource%2Fp452)%0Atransformer%E7%9A%84%E6%9C%AC%E8%B4%A8%E4%B8%8A%E6%98%AF%E4%B8%80%E4%B8%AAEncoder-Decoder%E7%9A%84%E7%BB%93%E6%9E%84%EF%BC%8CTransformer%E6%B2%A1%E6%9C%89%E9%87%87%E7%94%A8cnn%E6%88%96rnn%E7%BB%93%E6%9E%84%2C%E4%BB%85%E7%94%B1self-Attenion%E5%92%8CFeed%20Forward%20Neural%20Network%E7%BB%84%E6%88%90%E3%80%82%0A%0A**Add%26Normalize%E6%A8%A1%E5%9D%97**%0AAdd%E6%93%8D%E4%BD%9C%E6%AE%8B%E5%B7%AE%E6%A8%A1%E5%9D%97%EF%BC%8C%E5%80%9F%E9%89%B4%E4%BA%86ResNet%E6%A8%A1%E5%9E%8B%E7%9A%84%E7%BB%93%E6%9E%84%EF%BC%8C%E5%85%B6%E4%B8%BB%E8%A6%81%E4%BD%9C%E7%94%A8%E6%98%AF%E4%BD%BF%E5%BE%97transformer%E7%9A%84%E5%A4%9A%E5%B1%82%E5%8F%A0%E5%8A%A0%E8%80%8C%E6%95%88%E6%9E%9C%E4%B8%8D%E9%80%80%E5%8C%96%0ANormalize%E4%BD%BF%E7%94%A8Layer%20Normalization%E6%93%8D%E4%BD%9C%E5%AF%B9%E5%90%91%E9%87%8F%E8%BF%9B%E8%A1%8C%E6%A0%87%E5%87%86%E5%8C%96%EF%BC%8C%E6%8A%8A%E6%AF%8F%E4%B8%80%E4%B8%AA%E6%A0%B7%E6%9C%AC%E6%8B%89%E5%9B%9E%E5%88%B0%E6%A0%87%E5%87%86%E9%AB%98%E6%96%AF%E5%88%86%E5%B8%83%E3%80%82%0A**%E4%B8%BA%E4%BB%80%E4%B9%88%E4%B8%8D%E7%94%A8batch%20normalization%3F**%0Abatch%E6%98%AF%E2%80%9C%E7%AB%96%E2%80%9D%E7%9D%80%E6%9D%A5%E7%9A%84%EF%BC%8C%E5%90%84%E4%B8%AA%E7%BB%B4%E5%BA%A6%E5%81%9A%E5%BD%92%E4%B8%80%E5%8C%96%EF%BC%8C%E6%89%80%E4%BB%A5%E4%B8%8Ebatch%20size%E6%9C%89%E5%85%B3%E7%B3%BB%E3%80%82%0Alayer%E6%98%AF%E2%80%9C%E6%A8%AA%E2%80%9D%E7%9D%80%E6%9D%A5%E7%9A%84%EF%BC%8C%E5%AF%B9%E4%B8%80%E4%B8%AA%E6%A0%B7%E6%9C%AC%EF%BC%8C%E4%B8%8D%E5%90%8C%E7%9A%84%E7%A5%9E%E7%BB%8F%E5%85%83neuron%E9%97%B4%E5%81%9A%E5%BD%92%E4%B8%80%E5%8C%96%E3%80%82%0A%E6%9B%B4%E9%80%82%E5%90%88%EF%BC%8Cnlp%E4%B8%AD%E5%A4%9A%E7%94%A8layer%20normalization%EF%BC%8C%E5%9B%A0%E4%B8%BA%E6%AF%8F%E4%B8%AA%E5%8F%A5%E5%AD%90%E9%95%BF%E5%BA%A6%E4%B8%8D%E4%B8%80%E6%A0%B7%EF%BC%8C%E7%94%A8batch%E7%9A%84%E8%AF%9D%E9%9C%80%E8%A6%81%E5%81%9Apadding%0A**%E4%BB%80%E4%B9%88%E6%98%AFBatch%20Normalization%EF%BC%9F**%0A%E6%B7%B1%E5%BA%A6%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C%E5%9C%A8%E5%81%9A%E9%9D%9E%E7%BA%BF%E6%80%A7%E5%8F%98%E6%8D%A2%E5%89%8D%E7%9A%84%E6%BF%80%E6%B4%BB%E8%BE%93%E5%85%A5%E5%80%BC%E9%9A%8F%E7%9D%80%E7%BD%91%E7%BB%9C%E6%B7%B1%E5%BA%A6%E5%8A%A0%E6%B7%B1%E6%88%96%E8%80%85%E5%9C%A8%E8%AE%AD%E7%BB%83%E8%BF%87%E7%A8%8B%E4%B8%AD%EF%BC%8C%E5%85%B6**%E5%88%86%E5%B8%83%E9%80%90%E6%B8%90%E5%8F%91%E7%94%9F%E5%81%8F%E7%A7%BB%E6%88%96%E8%80%85%E5%8F%98%E5%8A%A8**%EF%BC%8C%E4%B9%8B%E6%89%80%E4%BB%A5%E8%AE%AD%E7%BB%83%E6%94%B6%E6%95%9B%E6%BB%A1%E6%85%A2%EF%BC%8C%E4%B8%80%E8%88%AC%E6%98%AF%E6%95%B4%E4%BD%93%E5%88%86%E5%B8%83%E9%80%90%E6%B8%90%E5%BE%80%E9%9D%9E%E7%BA%BF%E6%80%A7%E5%87%BD%E6%95%B0%E7%9A%84%E5%8F%96%E5%80%BC%E7%A9%BA%E9%97%B4%E7%9A%84%E4%B8%8A%E4%B8%8B%E9%99%90%E4%B8%A4%E7%AB%AF%EF%BC%8C%E5%8D%B3%E9%A5%B1%E5%92%8C%E5%8C%BA%E9%9D%A0%E8%BF%91%EF%BC%8C%E5%AF%BC%E8%87%B4**%E6%A2%AF%E5%BA%A6%E6%B6%88%E5%A4%B1**%E9%97%AE%E9%A2%98%E3%80%82%0A**Batch%20Normalization**%EF%BC%9A%E5%AF%B9%E4%BA%8E%E6%AF%8F%E4%B8%AA%E9%9A%90%E5%B1%82%E7%A5%9E%E7%BB%8F%E5%85%83%EF%BC%8C%E6%8A%8A%E9%80%90%E6%B8%90%E5%90%91%E7%BA%BF%E6%80%A7%E5%87%BD%E6%95%B0%E6%98%A0%E5%B0%84%E5%90%8E%E5%90%91%E5%8F%96%E5%80%BC%E7%A9%BA%E9%97%B4%E6%9E%81%E9%99%90%E9%A5%B1%E5%92%8C%E5%8C%BA%E9%9D%A0%E8%BF%91%E7%9A%84%E8%BE%93%E5%85%A5%E5%88%86%E5%B8%83%E5%BC%BA%E8%A1%8C%E6%8B%89%E5%9B%9E%E5%88%B0%E5%9D%87%E5%80%BC%E4%B8%BA0%E6%96%B9%E5%B7%AE%E4%B8%BA1%E7%9A%84%E6%A0%87%E5%87%86%E6%AD%A3%E6%80%81%E5%88%86%E5%B8%83%EF%BC%8C%E4%BD%BF%E5%BE%97%E9%9D%9E%E7%BA%BF%E6%80%A7%E5%87%BD%E6%95%B0%E7%9A%84%E8%BE%93%E5%85%A5%E5%80%BC%E8%90%BD%E5%85%A5%E5%AF%B9%E8%BE%93%E5%85%A5%E6%AF%94%E8%BE%83%E6%95%8F%E6%84%9F%E7%9A%84%E5%8C%BA%E5%9F%9F%EF%BC%8C%E4%BB%A5%E6%AD%A4%E9%81%BF%E5%85%8D%E6%A2%AF%E5%BA%A6%E6%B6%88%E5%A4%B1%E9%97%AE%E9%A2%98%E3%80%82%0A%E5%81%87%E8%AE%BE%E6%B2%A1%E6%9C%89%E7%BB%8F%E8%BF%87BN%E8%B0%83%E6%95%B4%E5%89%8Dx%E7%9A%84%E5%8E%9F%E5%85%88%E6%AD%A3%E6%80%81%E5%88%86%E5%B8%83%E5%9D%87%E5%80%BC%E6%98%AF-6%EF%BC%8C%E6%96%B9%E5%B7%AE%E6%98%AF1%EF%BC%8C%E9%82%A3%E4%B9%88%E6%84%8F%E5%91%B3%E7%9D%8095%25%E7%9A%84%E5%80%BC%E8%90%BD%E5%9C%A8%E4%BA%86%5B-8%2C-4%5D%E4%B9%8B%E9%97%B4%EF%BC%8C%E9%82%A3%E4%B9%88%E5%AF%B9%E5%BA%94%E7%9A%84Sigmoid%EF%BC%88x%EF%BC%89%E5%87%BD%E6%95%B0%E7%9A%84%E5%80%BC%E6%98%8E%E6%98%BE%E6%8E%A5%E8%BF%91%E4%BA%8E0%EF%BC%8C%E8%BF%99%E6%98%AF%E5%85%B8%E5%9E%8B%E7%9A%84%E6%A2%AF%E5%BA%A6%E9%A5%B1%E5%92%8C%E5%8C%BA%EF%BC%8C%E5%9C%A8%E8%BF%99%E4%B8%AA%E5%8C%BA%E5%9F%9F%E9%87%8C%E6%A2%AF%E5%BA%A6%E5%8F%98%E5%8C%96%E5%BE%88%E6%85%A2%EF%BC%8C%0A!%5B6614700790f1ffeb8d986ec728b31b4e.png%5D(evernotecid%3A%2F%2F50E31125-9E8E-4F15-8430-8CF4D64A8B72%2Fappyinxiangcom%2F30104613%2FENResource%2Fp46)%0A!%5B27c025b19d1e40a6c492e024e3ec1aa2.png%5D(evernotecid%3A%2F%2F50E31125-9E8E-4F15-8430-8CF4D64A8B72%2Fappyinxiangcom%2F30104613%2FENResource%2Fp45)%0A%E5%9D%87%E5%80%BC%E4%B8%BA0%EF%BC%8C%E6%96%B9%E5%B7%AE%E4%B8%BA1%E7%9A%84%E6%A0%87%E5%87%86%E6%AD%A3%E6%80%81%E5%88%86%E5%B8%8364%25%E7%9A%84%E6%A6%82%E7%8E%87x%E5%85%B6%E5%80%BC%E8%90%BD%E5%9C%A8%5B-1%2C1%5D%E7%9A%84%E8%8C%83%E5%9B%B4%E5%86%85%EF%BC%8C%E5%9C%A8%E4%B8%A4%E4%B8%AA%E6%A0%87%E5%87%86%E5%B7%AE%E8%8C%83%E5%9B%B4%E5%86%85%EF%BC%8C%E4%B9%9F%E5%B0%B1%E6%98%AF%E8%AF%B495%25%E7%9A%84%E6%A6%82%E7%8E%87x%E5%85%B6%E5%80%BC%E8%90%BD%E5%9C%A8%E4%BA%86%5B-2%2C2%5D%E7%9A%84%E8%8C%83%E5%9B%B4%E5%86%85%EF%BC%8C%E5%BE%88%E6%98%8E%E6%98%BE%E8%BF%99%E4%B8%80%E6%AE%B5%E6%98%AFsigmoid(x)%E5%87%BD%E6%95%B0**%E6%8E%A5%E8%BF%91%E4%BA%8E%E7%BA%BF%E6%80%A7%E5%8F%98%E6%8D%A2**%E7%9A%84%E5%8C%BA%E5%9F%9F%EF%BC%8C%E6%84%8F%E5%91%B3%E7%9D%80x%E7%9A%84%E5%B0%8F%E5%8F%98%E5%8C%96%E4%BC%9A%E5%AF%BC%E8%87%B4%E9%9D%9E%E7%BA%BF%E6%80%A7%E5%87%BD%E6%95%B0%E5%80%BC%E8%BE%83%E5%A4%A7%E7%9A%84%E5%8F%98%E5%8C%96%EF%BC%8C%E4%B9%9F%E5%8D%B3%E6%98%AF%E6%A2%AF%E5%BA%A6%E5%8F%98%E5%8C%96%E8%BE%83%E5%A4%A7%EF%BC%8C%E5%AF%B9%E5%BA%94%E5%AF%BC%E6%95%B0%E5%87%BD%E6%95%B0%E5%9B%BE%E4%B8%AD%E6%98%8E%E6%98%BE%E5%A4%A7%E4%BA%8E0%E7%9A%84%E5%8C%BA%E5%9F%9F%EF%BC%8C%E5%B0%B1%E6%98%AF%E6%A2%AF%E5%BA%A6%E9%9D%9E%E9%A5%B1%E5%92%8C%E5%8C%BA%E3%80%82%0A%0A%E5%A6%82%E6%9E%9C%E9%83%BD%E9%80%9A%E8%BF%87BN%EF%BC%8C%E9%82%A3%E4%B9%88%E4%B8%8D%E5%B0%B1%E8%B7%9F%E6%8A%8A%E9%9D%9E%E7%BA%BF%E6%80%A7%E5%87%BD%E6%95%B0%E6%9B%BF%E6%8D%A2%E6%88%90%E7%BA%BF%E6%80%A7%E5%87%BD%E6%95%B0%E6%95%88%E6%9E%9C%E7%9B%B8%E5%90%8C%E4%BA%86%EF%BC%9F%0ABN%E4%B8%BA%E4%BA%86%E4%BF%9D%E8%AF%81%E9%9D%9E%E7%BA%BF%E6%80%A7%E7%9A%84%E8%8E%B7%E5%BE%97%EF%BC%8C%E5%AF%B9%E5%8F%98%E6%8D%A2%E5%90%8E%E7%9A%84%E5%9D%87%E5%80%BC%E4%B8%BA0%E6%96%B9%E5%B7%AE%E4%B8%BA1%E7%9A%84%E5%88%86%E5%B8%83x%E5%8F%88%E8%BF%9B%E8%A1%8C%E4%BA%86scale%E5%8A%A0%E4%B8%8Ashift%E6%93%8D%E4%BD%9Cy%3Dscale%20x%2Bshift%EF%BC%8C%E6%84%8F%E6%80%9D%E6%98%AF%E9%80%9A%E8%BF%87scale%E5%92%8Cshift%E6%8A%8A%E8%BF%99%E4%B8%A4%E4%B8%AA%E5%80%BC%E4%BB%8E%E6%A0%87%E5%87%86%E6%AD%A3%E6%80%81%E5%88%86%E5%B8%83%E5%B7%A6%E7%A7%BB%E6%88%96%E5%8F%B3%E7%A7%BB%E4%B8%80%E7%82%B9%E4%BD%BF%E5%BE%97%E5%88%86%E5%B8%83%E5%8F%98%E8%83%96%E6%88%96%E7%98%A6%E4%B8%80%E7%82%B9%EF%BC%8C%E6%AF%8F%E4%B8%AAx%E7%A7%BB%E5%8A%A8%E7%9A%84%E7%A8%8B%E5%BA%A6%E4%B8%8D%E4%B8%80%E6%A0%B7%EF%BC%8C%E8%BF%99%E7%AD%89%E4%BB%B7%E4%BA%8E%E9%9D%9E%E7%BA%BF%E6%80%A7%E5%87%BD%E6%95%B0%E7%9A%84%E5%80%BC%E4%BB%8E%E6%AD%A3%E4%B8%AD%E5%BF%83%E5%91%A8%E5%9B%B4%E7%9A%84%E5%85%88%E8%A1%8C%E5%8C%BA%E5%BE%80%E9%9D%9E%E7%BA%BF%E6%80%A7%E5%8C%BA%E5%8A%A8%E4%BA%86%E5%8A%A8%E3%80%82%E6%A0%B8%E5%BF%83%E6%80%9D%E6%83%B3%E6%98%AF%E6%89%BE%E5%88%B0%E4%B8%80%E4%B8%AA%E7%BA%BF%E6%80%A7%E5%92%8C%E9%9D%9E%E7%BA%BF%E6%80%A7%E7%9A%84%E8%BE%83%E5%A5%BD%E7%9A%84%E5%B9%B3%E8%A1%A1%E7%82%B9%E3%80%82%0A%E4%BC%98%E7%82%B9%EF%BC%9A%0A*%20%E6%8F%90%E5%8D%87%E8%AE%AD%E7%BB%83%E9%80%9F%E5%BA%A6%EF%BC%8C%E5%8A%A0%E5%BF%AB%E6%94%B6%E6%95%9B%0A*%20%E9%98%B2%E6%AD%A2%E8%BF%87%E6%8B%9F%E5%90%88%E7%9A%84%E6%AD%A3%E5%88%99%E5%8C%96%0A*%20%E8%B0%83%E5%8F%82%E7%AE%80%E5%8D%95%0A**Feed-Forward%20Layer**%0A%20%20%20%20%E5%B0%B1%E6%98%AF%E4%B8%A4%E4%B8%AA%E5%85%A8%E8%BF%9E%E6%8E%A5%E5%B1%82%0A%20%20%20%20FFN%E7%9A%84%E5%8A%A0%E5%85%A5%E5%BC%95%E5%85%A5%E4%BA%86**%E9%9D%9E%E7%BA%BF%E6%80%A7(ReLu%E6%BF%80%E6%B4%BB%E5%87%BD%E6%95%B0)%EF%BC%8C%E5%8F%98%E6%8D%A2%E4%BA%86attention%20output%E7%9A%84%E7%A9%BA%E9%97%B4%2C%20%E4%BB%8E%E8%80%8C%E5%A2%9E%E5%BC%BA%E4%BA%86%E6%A8%A1%E5%9E%8B%E7%9A%84%E8%A1%A8%E7%8E%B0%E8%83%BD%E5%8A%9B**%E3%80%82%E6%8A%8AFFN%E5%8E%BB%E6%8E%89%E6%A8%A1%E5%9E%8B%E4%B9%9F%E6%98%AF%E5%8F%AF%E4%BB%A5%E7%94%A8%E7%9A%84%EF%BC%8C%E4%BD%86%E6%98%AF%E6%95%88%E6%9E%9C%E5%B7%AE%E4%BA%86%E5%BE%88%E5%A4%9A%E3%80%82%0A%20%20%20%20%E5%B0%86Multi-Head%20Attention%E5%BE%97%E5%88%B0%E7%9A%84%E6%8F%90%E7%82%BC%E5%A5%BD%E7%9A%84%E5%90%91%E9%87%8F%E5%86%8D%E6%8A%95%E5%BD%B1%E5%88%B0%E4%B8%80%E4%B8%AA%E6%9B%B4%E5%A4%A7%E7%9A%84%E7%A9%BA%E9%97%B4%EF%BC%88%E8%AE%BA%E6%96%87%E9%87%8C%E5%B0%86%E7%A9%BA%E9%97%B4%E6%94%BE%E5%A4%A7%E4%BA%864%E5%80%8D%EF%BC%89%E5%9C%A8%E9%82%A3%E4%B8%AA%E5%A4%A7%E7%A9%BA%E9%97%B4%E9%87%8C%E5%8F%AF%E4%BB%A5%E6%9B%B4%E6%96%B9%E4%BE%BF%E5%9C%B0%E6%8F%90%E5%8F%96%E9%9C%80%E8%A6%81%E7%9A%84%E4%BF%A1%E6%81%AF%EF%BC%88%E4%BD%BF%E7%94%A8Relu%E6%BF%80%E6%B4%BB%E5%87%BD%E6%95%B0%EF%BC%89%EF%BC%8C%E6%9C%80%E5%90%8E%E5%86%8D%E6%8A%95%E5%BD%B1%E5%9B%9Etoken%E5%90%91%E9%87%8F%E5%8E%9F%E6%9D%A5%E7%9A%84%E7%A9%BA%E9%97%B4%E3%80%82%0A!%5B2be26569d10697fba60b25fa7a091187.png%5D(evernotecid%3A%2F%2F50E31125-9E8E-4F15-8430-8CF4D64A8B72%2Fappyinxiangcom%2F30104613%2FENResource%2Fp450)%0A!%5B38ef94ca6ae2f013a918ff2f5e274b11.png%5D(evernotecid%3A%2F%2F50E31125-9E8E-4F15-8430-8CF4D64A8B72%2Fappyinxiangcom%2F30104613%2FENResource%2Fp455)%0A%E5%8F%AF%E4%BB%A5%E5%80%9F%E9%89%B4SVM%E6%9D%A5%E7%90%86%E8%A7%A3%EF%BC%9ASVM%E5%AF%B9%E4%BA%8E%E6%AF%94%E8%BE%83%E5%A4%8D%E6%9D%82%E7%9A%84%E9%97%AE%E9%A2%98%E9%80%9A%E8%BF%87%E5%B0%86%E7%89%B9%E5%BE%81%E5%85%B6%E6%8A%95%E5%BD%B1%E5%88%B0%E6%9B%B4%E9%AB%98%E7%BB%B4%E7%9A%84%E7%A9%BA%E9%97%B4%E4%BD%BF%E5%BE%97%E9%97%AE%E9%A2%98%E7%AE%80%E5%8D%95%E5%88%B0%E4%B8%80%E4%B8%AA%E8%B6%85%E5%B9%B3%E9%9D%A2%E5%B0%B1%E8%83%BD%E8%A7%A3%E5%86%B3%E3%80%82%E8%BF%99%E9%87%8Ctoken%E5%90%91%E9%87%8F%E9%87%8C%E7%9A%84%E4%BF%A1%E6%81%AF%E9%80%9A%E8%BF%87Feed%20Forward%20Layer%E8%A2%AB%E6%8A%95%E5%BD%B1%E5%88%B0%E6%9B%B4%E9%AB%98%E7%BB%B4%E7%9A%84%E7%A9%BA%E9%97%B4%EF%BC%8C%E5%9C%A8%E9%AB%98%E7%BB%B4%E7%A9%BA%E9%97%B4%E9%87%8C%E5%90%91%E9%87%8F%E7%9A%84%E5%90%84%E7%B1%BB%E4%BF%A1%E6%81%AF%E5%BD%BC%E6%AD%A4%E4%B9%8B%E9%97%B4%E6%9B%B4%E5%AE%B9%E6%98%93%E5%8C%BA%E5%88%AB%E3%80%82%0A**Position%20Embedding**%0Atransformer%E4%BD%BF%E7%94%A8%E7%9A%84%E6%98%AF**%E5%87%BD%E6%95%B0%E5%BC%8F%E7%BB%9D%E5%AF%B9%E4%BD%8D%E7%BD%AE%E7%BC%96%E7%A0%81**%0A%E9%80%9A%E8%BF%87%E5%B0%86token%E6%8C%89%E6%AC%A1%E5%BA%8F%E4%BE%9D%E6%AC%A1%E5%B0%86%E5%85%B6%E4%BC%A0%E5%85%A5RNN%EF%BC%8CRNN%E5%8F%AF%E4%BB%A5%E9%9A%90%E5%90%AB%E7%9A%84%E5%B0%86token%E6%89%80%E5%9C%A8%E7%9A%84%E6%AC%A1%E5%BA%8F%E4%BD%8D%E7%BD%AE%E4%BF%A1%E6%81%AF%E7%BC%96%E7%A0%81%E8%BF%9B%E5%85%B6%E9%9A%90%E8%97%8F%E7%8A%B6%E6%80%81%E9%87%8C%E8%80%8C%E4%BB%8EMulti-Head%20Attention%E7%BB%93%E6%9E%84%E5%8F%AF%E4%BB%A5%E7%9C%8B%E5%87%BA%E6%9D%A5token%E5%90%91%E9%87%8F%E7%9A%84%E4%BF%A1%E6%81%AF%E6%8F%90%E5%8F%96%E6%98%AF%E9%80%9A%E8%BF%87Attention%E6%9C%BA%E5%88%B6%E5%AE%8C%E6%88%90%E7%9A%84%EF%BC%8C%E6%97%A0%E6%B3%95%E5%83%8FRNN%E4%B8%80%E6%A0%B7%E5%8E%BB%E9%9A%90%E5%90%AB%E7%9A%84%E5%B0%86%E6%AC%A1%E5%BA%8F%E4%BD%8D%E7%BD%AE%E4%BF%A1%E6%81%AF%E7%BC%96%E7%A0%81%E8%BF%9B%E5%8E%BB%E3%80%82%0A!%5B1e6ac2e408e77a5a5cd098cdba62e1f5.png%5D(evernotecid%3A%2F%2F50E31125-9E8E-4F15-8430-8CF4D64A8B72%2Fappyinxiangcom%2F30104613%2FENResource%2Fp453)%0ARNN%E4%B8%AD%EF%BC%8C%E7%AC%AC%E4%B8%80%E4%B8%AA%22I%22%E4%B8%8E%E7%AC%AC%E4%BA%8C%E4%B8%AA%22I%22%E7%9A%84%E8%BE%93%E5%87%BA%E8%A1%A8%E5%BE%81%E4%B8%8D%E5%90%8C%EF%BC%8C%E5%9B%A0%E4%B8%BA%E7%94%A8%E4%BA%8E%E7%94%9F%E6%88%90%E8%BF%99%E4%B8%A4%E4%B8%AA%E5%8D%95%E8%AF%8D%E7%9A%84hidden%20states%E6%98%AF%E4%B8%8D%E5%90%8C%E7%9A%84%E3%80%82%E5%AF%B9%E4%BA%8E%E7%AC%AC%E4%B8%80%E4%B8%AA%22I%22%EF%BC%8C%E5%85%B6hidden%20state%E6%98%AF%E5%88%9D%E5%A7%8B%E5%8C%96%E7%9A%84%E7%8A%B6%E6%80%81%EF%BC%9B%E5%AF%B9%E4%BA%8E%E7%AC%AC%E4%BA%8C%E4%B8%AA%22I%22%EF%BC%8C%E5%85%B6hidden%20state%E6%98%AF%E7%BC%96%E7%A0%81%E4%BA%86%22I%20think%20therefore%22%E7%9A%84hidden%20state%E3%80%82%E6%89%80%E4%BB%A5RNN%E7%9A%84hidden%20state%E4%BF%9D%E8%AF%81%E4%BA%86%E5%9C%A8%E5%90%8C%E4%B8%80%E4%B8%AA%E8%BE%93%E5%85%A5%E5%BA%8F%E5%88%97%E4%B8%AD%EF%BC%8C%E4%B8%8D%E5%90%8C%E4%BD%8D%E7%BD%AE%E7%9A%84%E5%90%8C%E6%A0%B7%E7%9A%84%E5%8D%95%E8%AF%8D%E7%9A%84output%20representation%E6%98%AF%E4%B8%8D%E5%90%8C%E7%9A%84%E3%80%82%0A!%5Bc89bfac1c0207a3e0d4235202f7c4cb1.png%5D(evernotecid%3A%2F%2F50E31125-9E8E-4F15-8430-8CF4D64A8B72%2Fappyinxiangcom%2F30104613%2FENResource%2Fp454)%0A%E5%9C%A8self-attention%E4%B8%AD%EF%BC%8C%E7%AC%AC%E4%B8%80%E4%B8%AA%22I%22%E4%B8%8E%E7%AC%AC%E4%BA%8C%E4%B8%AA%22I%22%E7%9A%84%E8%BE%93%E5%87%BA%E5%B0%86%E5%AE%8C%E5%85%A8%E7%9B%B8%E5%90%8C%E3%80%82%E5%9B%A0%E4%B8%BA%E5%AE%83%E4%BB%AC%E7%94%A8%E4%BA%8E%E4%BA%A7%E7%94%9F%E8%BE%93%E5%87%BA%E7%9A%84%E2%80%9Cinput%E2%80%9D%E6%98%AF%E5%AE%8C%E5%85%A8%E7%9B%B8%E5%90%8C%E7%9A%84%E3%80%82%E5%8D%B3%E5%9C%A8%E5%90%8C%E4%B8%80%E4%B8%AA%E8%BE%93%E5%85%A5%E5%BA%8F%E5%88%97%E4%B8%AD%EF%BC%8C%E4%B8%8D%E5%90%8C%E4%BD%8D%E7%BD%AE%E7%9A%84%E7%9B%B8%E5%90%8C%E7%9A%84%E5%8D%95%E8%AF%8D%E7%9A%84output%20representation%E5%AE%8C%E5%85%A8%E7%9B%B8%E5%90%8C%EF%BC%8C%E8%BF%99%E6%A0%B7%E5%B0%B1%E4%B8%8D%E8%83%BD%E4%BD%93%E7%8E%B0%E5%8D%95%E8%AF%8D%E4%B9%8B%E9%97%B4%E7%9A%84%E6%97%B6%E5%BA%8F%E5%85%B3%E7%B3%BB%E3%80%82--%E6%89%80%E4%BB%A5%E8%A6%81%E5%AF%B9%E5%8D%95%E8%AF%8D%E7%9A%84%E6%97%B6%E5%BA%8F%E4%BD%8D%E7%BD%AE%E8%BF%9B%E8%A1%8C%E7%BC%96%E7%A0%81%E8%A1%A8%E5%BE%81%E3%80%82%0A%E5%B0%86%E6%AC%A1%E5%BA%8F%E4%BD%8D%E7%BD%AE%E4%BF%A1%E6%81%AF%E7%BC%96%E7%A0%81%E6%88%90%E5%90%91%E9%87%8F%E5%B9%B6%E7%9B%B4%E6%8E%A5%E5%8A%A0%E5%88%B0%E8%AF%8Dtoken%E5%90%91%E9%87%8F%E4%B8%8A%EF%BC%8C%E4%BD%BF%E7%94%A8%E4%B8%89%E8%A7%92%E5%87%BD%E6%95%B0%E8%AE%A1%E7%AE%97%E4%BD%8D%E7%BD%AE%E7%BC%96%E7%A0%81%E3%80%82%0A**%E4%B8%BA%E4%BB%80%E4%B9%88%E4%BD%BF%E7%94%A8%E4%B8%89%E8%A7%92%E5%87%BD%E6%95%B0%E7%BC%96%E7%A0%81%EF%BC%9A**%0A**%E5%91%A8%E6%9C%9F%E5%87%BD%E6%95%B0%E8%83%BD%E5%BC%95%E5%85%A5%E4%BD%8D%E7%BD%AE%E4%BF%A1%E6%81%AF**%0A%E4%B8%BA%E4%BA%86%E4%BD%93%E7%8E%B0%E6%9F%90%E4%B8%AA%E5%AD%97%E5%9C%A8%E5%8F%A5%E5%AD%90%E4%B8%AD%E7%9A%84%E7%BB%9D%E5%AF%B9%E4%BD%8D%E7%BD%AE%EF%BC%8C%E4%BD%BF%E7%94%A8%E4%BA%86%E4%B8%80%E4%B8%AA%E5%8D%95%E8%B0%83%E7%9A%84%E5%87%BD%E6%95%B0%EF%BC%8C%E4%BD%BF%E5%BE%97%E4%BB%BB%E6%84%8F%E5%90%8E%E7%BB%AD%E7%9A%84%E8%AF%8D%E7%9A%84%E4%BD%8D%E7%BD%AE%E7%BC%96%E7%A0%81%E9%83%BD%E5%A4%A7%E4%BA%8E%E5%89%8D%E9%9D%A2%E7%9A%84%E8%AF%8D%EF%BC%8C%E5%A6%82%E6%9E%9C%E6%88%91%E4%BB%AC%E6%94%BE%E5%BC%83%E5%AF%B9%E7%BB%9D%E5%AF%B9%E4%BD%8D%E7%BD%AE%E7%9A%84%E8%BF%BD%E6%B1%82%EF%BC%8C%E8%BD%AC%E8%80%8C%E8%A6%81%E6%B1%82%E4%BD%8D%E7%BD%AE%E7%BC%96%E7%A0%81%E4%BB%85%E4%BB%85%E5%85%B3%E6%B3%A8%E4%B8%80%E5%AE%9A%E8%8C%83%E5%9B%B4%E5%86%85%E7%9A%84%E7%9B%B8%E5%AF%B9%E6%AC%A1%E5%BA%8F%E5%85%B3%E7%B3%BB%EF%BC%8C%E9%82%A3%E4%B9%88%E4%BD%BF%E7%94%A8%E4%B8%80%E4%B8%AAsin%2Fcos%E5%87%BD%E6%95%B0%E5%B0%B1%E6%98%AF%E5%BE%88%E5%A5%BD%E7%9A%84%E9%80%89%E6%8B%A9%EF%BC%8C%E5%9B%A0%E4%B8%BAsin%2Fcos%E5%87%BD%E6%95%B0%E7%9A%84**%E5%91%A8%E6%9C%9F**%E5%8F%98%E5%8C%96%E8%A7%84%E5%BE%8B%E9%9D%9E%E5%B8%B8%E7%A8%B3%E5%AE%9A%EF%BC%8C%E6%89%80%E4%BB%A5%E7%BC%96%E7%A0%81%E5%85%B7%E6%9C%89%E4%B8%80%E5%AE%9A%E7%9A%84%E4%B8%8D%E5%8F%98%E6%80%A7%E3%80%82%0A%0A**Attention**%0A**transformer%E4%B8%AD%E7%9A%84attention%E5%85%B6%E5%AE%9E%E6%98%AFmulti-head%20attention**%EF%BC%88%E7%B1%BB%E4%BC%BC%E4%BA%8Ecnn%E4%B8%AD%E7%9A%84%E5%A4%9A%E4%B8%AAchannel%2C%E5%8F%AF%E4%BB%A5%E5%8E%BBcapture%E4%B8%8D%E5%90%8C%E7%BB%B4%E5%BA%A6%E5%92%8C%E7%9A%84%E7%89%B9%E5%BE%81%EF%BC%89%E3%80%82%E8%AF%A5%E6%9C%BA%E5%88%B6%E7%90%86%E8%A7%A3%E8%B5%B7%E6%9D%A5%E5%BE%88%E7%AE%80%E5%8D%95%EF%BC%8C%E5%B0%B1%E6%98%AF%E8%AF%B4%E4%B8%8D%E4%BB%85%E4%BB%85%E5%8F%AA%E5%88%9D%E5%A7%8B%E5%8C%96%E4%B8%80%E7%BB%84Q%E3%80%81K%E3%80%81V%E7%9A%84%E7%9F%A9%E9%98%B5%EF%BC%8C%E8%80%8C%E6%98%AF%E5%88%9D%E5%A7%8B%E5%8C%96%E5%A4%9A%E7%BB%84%EF%BC%8Ctranformer%E6%98%AF%E4%BD%BF%E7%94%A8%E4%BA%868%E7%BB%84%EF%BC%8C%E6%89%80%E4%BB%A5%E6%9C%80%E5%90%8E%E5%BE%97%E5%88%B0%E7%9A%84%E7%BB%93%E6%9E%9C%E6%98%AF8%E4%B8%AA%E7%9F%A9%E9%98%B5%E3%80%82%0A**multi-head%20attention%E5%A5%BD%E5%A4%84**%0A*%20%E5%B9%B6%E8%A1%8C%EF%BC%8C%E9%80%9F%E5%BA%A6%E5%BF%AB%0A*%20%E5%AD%A6%E5%88%B0%E6%9B%B4%E5%A4%9A%E7%9A%84%E4%BF%A1%E6%81%AF%0A%0A**transformer%E7%94%A8%E5%88%B0%E7%9A%84mask%EF%BC%9A**%0A**encoder%E4%B8%AD%E7%9A%84attention**%0A%E4%BD%BF%E7%94%A8mask%E8%AE%A9%E5%9C%A8%E4%B8%80%E4%B8%AAbatch%E4%B8%AD%E9%95%BF%E5%BA%A6%E8%BE%83%E7%9F%AD%E5%BA%8F%E5%88%97%E7%9A%84padding%E4%BD%8D%E7%BD%AE%E4%B8%8D%E5%8F%82%E4%B8%8Eattention%E8%AE%A1%E7%AE%97%E3%80%82%0A**decoder%E4%B8%AD%E7%9A%84%E4%B8%A4%E4%B8%AAattention%3A**%0ASelf-Attention%EF%BC%9Akey%2C%20query%2C%20value%E5%9D%87%E6%9D%A5%E8%87%AA%E5%89%8D%E4%B8%80%E5%B1%82decoder%E7%9A%84%E8%BE%93%E5%87%BA%EF%BC%8C%E4%BD%86**%E5%8A%A0%E5%85%A5%E4%BA%86Mask%E6%93%8D%E4%BD%9C**%EF%BC%8C%E5%8D%B3%E4%B8%8D%E8%83%BD%E7%9C%8B%E8%A7%81%E6%9C%AA%E6%9D%A5%E7%9A%84%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%88%91%E4%BB%AC%E5%8F%AA%E8%83%BD%E8%A7%82%E6%B5%8B%E5%88%B0%E5%89%8D%E9%9D%A2%E7%9A%84%E8%AF%8D%E5%92%8C%E5%BD%93%E5%89%8D%E7%9A%84%E8%AF%8D%E4%B9%8B%E9%97%B4%E7%9A%84%E5%85%B3%E7%B3%BB%0A!%5Be32d0a0e363b596e766f76312105f575.png%5D(evernotecid%3A%2F%2F50E31125-9E8E-4F15-8430-8CF4D64A8B72%2Fappyinxiangcom%2F30104613%2FENResource%2Fp451)%0A%0AEncoder-Decoder%20Attention%EF%BC%9Aquery%E6%9D%A5%E8%87%AA%E4%BA%8E%E4%B9%8B%E5%89%8D%E4%B8%80%E7%BA%A7%E7%9A%84decoder%E5%B1%82%E7%9A%84%E8%BE%93%E5%87%BA%EF%BC%88%E6%96%87%E6%9C%AC%E8%BE%93%E5%87%BA%E5%90%91%E9%87%8F%EF%BC%89%EF%BC%8C%E4%BD%86%E5%85%B6key%E5%92%8Cvalue%E6%9D%A5%E8%87%AA%E4%BA%8Eencoder%E7%9A%84%E8%BE%93%E5%87%BA%EF%BC%8C%E8%BF%99%E4%BD%BF%E5%BE%97decoder%E7%9A%84%E6%AF%8F%E4%B8%80%E4%B8%AA%E4%BD%8D%E7%BD%AE%E9%83%BD%E5%8F%AF%E4%BB%A5attend%E5%88%B0%E8%BE%93%E5%85%A5%E5%BA%8F%E5%88%97%E7%9A%84%E6%AF%8F%E4%B8%80%E4%B8%AA%E4%BD%8D%E7%BD%AE%E3%80%82%E8%AE%A1%E7%AE%97%E5%BD%93%E5%89%8D%E8%AF%8D%E5%92%8Cquery%E4%B9%8B%E9%97%B4%E7%9A%84%E5%85%B3%E7%B3%BB%E3%80%82%0A%0A*%20%E4%B8%BA%E4%BB%80%E4%B9%88%E7%94%A8mask%20attention%0A%E9%9C%80%E8%A6%81%E9%98%B2%E6%AD%A2%E6%A0%87%E7%AD%BE%E6%B3%84%E9%9C%B2%EF%BC%8CTransformer%E7%9A%84%E4%BB%BB%E5%8A%A1%E6%98%AFseq2seq%EF%BC%8C%E5%8D%B3%E5%BA%8F%E5%88%97%E7%AC%ACi%E4%B8%AA%E4%BD%8D%E7%BD%AE%E8%A6%81%E7%94%A8%E5%88%B0%E5%89%8Di-1%E6%97%B6%E5%88%BB%E7%9A%84%E4%BF%A1%E6%81%AF%EF%BC%8C%E8%80%8C%E4%B8%8E%E5%85%B6%E5%90%8E%E9%9D%A2%E4%BD%8D%E7%BD%AE%E7%9A%84%E5%85%B6%E5%AE%83%E4%BF%A1%E6%81%AF%E6%97%A0%E5%85%B3%E3%80%82%0A%0A**transformer%E7%9A%84%E6%9D%83%E9%87%8D%E5%85%B1%E4%BA%AB%E4%BD%93%E7%8E%B0%E5%9C%A8%E5%93%AA**%0A*%20encoder%E5%92%8Cdecoder%E5%B1%82%E4%B9%8B%E9%97%B4%E7%9A%84embedding%E5%B1%82%E6%9D%83%E9%87%8D%E5%85%B1%E4%BA%AB%0A*%20decoder%E4%B8%ADembedding%E5%92%8C%E5%85%A8%E8%BF%9E%E6%8E%A5%E5%B1%82%E6%9D%83%E9%87%8D%E5%85%B1%E4%BA%AB%0Atransformer%E8%AF%8D%E8%A1%A8%E4%BD%BF%E7%94%A8bpe%E6%9D%A5%E5%A4%84%E7%90%86%EF%BC%8C%E6%89%80%E4%BB%A5%E6%9C%80%E5%B0%8F%E7%9A%84%E5%8D%95%E5%85%83%E6%98%AFsubword%EF%BC%8C%E5%A6%82%E8%8B%B1%E8%AF%AD%E5%92%8C%E5%BE%B7%E8%AF%AD%E4%B9%8B%E9%97%B4%E7%9A%84%E8%BD%AC%E6%8D%A2%EF%BC%8C%E5%8E%9F%E6%96%87%E6%9C%AC%E5%92%8C%E7%9B%AE%E6%A0%87%E6%96%87%E6%9C%AC%E5%8F%AF%E4%BB%A5%E5%85%B1%E4%BA%AB%E7%B1%BB%E4%BC%BC%E7%9A%84%E8%AF%AD%E4%B9%89%EF%BC%8C%E8%80%8C%E5%83%8F%E4%B8%AD%E8%8B%B1%E8%BF%99%E6%A0%B7%E7%9B%B8%E5%B7%AE%E8%BE%83%E5%A4%A7%E7%9A%84%E8%AF%AD%E7%B3%BB%EF%BC%8C%E6%9D%83%E9%87%8D%E5%85%B1%E4%BA%AB%E4%BD%9C%E7%94%A8%E5%8F%AF%E8%83%BD%E4%B8%8D%E6%98%AF%E5%BE%88%E5%A4%A7%E3%80%82%0A%0A%0A</center></body></html>