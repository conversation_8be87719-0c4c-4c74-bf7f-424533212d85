<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/><meta name="exporter-version" content="Evernote Mac 9.7.14 (474683)"/><meta name="created" content="2021-11-14 07:46:35 +0000"/><meta name="updated" content="2024-03-28 03:50:31 +0000"/><meta name="content-class" content="yinxiang.markdown"/><title>Word2Vec</title></head><body><div style="font-size: 14px; margin: 0; padding: 0; width: 100%;"><p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">Word2Vec</strong><br/>
包括CBOW和Skip-Gram两种模型<br/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">CBOW</strong>（连续词袋模型）：输入一个当前词上下文的词向量，输出所有词的softmax概率，训练的目标是期望当前词对应的目标词的softmax概率最大<br/>
不考虑单词之间的相对距离<br/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">Skip-Gram</strong>：输入当前单词向量，输出上下文<br/>
<img src="Word2Vec.resources/A51055DF-014C-4DC9-9E96-794ECBC46ADA.png" height="537" width="868"/><br/>
传统的神经网络词向量模型，隐藏层到输出的softmax层的计算量很大，因为要计算所有词的softmax概率，再去找概率最大的值对应的词<br/>
<img src="Word2Vec.resources/21EDCCE2-E038-4DF2-9959-A4A23F8E9BD5.png" height="272" width="506"/><br/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">word2vec对传统的模型进行了改进</strong></p>
<ul style="line-height: 160%; box-sizing: content-box; display: block; list-style-type: disc; padding-left: 30px; margin: 6px 0 10px; color: #333;">
<li style="line-height: 160%; box-sizing: content-box; position: relative;">1.对于从输入到隐藏层的映射，<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">没有采取神经网络线性变换加激活函数的方法，而是简单的对所有词向量求和并取平均。</strong></li>
<li style="line-height: 160%; box-sizing: content-box; position: relative;">
<ol start="2" style="line-height: 160%; box-sizing: content-box; display: block; padding-left: 30px; margin: 6px 0 10px; color: #333; list-style-type: decimal; margin-top: 0; margin-bottom: 0;">
<li style="line-height: 160%; box-sizing: content-box; position: relative;">哈夫曼树或者负采样<br/>
1）为了避免计算所有词的softmax概率，word2vec采样了霍夫曼树来代替从隐层到softmax层的映射，根据词频来建立哈夫曼树。将多分类转为二分类问题<br/>
<img src="Word2Vec.resources/FBEFCB0C-B450-4614-BC29-8202BB4B1593.png" height="793" width="751"/><br/>
<img src="Word2Vec.resources/7F313A53-385F-4E7A-8962-B9D2967C8E50.png" height="656" width="1079"/><br/>
<img src="Word2Vec.resources/FA7875FB-F726-400F-AD07-F472A6019B3A.png" height="47" width="612"/><br/>
哈夫曼树的所有内部节点就类似之前神经网络隐藏层的神经元，其中，根节点的词向量对应我们的投影后的词向量，而<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">所有叶子节点就类似于之前神经网络softmax输出层的神经元</strong>，叶子节点的个数就是词汇表的大小。</li>
</ol>
</li>
</ul>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;">使用霍夫曼树来代替传统的神经网络，可以提高模型训练的效率。但是如果我们的训练样本里的中心词是一个很生僻的词，那么就得在霍夫曼树中辛苦的向下走很久了。负采样可以解决该问题<br/>
2）负采样：对于给定的词W的上下文Context(w)，词w是一个正样本，其他词是负样本。<br/>
使用sigmoid函数<br/>
<img src="Word2Vec.resources/0750863C-642F-4E70-A68F-CDA758933409.png" height="242" width="629"/></p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;">负采样的本质：每次让一个训练样本只更新部分权重，其他权重全部固定；减少计算量；（一定程度上还可以增加随机性）<br/>
当我们用训练样本（input word:"fox", output word:"quick"）来训练我们的神经网时，“fox”和“quick”都是经过one-hot编码的。如果我们的vocabulary大小为10000时，在输出层，我们希望“quick”单词那个位置输出1，其余都是0。这些其余我们期望输出0的位置所对应的单词我们成为“negative” word。<br/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">当使用负采样时，我们将随机选择一小部分的negative words（比如选5个negative words）来更新对应的权重。我们也会对我们的positive word进行权重更新</strong>（上面的例子指的是"quick"）。<br/>
假设隐层-输出层拥有300 x 10000的权重矩阵。如果使用了负采样的方法我们仅仅去更新我们的positive word-“quick”的和我们选择的其他5个negative words的结点对应的权重，共计6个输出神经元，相当于每次只更新300* 6 = 1800个权重。<br/>
<img src="Word2Vec.resources/629BF3A3-EB62-4068-9291-540807603C64.png" height="445" width="718"/><br/>
采样方式：<br/>
概率采样，可以根据词频进行随机抽样，我们倾向于选择词频比较大的负样本，比如“的”，这种词语其实是对我们的目标单词没有很大贡献的。Word2vec则在词频基础上取了0.75次幂，减小词频之间差异过大所带来的影响，使得词频比较小的负样本也有机会被采到。</p>
</div><center style="display:none !important;visibility:collapse !important;height:0 !important;white-space:nowrap;width:100%;overflow:hidden">**Word2Vec**%0A%E5%8C%85%E6%8B%ACCBOW%E5%92%8CSkip-Gram%E4%B8%A4%E7%A7%8D%E6%A8%A1%E5%9E%8B%0A**CBOW**%EF%BC%88%E8%BF%9E%E7%BB%AD%E8%AF%8D%E8%A2%8B%E6%A8%A1%E5%9E%8B%EF%BC%89%EF%BC%9A%E8%BE%93%E5%85%A5%E4%B8%80%E4%B8%AA%E5%BD%93%E5%89%8D%E8%AF%8D%E4%B8%8A%E4%B8%8B%E6%96%87%E7%9A%84%E8%AF%8D%E5%90%91%E9%87%8F%EF%BC%8C%E8%BE%93%E5%87%BA%E6%89%80%E6%9C%89%E8%AF%8D%E7%9A%84softmax%E6%A6%82%E7%8E%87%EF%BC%8C%E8%AE%AD%E7%BB%83%E7%9A%84%E7%9B%AE%E6%A0%87%E6%98%AF%E6%9C%9F%E6%9C%9B%E5%BD%93%E5%89%8D%E8%AF%8D%E5%AF%B9%E5%BA%94%E7%9A%84%E7%9B%AE%E6%A0%87%E8%AF%8D%E7%9A%84softmax%E6%A6%82%E7%8E%87%E6%9C%80%E5%A4%A7%0A%E4%B8%8D%E8%80%83%E8%99%91%E5%8D%95%E8%AF%8D%E4%B9%8B%E9%97%B4%E7%9A%84%E7%9B%B8%E5%AF%B9%E8%B7%9D%E7%A6%BB%0A**Skip-Gram**%EF%BC%9A%E8%BE%93%E5%85%A5%E5%BD%93%E5%89%8D%E5%8D%95%E8%AF%8D%E5%90%91%E9%87%8F%EF%BC%8C%E8%BE%93%E5%87%BA%E4%B8%8A%E4%B8%8B%E6%96%87%0A!%5B4ef5128f4aba32aadaddf799e6abdab7.png%5D(en-resource%3A%2F%2Fdatabase%2F3135%3A1)%0A%E4%BC%A0%E7%BB%9F%E7%9A%84%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C%E8%AF%8D%E5%90%91%E9%87%8F%E6%A8%A1%E5%9E%8B%EF%BC%8C%E9%9A%90%E8%97%8F%E5%B1%82%E5%88%B0%E8%BE%93%E5%87%BA%E7%9A%84softmax%E5%B1%82%E7%9A%84%E8%AE%A1%E7%AE%97%E9%87%8F%E5%BE%88%E5%A4%A7%EF%BC%8C%E5%9B%A0%E4%B8%BA%E8%A6%81%E8%AE%A1%E7%AE%97%E6%89%80%E6%9C%89%E8%AF%8D%E7%9A%84softmax%E6%A6%82%E7%8E%87%EF%BC%8C%E5%86%8D%E5%8E%BB%E6%89%BE%E6%A6%82%E7%8E%87%E6%9C%80%E5%A4%A7%E7%9A%84%E5%80%BC%E5%AF%B9%E5%BA%94%E7%9A%84%E8%AF%8D%0A!%5Bf8d8cc111b1f8c5c6ab79303e6bb8142.png%5D(en-resource%3A%2F%2Fdatabase%2F3139%3A1)%0A**word2vec%E5%AF%B9%E4%BC%A0%E7%BB%9F%E7%9A%84%E6%A8%A1%E5%9E%8B%E8%BF%9B%E8%A1%8C%E4%BA%86%E6%94%B9%E8%BF%9B**%0A*%201.%E5%AF%B9%E4%BA%8E%E4%BB%8E%E8%BE%93%E5%85%A5%E5%88%B0%E9%9A%90%E8%97%8F%E5%B1%82%E7%9A%84%E6%98%A0%E5%B0%84%EF%BC%8C**%E6%B2%A1%E6%9C%89%E9%87%87%E5%8F%96%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C%E7%BA%BF%E6%80%A7%E5%8F%98%E6%8D%A2%E5%8A%A0%E6%BF%80%E6%B4%BB%E5%87%BD%E6%95%B0%E7%9A%84%E6%96%B9%E6%B3%95%EF%BC%8C%E8%80%8C%E6%98%AF%E7%AE%80%E5%8D%95%E7%9A%84%E5%AF%B9%E6%89%80%E6%9C%89%E8%AF%8D%E5%90%91%E9%87%8F%E6%B1%82%E5%92%8C%E5%B9%B6%E5%8F%96%E5%B9%B3%E5%9D%87%E3%80%82**%0A*%202.%20%E5%93%88%E5%A4%AB%E6%9B%BC%E6%A0%91%E6%88%96%E8%80%85%E8%B4%9F%E9%87%87%E6%A0%B7%0A1%EF%BC%89%E4%B8%BA%E4%BA%86%E9%81%BF%E5%85%8D%E8%AE%A1%E7%AE%97%E6%89%80%E6%9C%89%E8%AF%8D%E7%9A%84softmax%E6%A6%82%E7%8E%87%EF%BC%8Cword2vec%E9%87%87%E6%A0%B7%E4%BA%86%E9%9C%8D%E5%A4%AB%E6%9B%BC%E6%A0%91%E6%9D%A5%E4%BB%A3%E6%9B%BF%E4%BB%8E%E9%9A%90%E5%B1%82%E5%88%B0softmax%E5%B1%82%E7%9A%84%E6%98%A0%E5%B0%84%EF%BC%8C%E6%A0%B9%E6%8D%AE%E8%AF%8D%E9%A2%91%E6%9D%A5%E5%BB%BA%E7%AB%8B%E5%93%88%E5%A4%AB%E6%9B%BC%E6%A0%91%E3%80%82%E5%B0%86%E5%A4%9A%E5%88%86%E7%B1%BB%E8%BD%AC%E4%B8%BA%E4%BA%8C%E5%88%86%E7%B1%BB%E9%97%AE%E9%A2%98%0A%20!%5B9475b82f0971b7698d5a7985358f5372.png%5D(en-resource%3A%2F%2Fdatabase%2F3138%3A1)%0A!%5B4562b5c8150772a7d7dd5c865d97125e.png%5D(en-resource%3A%2F%2Fdatabase%2F3134%3A1)%0A!%5B136f8e39c3157da50c008d035309c91c.png%5D(en-resource%3A%2F%2Fdatabase%2F3133%3A1)%0A%E5%93%88%E5%A4%AB%E6%9B%BC%E6%A0%91%E7%9A%84%E6%89%80%E6%9C%89%E5%86%85%E9%83%A8%E8%8A%82%E7%82%B9%E5%B0%B1%E7%B1%BB%E4%BC%BC%E4%B9%8B%E5%89%8D%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C%E9%9A%90%E8%97%8F%E5%B1%82%E7%9A%84%E7%A5%9E%E7%BB%8F%E5%85%83%EF%BC%8C%E5%85%B6%E4%B8%AD%EF%BC%8C%E6%A0%B9%E8%8A%82%E7%82%B9%E7%9A%84%E8%AF%8D%E5%90%91%E9%87%8F%E5%AF%B9%E5%BA%94%E6%88%91%E4%BB%AC%E7%9A%84%E6%8A%95%E5%BD%B1%E5%90%8E%E7%9A%84%E8%AF%8D%E5%90%91%E9%87%8F%EF%BC%8C%E8%80%8C**%E6%89%80%E6%9C%89%E5%8F%B6%E5%AD%90%E8%8A%82%E7%82%B9%E5%B0%B1%E7%B1%BB%E4%BC%BC%E4%BA%8E%E4%B9%8B%E5%89%8D%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9Csoftmax%E8%BE%93%E5%87%BA%E5%B1%82%E7%9A%84%E7%A5%9E%E7%BB%8F%E5%85%83**%EF%BC%8C%E5%8F%B6%E5%AD%90%E8%8A%82%E7%82%B9%E7%9A%84%E4%B8%AA%E6%95%B0%E5%B0%B1%E6%98%AF%E8%AF%8D%E6%B1%87%E8%A1%A8%E7%9A%84%E5%A4%A7%E5%B0%8F%E3%80%82%0A%0A%E4%BD%BF%E7%94%A8%E9%9C%8D%E5%A4%AB%E6%9B%BC%E6%A0%91%E6%9D%A5%E4%BB%A3%E6%9B%BF%E4%BC%A0%E7%BB%9F%E7%9A%84%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C%EF%BC%8C%E5%8F%AF%E4%BB%A5%E6%8F%90%E9%AB%98%E6%A8%A1%E5%9E%8B%E8%AE%AD%E7%BB%83%E7%9A%84%E6%95%88%E7%8E%87%E3%80%82%E4%BD%86%E6%98%AF%E5%A6%82%E6%9E%9C%E6%88%91%E4%BB%AC%E7%9A%84%E8%AE%AD%E7%BB%83%E6%A0%B7%E6%9C%AC%E9%87%8C%E7%9A%84%E4%B8%AD%E5%BF%83%E8%AF%8D%E6%98%AF%E4%B8%80%E4%B8%AA%E5%BE%88%E7%94%9F%E5%83%BB%E7%9A%84%E8%AF%8D%EF%BC%8C%E9%82%A3%E4%B9%88%E5%B0%B1%E5%BE%97%E5%9C%A8%E9%9C%8D%E5%A4%AB%E6%9B%BC%E6%A0%91%E4%B8%AD%E8%BE%9B%E8%8B%A6%E7%9A%84%E5%90%91%E4%B8%8B%E8%B5%B0%E5%BE%88%E4%B9%85%E4%BA%86%E3%80%82%E8%B4%9F%E9%87%87%E6%A0%B7%E5%8F%AF%E4%BB%A5%E8%A7%A3%E5%86%B3%E8%AF%A5%E9%97%AE%E9%A2%98%0A2%EF%BC%89%E8%B4%9F%E9%87%87%E6%A0%B7%EF%BC%9A%E5%AF%B9%E4%BA%8E%E7%BB%99%E5%AE%9A%E7%9A%84%E8%AF%8DW%E7%9A%84%E4%B8%8A%E4%B8%8B%E6%96%87Context(w)%EF%BC%8C%E8%AF%8Dw%E6%98%AF%E4%B8%80%E4%B8%AA%E6%AD%A3%E6%A0%B7%E6%9C%AC%EF%BC%8C%E5%85%B6%E4%BB%96%E8%AF%8D%E6%98%AF%E8%B4%9F%E6%A0%B7%E6%9C%AC%E3%80%82%0A%E4%BD%BF%E7%94%A8sigmoid%E5%87%BD%E6%95%B0%0A!%5B5a42cb13580985820bfe203a63d17b9e.png%5D(en-resource%3A%2F%2Fdatabase%2F3136%3A1)%0A%0A%E8%B4%9F%E9%87%87%E6%A0%B7%E7%9A%84%E6%9C%AC%E8%B4%A8%EF%BC%9A%E6%AF%8F%E6%AC%A1%E8%AE%A9%E4%B8%80%E4%B8%AA%E8%AE%AD%E7%BB%83%E6%A0%B7%E6%9C%AC%E5%8F%AA%E6%9B%B4%E6%96%B0%E9%83%A8%E5%88%86%E6%9D%83%E9%87%8D%EF%BC%8C%E5%85%B6%E4%BB%96%E6%9D%83%E9%87%8D%E5%85%A8%E9%83%A8%E5%9B%BA%E5%AE%9A%EF%BC%9B%E5%87%8F%E5%B0%91%E8%AE%A1%E7%AE%97%E9%87%8F%EF%BC%9B%EF%BC%88%E4%B8%80%E5%AE%9A%E7%A8%8B%E5%BA%A6%E4%B8%8A%E8%BF%98%E5%8F%AF%E4%BB%A5%E5%A2%9E%E5%8A%A0%E9%9A%8F%E6%9C%BA%E6%80%A7%EF%BC%89%0A%E5%BD%93%E6%88%91%E4%BB%AC%E7%94%A8%E8%AE%AD%E7%BB%83%E6%A0%B7%E6%9C%AC%EF%BC%88input%20word%3A%22fox%22%2C%20output%20word%3A%22quick%22%EF%BC%89%E6%9D%A5%E8%AE%AD%E7%BB%83%E6%88%91%E4%BB%AC%E7%9A%84%E7%A5%9E%E7%BB%8F%E7%BD%91%E6%97%B6%EF%BC%8C%E2%80%9Cfox%E2%80%9D%E5%92%8C%E2%80%9Cquick%E2%80%9D%E9%83%BD%E6%98%AF%E7%BB%8F%E8%BF%87one-hot%E7%BC%96%E7%A0%81%E7%9A%84%E3%80%82%E5%A6%82%E6%9E%9C%E6%88%91%E4%BB%AC%E7%9A%84vocabulary%E5%A4%A7%E5%B0%8F%E4%B8%BA10000%E6%97%B6%EF%BC%8C%E5%9C%A8%E8%BE%93%E5%87%BA%E5%B1%82%EF%BC%8C%E6%88%91%E4%BB%AC%E5%B8%8C%E6%9C%9B%E2%80%9Cquick%E2%80%9D%E5%8D%95%E8%AF%8D%E9%82%A3%E4%B8%AA%E4%BD%8D%E7%BD%AE%E8%BE%93%E5%87%BA1%EF%BC%8C%E5%85%B6%E4%BD%99%E9%83%BD%E6%98%AF0%E3%80%82%E8%BF%99%E4%BA%9B%E5%85%B6%E4%BD%99%E6%88%91%E4%BB%AC%E6%9C%9F%E6%9C%9B%E8%BE%93%E5%87%BA0%E7%9A%84%E4%BD%8D%E7%BD%AE%E6%89%80%E5%AF%B9%E5%BA%94%E7%9A%84%E5%8D%95%E8%AF%8D%E6%88%91%E4%BB%AC%E6%88%90%E4%B8%BA%E2%80%9Cnegative%E2%80%9D%20word%E3%80%82%0A**%E5%BD%93%E4%BD%BF%E7%94%A8%E8%B4%9F%E9%87%87%E6%A0%B7%E6%97%B6%EF%BC%8C%E6%88%91%E4%BB%AC%E5%B0%86%E9%9A%8F%E6%9C%BA%E9%80%89%E6%8B%A9%E4%B8%80%E5%B0%8F%E9%83%A8%E5%88%86%E7%9A%84negative%20words%EF%BC%88%E6%AF%94%E5%A6%82%E9%80%895%E4%B8%AAnegative%20words%EF%BC%89%E6%9D%A5%E6%9B%B4%E6%96%B0%E5%AF%B9%E5%BA%94%E7%9A%84%E6%9D%83%E9%87%8D%E3%80%82%E6%88%91%E4%BB%AC%E4%B9%9F%E4%BC%9A%E5%AF%B9%E6%88%91%E4%BB%AC%E7%9A%84positive%20word%E8%BF%9B%E8%A1%8C%E6%9D%83%E9%87%8D%E6%9B%B4%E6%96%B0**%EF%BC%88%E4%B8%8A%E9%9D%A2%E7%9A%84%E4%BE%8B%E5%AD%90%E6%8C%87%E7%9A%84%E6%98%AF%22quick%22%EF%BC%89%E3%80%82%0A%E5%81%87%E8%AE%BE%E9%9A%90%E5%B1%82-%E8%BE%93%E5%87%BA%E5%B1%82%E6%8B%A5%E6%9C%89300%20x%2010000%E7%9A%84%E6%9D%83%E9%87%8D%E7%9F%A9%E9%98%B5%E3%80%82%E5%A6%82%E6%9E%9C%E4%BD%BF%E7%94%A8%E4%BA%86%E8%B4%9F%E9%87%87%E6%A0%B7%E7%9A%84%E6%96%B9%E6%B3%95%E6%88%91%E4%BB%AC%E4%BB%85%E4%BB%85%E5%8E%BB%E6%9B%B4%E6%96%B0%E6%88%91%E4%BB%AC%E7%9A%84positive%20word-%E2%80%9Cquick%E2%80%9D%E7%9A%84%E5%92%8C%E6%88%91%E4%BB%AC%E9%80%89%E6%8B%A9%E7%9A%84%E5%85%B6%E4%BB%965%E4%B8%AAnegative%20words%E7%9A%84%E7%BB%93%E7%82%B9%E5%AF%B9%E5%BA%94%E7%9A%84%E6%9D%83%E9%87%8D%EF%BC%8C%E5%85%B1%E8%AE%A16%E4%B8%AA%E8%BE%93%E5%87%BA%E7%A5%9E%E7%BB%8F%E5%85%83%EF%BC%8C%E7%9B%B8%E5%BD%93%E4%BA%8E%E6%AF%8F%E6%AC%A1%E5%8F%AA%E6%9B%B4%E6%96%B0300*%206%20%3D%201800%E4%B8%AA%E6%9D%83%E9%87%8D%E3%80%82%0A!%5B8827306ee7b7c846832d14fff52dd7d8.png%5D(en-resource%3A%2F%2Fdatabase%2F3137%3A1)%0A%E9%87%87%E6%A0%B7%E6%96%B9%E5%BC%8F%EF%BC%9A%0A%E6%A6%82%E7%8E%87%E9%87%87%E6%A0%B7%EF%BC%8C%E5%8F%AF%E4%BB%A5%E6%A0%B9%E6%8D%AE%E8%AF%8D%E9%A2%91%E8%BF%9B%E8%A1%8C%E9%9A%8F%E6%9C%BA%E6%8A%BD%E6%A0%B7%EF%BC%8C%E6%88%91%E4%BB%AC%E5%80%BE%E5%90%91%E4%BA%8E%E9%80%89%E6%8B%A9%E8%AF%8D%E9%A2%91%E6%AF%94%E8%BE%83%E5%A4%A7%E7%9A%84%E8%B4%9F%E6%A0%B7%E6%9C%AC%EF%BC%8C%E6%AF%94%E5%A6%82%E2%80%9C%E7%9A%84%E2%80%9D%EF%BC%8C%E8%BF%99%E7%A7%8D%E8%AF%8D%E8%AF%AD%E5%85%B6%E5%AE%9E%E6%98%AF%E5%AF%B9%E6%88%91%E4%BB%AC%E7%9A%84%E7%9B%AE%E6%A0%87%E5%8D%95%E8%AF%8D%E6%B2%A1%E6%9C%89%E5%BE%88%E5%A4%A7%E8%B4%A1%E7%8C%AE%E7%9A%84%E3%80%82Word2vec%E5%88%99%E5%9C%A8%E8%AF%8D%E9%A2%91%E5%9F%BA%E7%A1%80%E4%B8%8A%E5%8F%96%E4%BA%860.75%E6%AC%A1%E5%B9%82%EF%BC%8C%E5%87%8F%E5%B0%8F%E8%AF%8D%E9%A2%91%E4%B9%8B%E9%97%B4%E5%B7%AE%E5%BC%82%E8%BF%87%E5%A4%A7%E6%89%80%E5%B8%A6%E6%9D%A5%E7%9A%84%E5%BD%B1%E5%93%8D%EF%BC%8C%E4%BD%BF%E5%BE%97%E8%AF%8D%E9%A2%91%E6%AF%94%E8%BE%83%E5%B0%8F%E7%9A%84%E8%B4%9F%E6%A0%B7%E6%9C%AC%E4%B9%9F%E6%9C%89%E6%9C%BA%E4%BC%9A%E8%A2%AB%E9%87%87%E5%88%B0%E3%80%82%0A%0A</center></body></html>