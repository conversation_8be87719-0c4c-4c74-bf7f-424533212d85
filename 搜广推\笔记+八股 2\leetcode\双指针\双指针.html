<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/><meta name="exporter-version" content="Evernote Mac 9.7.14 (474683)"/><meta name="author" content="安能行叹复坐愁"/><meta name="created" content="2022-11-18 08:34:06 +0000"/><meta name="source" content="desktop.win"/><meta name="source-application" content="yinxiang.win32"/><meta name="source-url" content="https://leetcode.cn/problems/remove-element/submissions/383064286/"/><meta name="updated" content="2024-04-10 02:07:47 +0000"/><meta name="application-data:yinxiang.superNote" content="{&quot;aiMode&quot;:false}"/><title>双指针</title></head><body style="font-size: 16px;"><div><span style="font-size: 16px;"><span style="font-size: 13px; font-weight: bold;">1.快慢指针</span></span></div><div><span style="font-size: 13px; font-weight: bold;">  移除元素</span></div><div><font style="font-size: 13px;"><span style="font-size: 13px; font-weight: bold;">  </span><span style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; font-size: 13px; color: black; font-variant-caps: normal; font-variant-ligatures: normal; font-weight: bold; line-height: 1.5;">删除有序数组中的重复项</span></font></div><div><font style="font-size: 13px;"><span style="font-size: 13px; font-weight: bold;">  </span><span style="font-size: 13px; font-weight: bold;">移动零（把0移到数组后方）</span></font></div><div><span style="font-size: 13px; font-weight: bold;">2.有序数组的平方</span></div><div><span style="font-size: 13px; font-weight: bold;">3.判断a是否是b的子序列</span></div><div><span style="font-size: 13px; font-weight: bold;">4.三数之和、最接近的三数之和</span></div><div><font style="font-size: 13px;"><span style="font-size: 13px; font-weight: bold;">5.合并两个有序数组（扩容，不创建新数组）</span></font></div><div><font style="font-size: 13px;"><span style="font-size: 13px; font-weight: bold;">6.接雨水</span></font></div><div><font style="font-size: 13px;"><span style="font-size: 13px; font-weight: bold;">  </span></font><span style="font-size: 13px; font-weight: bold;">盛水最多的容器 </span></div><div><span style="font-size: 13px; color: unset; font-weight: bold;">7.旋转数组</span></div><div><font style="font-size: 13px;"><span style="font-size: 13px; font-weight: bold;">8.颜色分类</span></font></div><div><span style="font-size: 13px; font-weight: bold;">9.至少有K个重复字符的最长子串</span></div><div><span style="font-size: 13px; font-weight: bold;">10.等差数列划分（梯形公式）</span></div><div><span style="font-size: 13px; font-weight: bold;">11.替换后的最长重复字符（滑动窗口） </span></div><div><font style="font-size: 13px;"><span style="font-size: 13px; font-weight: bold;">12.</span><span style="font-size: 13px; font-weight: bold;">调整数组顺序使奇数位于偶数前面</span></font></div><div><span style="font-size: 13px; font-weight: bold;">13.找到k个最接近的元素</span></div><div><span style="font-size: 13px; font-weight: bold;">15.重构字符串（使得相邻的两个字符不同）</span></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px; font-weight: bold;">1.移除元素</span></div><div><span style="font-size: 13px;">给你一个数组 nums 和一个值 val，你需要</span> <span style="font-size: 13px; font-weight: bold;">原地移除</span><span style="font-size: 13px;">所有数值等于 val 的元素，并返回移除后数组的新长度。 </span></div><div><span style="font-size: 13px;">思路：快慢指针。快指针一直往前走，把不是目标值的元素，赋给慢指针所在位置。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><span style="color: rgb(0, 0, 0);"><font face="Courier New" style="font-size: 13px;">    def removeElement(self, nums, val):</font></span></div><div><span style="color: rgb(0, 0, 0);"><font face="Courier New" style="font-size: 13px;">        slow = fast = 0</font></span></div><div><span style="color: rgb(0, 0, 0);"><font face="Courier New" style="font-size: 13px;">        while fast&lt;len(nums):</font></span></div><div><span style="color: rgb(0, 0, 0);"><font face="Courier New" style="font-size: 13px;">            if nums[fast]!=val:</font></span></div><div><span style="color: rgb(0, 0, 0);"><font face="Courier New" style="font-size: 13px;">                nums[slow] = nums[fast]</font></span></div><div><span style="color: rgb(0, 0, 0);"><font face="Courier New" style="font-size: 13px;">                slow = slow + 1</font></span></div><div><span style="color: rgb(0, 0, 0);"><font face="Courier New" style="font-size: 13px;">            fast = fast + 1</font></span></div><div><span style="color: rgb(0, 0, 0);"><font face="Courier New" style="font-size: 13px;">        return slow</font></span></div></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><span style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal;"><span style="font-size: 13px; color: black; font-variant-caps: normal; font-variant-ligatures: normal; font-weight: bold; line-height: 1.5;">1.删除有序数组中的重复项</span></span></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><font style="font-size: 13px;"><span style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; font-size: 13px; color: black; font-variant-caps: normal; font-variant-ligatures: normal; line-height: 1.5;">给定一个</span><span style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; font-size: 13px; color: rgb(255, 38, 0); font-variant-caps: normal; font-variant-ligatures: normal; line-height: 1.5;">升序数组</span><span style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; font-size: 13px; color: black; font-variant-caps: normal; font-variant-ligatures: normal; line-height: 1.5;">，删除重复元素（将不重复的元素放到前k个位置，返回nums[:k]）</span></font></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><font style="font-size: 13px;"><span style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; font-size: 13px; color: black; font-variant-caps: normal; font-variant-ligatures: normal; line-height: 1.5;">思路：快慢指针。</span><span style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; font-size: 13px;">if nums[fast] != nums[slow]:</span><span style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; font-size: 13px; color: black; font-variant-caps: normal; font-variant-ligatures: normal; line-height: 1.5;">就把新元素赋给慢指针，此时慢指针才会走一步。</span></font></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><span style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal;"><span style="font-size: 13px; color: black; font-variant-caps: normal; font-variant-ligatures: normal; line-height: 1.5;">注意，快慢指针都要从1开始，而不是0。</span></span></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 13px;">def removeDuplicates(self, nums: List[int]) -&gt; int:</font></div><div><font style="font-size: 13px;">    if len(nums) == 0:</font></div><div><font style="font-size: 13px;">        return []</font></div><div><font style="font-size: 13px;">    slow = 0 # slow是比较的位置，slow+1是插入的位置</font></div><div><font style="font-size: 13px;">    fast = 1</font></div><div><font style="font-size: 13px;">    while fast &lt; len(nums):</font></div><div><font style="font-size: 13px;">        if nums[fast] != nums[slow]:</font></div><div><font style="font-size: 13px;">            nums[slow + 1] = nums[fast]</font></div><div><font style="font-size: 13px;">            slow += 1</font></div><div><font style="font-size: 13px;">        fast += 1</font></div><div><font style="font-size: 13px;">    return slow + 1</font></div></div></div><div><span style="font-size: 13px; font-weight: bold;">移动零</span></div><div><span style="font-size: 13px; font-weight: bold;">把0元素全都移动到数组后方。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font face="Courier New" style="font-size: 13px;">    def moveZeroes(self, nums):</font></div><div><font face="Courier New" style="font-size: 13px;">        slow = 0</font></div><div><font face="Courier New" style="font-size: 13px;">        fast = 0</font></div><div><font face="Courier New" style="font-size: 13px;">        while fast&lt;len(nums):</font></div><div><font face="Courier New" style="font-size: 13px;">            if nums[fast]!=0:</font></div><div><font face="Courier New" style="font-size: 13px;">                nums[slow] = nums[fast]# slow是插入位置</font></div><div><font face="Courier New" style="font-size: 13px;">                slow = slow + 1</font></div><div><font face="Courier New" style="font-size: 13px;">            fast = fast + 1</font></div><div><font face="Courier New" style="font-size: 13px;">        for i in range(slow, fast):</font></div><div><font face="Courier New" style="font-size: 13px;">            nums[i] = 0</font></div><div><font face="Courier New" style="font-size: 13px;">        return nums</font></div></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px; font-weight: bold;">2.有序数组的平方</span></div><div><span style="font-size: 13px;">给你一个按 非递减顺序 排序的整数数组 nums，返回 每个数字的平方 组成的新数组，要求也按 非递减顺序 排序。</span></div><div><span style="font-size: 13px;">思路：直接平方排序，时间复杂度是O(nlogn)。 改进：最大的数肯定在左右两边，1.创建一个新数组 2.nums[left]的平方和num[right]的平方，把大的写入新数组</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font face="Courier New" style="font-size: 13px;">    def sortedSquares(self, nums):</font></div><div><font face="Courier New" style="font-size: 13px;">        left = 0</font></div><div><font face="Courier New" style="font-size: 13px;">        right = len(nums)-1</font></div><div><font face="Courier New" style="font-size: 13px;">        new = [-1]*len(nums)</font></div><div><font face="Courier New" style="font-size: 13px;">        new_index = len(new) - 1</font></div><div><font face="Courier New" style="font-size: 13px;">        while left&lt;=right:</font></div><div><font face="Courier New" style="font-size: 13px;">            if (nums[left]*nums[left])&lt;(nums[right]*nums[right]):</font></div><div><font face="Courier New" style="font-size: 13px;">                new[new_index] = nums[right]*nums[right]</font></div><div><font face="Courier New" style="font-size: 13px;">                right = right - 1</font></div><div><font face="Courier New" style="font-size: 13px;">                new_index -=1</font></div><div><font face="Courier New" style="font-size: 13px;">            else:</font></div><div><font face="Courier New" style="font-size: 13px;">                new[new_index] = nums[left]*nums[left]</font></div><div><font face="Courier New" style="font-size: 13px;">                left = left + 1</font></div><div><font face="Courier New" style="font-size: 13px;">                new_index -=1</font></div><div><font face="Courier New" style="font-size: 13px;">        return new</font></div><div><font face="Courier New" style="font-size: 13px;"><br/></font></div></div><div><span style="font-size: 13px; font-weight: bold;">3，判断子序列</span></div><div><span style="font-size: 13px;">判断s是否是t的子序列。</span></div><div><span style="font-size: 13px;">双指针：指针1指向短的，指针2指向长的。长的一直走，只要长的和短的元素相等，短的走一步。 如果最后短的走完所有元素，返回true。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font face="Courier New" style="font-size: 13px;">    def isSubsequence(self, s, t):</font></div><div><font face="Courier New" style="font-size: 13px;">        zz_1 = 0</font></div><div><font face="Courier New" style="font-size: 13px;">        zz_2 = 0</font></div><div><font face="Courier New" style="font-size: 13px;">        while zz_1&lt;len(s) and zz_2&lt;len(t):</font></div><div><font face="Courier New" style="font-size: 13px;">            if s[zz_1]==t[zz_2]:</font></div><div><font face="Courier New" style="font-size: 13px;">                zz_1 = zz_1 + 1</font></div><div><font face="Courier New" style="font-size: 13px;">                zz_2 = zz_2 + 1</font></div><div><font face="Courier New" style="font-size: 13px;">            else:</font></div><div><font face="Courier New" style="font-size: 13px;">                zz_2 = zz_2 + 1</font></div><div><font face="Courier New" style="font-size: 13px;">       </font></div><div><font face="Courier New" style="font-size: 13px;">        return zz_1==len(s)</font></div></div><div><span style="font-size: 13px; font-weight: bold;">4.三数之和</span></div><div style="border-width: 0px; border-style: solid; border-color: hsl(var(--sd-border)); box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x: 0; --tw-border-spacing-y: 0; --tw-translate-x: 0; --tw-translate-y: 0; --tw-rotate: 0; --tw-skew-x: 0; --tw-skew-y: 0; --tw-scale-x: 1; --tw-scale-y: 1; --tw-scroll-snap-strictness: proximity; --tw-ring-offset-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: #3b82f680; --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; margin: 0px 0px 1rem; font-size: 14px; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240);"><span style="border-width: 0px; border-style: solid; border-color: hsl(var(--sd-border)); box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 14px; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240); color: rgb(38, 38, 38); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-variant-caps: normal; font-variant-ligatures: normal;">给你一个整数数组</span><span style="border-width: 0px; border-style: solid; border-color: hsl(var(--sd-border)); box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 14px; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240); color: rgb(38, 38, 38); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-variant-caps: normal; font-variant-ligatures: normal;"> </span><span style="box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 0.75rem; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: pre-wrap; background-color: rgba(0, 10, 32, 0.03); border: 1px solid rgba(0, 0, 0, 0.05); border-top-left-radius: 5px; border-top-right-radius: 5px; border-bottom-right-radius: 5px; border-bottom-left-radius: 5px; color: rgba(38, 38, 38, 0.75); font-family: Menlo, sans-serif; font-variant-caps: normal; font-variant-ligatures: normal; line-height: 1rem;">nums</span><span style="border-width: 0px; border-style: solid; border-color: hsl(var(--sd-border)); box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 14px; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240); color: rgb(38, 38, 38); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-variant-caps: normal; font-variant-ligatures: normal;"> </span><span style="border-width: 0px; border-style: solid; border-color: hsl(var(--sd-border)); box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 14px; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240); color: rgb(38, 38, 38); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-variant-caps: normal; font-variant-ligatures: normal;">，判断是否存在三元组</span><span style="border-width: 0px; border-style: solid; border-color: hsl(var(--sd-border)); box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 14px; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240); color: rgb(38, 38, 38); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-variant-caps: normal; font-variant-ligatures: normal;"> </span><span style="box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 0.75rem; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: pre-wrap; background-color: rgba(0, 10, 32, 0.03); border: 1px solid rgba(0, 0, 0, 0.05); border-top-left-radius: 5px; border-top-right-radius: 5px; border-bottom-right-radius: 5px; border-bottom-left-radius: 5px; color: rgba(38, 38, 38, 0.75); font-family: Menlo, sans-serif; font-variant-caps: normal; font-variant-ligatures: normal; line-height: 1rem;">[nums[i], nums[j], nums[k]]</span><span style="border-width: 0px; border-style: solid; border-color: hsl(var(--sd-border)); box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 14px; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240); color: rgb(38, 38, 38); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-variant-caps: normal; font-variant-ligatures: normal;"> </span><span style="border-width: 0px; border-style: solid; border-color: hsl(var(--sd-border)); box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 14px; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240); color: rgb(38, 38, 38); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-variant-caps: normal; font-variant-ligatures: normal;">满足</span><span style="border-width: 0px; border-style: solid; border-color: hsl(var(--sd-border)); box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 14px; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240); color: rgb(38, 38, 38); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-variant-caps: normal; font-variant-ligatures: normal;"> </span><span style="box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 0.75rem; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: pre-wrap; background-color: rgba(0, 10, 32, 0.03); border: 1px solid rgba(0, 0, 0, 0.05); border-top-left-radius: 5px; border-top-right-radius: 5px; border-bottom-right-radius: 5px; border-bottom-left-radius: 5px; color: rgba(38, 38, 38, 0.75); font-family: Menlo, sans-serif; font-variant-caps: normal; font-variant-ligatures: normal; line-height: 1rem;">i != j</span><span style="border-width: 0px; border-style: solid; border-color: hsl(var(--sd-border)); box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 14px; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240); color: rgb(38, 38, 38); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-variant-caps: normal; font-variant-ligatures: normal;">、</span><span style="box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 0.75rem; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: pre-wrap; background-color: rgba(0, 10, 32, 0.03); border: 1px solid rgba(0, 0, 0, 0.05); border-top-left-radius: 5px; border-top-right-radius: 5px; border-bottom-right-radius: 5px; border-bottom-left-radius: 5px; color: rgba(38, 38, 38, 0.75); font-family: Menlo, sans-serif; font-variant-caps: normal; font-variant-ligatures: normal; line-height: 1rem;">i != k</span><span style="border-width: 0px; border-style: solid; border-color: hsl(var(--sd-border)); box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 14px; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240); color: rgb(38, 38, 38); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-variant-caps: normal; font-variant-ligatures: normal;"> </span><span style="border-width: 0px; border-style: solid; border-color: hsl(var(--sd-border)); box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 14px; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240); color: rgb(38, 38, 38); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-variant-caps: normal; font-variant-ligatures: normal;">且</span><span style="border-width: 0px; border-style: solid; border-color: hsl(var(--sd-border)); box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 14px; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240); color: rgb(38, 38, 38); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-variant-caps: normal; font-variant-ligatures: normal;"> </span><span style="box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 0.75rem; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: pre-wrap; background-color: rgba(0, 10, 32, 0.03); border: 1px solid rgba(0, 0, 0, 0.05); border-top-left-radius: 5px; border-top-right-radius: 5px; border-bottom-right-radius: 5px; border-bottom-left-radius: 5px; color: rgba(38, 38, 38, 0.75); font-family: Menlo, sans-serif; font-variant-caps: normal; font-variant-ligatures: normal; line-height: 1rem;">j != k</span><span style="border-width: 0px; border-style: solid; border-color: hsl(var(--sd-border)); box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 14px; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240); color: rgb(38, 38, 38); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-variant-caps: normal; font-variant-ligatures: normal;"> </span><span style="border-width: 0px; border-style: solid; border-color: hsl(var(--sd-border)); box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 14px; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240); color: rgb(38, 38, 38); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-variant-caps: normal; font-variant-ligatures: normal;">，同时还满足</span><span style="border-width: 0px; border-style: solid; border-color: hsl(var(--sd-border)); box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 14px; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240); color: rgb(38, 38, 38); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-variant-caps: normal; font-variant-ligatures: normal;"> </span><span style="box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 0.75rem; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: pre-wrap; background-color: rgba(0, 10, 32, 0.03); border: 1px solid rgba(0, 0, 0, 0.05); border-top-left-radius: 5px; border-top-right-radius: 5px; border-bottom-right-radius: 5px; border-bottom-left-radius: 5px; color: rgba(38, 38, 38, 0.75); font-family: Menlo, sans-serif; font-variant-caps: normal; font-variant-ligatures: normal; line-height: 1rem;">nums[i] + nums[j] + nums[k] == 0</span><span style="border-width: 0px; border-style: solid; border-color: hsl(var(--sd-border)); box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 14px; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240); color: rgb(38, 38, 38); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-variant-caps: normal; font-variant-ligatures: normal;"> </span><span style="border-width: 0px; border-style: solid; border-color: hsl(var(--sd-border)); box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 14px; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240); color: rgb(38, 38, 38); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-variant-caps: normal; font-variant-ligatures: normal;">。请</span></div><div style="border-width: 0px; border-style: solid; border-color: hsl(var(--sd-border)); box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x: 0; --tw-border-spacing-y: 0; --tw-translate-x: 0; --tw-translate-y: 0; --tw-rotate: 0; --tw-skew-x: 0; --tw-skew-y: 0; --tw-scale-x: 1; --tw-scale-y: 1; --tw-scroll-snap-strictness: proximity; --tw-ring-offset-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: #3b82f680; --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; margin: 0px 0px 1rem; font-size: 14px; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240);"><span style="border-width: 0px; border-style: solid; border-color: hsl(var(--sd-border)); box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 14px; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240); color: rgb(38, 38, 38); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-variant-caps: normal; font-variant-ligatures: normal;">你返回所有和为</span><span style="border-width: 0px; border-style: solid; border-color: hsl(var(--sd-border)); box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 14px; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240); color: rgb(38, 38, 38); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-variant-caps: normal; font-variant-ligatures: normal;"> </span><span style="box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 0.75rem; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: pre-wrap; background-color: rgba(0, 10, 32, 0.03); border: 1px solid rgba(0, 0, 0, 0.05); border-top-left-radius: 5px; border-top-right-radius: 5px; border-bottom-right-radius: 5px; border-bottom-left-radius: 5px; color: rgba(38, 38, 38, 0.75); font-family: Menlo, sans-serif; font-variant-caps: normal; font-variant-ligatures: normal; line-height: 1rem;">0</span><span style="border-width: 0px; border-style: solid; border-color: hsl(var(--sd-border)); box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 14px; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240); color: rgb(38, 38, 38); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-variant-caps: normal; font-variant-ligatures: normal;"> </span><span style="border-width: 0px; border-style: solid; border-color: hsl(var(--sd-border)); box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 14px; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240); color: rgb(38, 38, 38); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-variant-caps: normal; font-variant-ligatures: normal;">且不重复的三元组。</span></div><div style="text-align: start;"><span style="font-size: 13px;">nums = [-1,0,1,2,-1,-4]</span></div><div style="text-align: start;"><span style="font-size: 13px;">输出：[[-1,-1,2],[-1,0,1]]</span></div><div style="text-align: start;"><span style="font-size: 13px;">思路：1.将数组排序。 2.固定一个nums[i]，那么第二个数的下标是i+1，第三个数的下标是数组末尾。 如果当前三数之和&gt;0，第三个数就往前移动，如果小于0，第二个数就往后移动（因为数组是排序的）</span></div><div style="text-align: start;"><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font face="Courier New" style="font-size: 13px;">    def threeSum(num):</font></div><div><font face="Courier New" style="font-size: 13px;">        result=[]</font></div><div><font face="Courier New" style="font-size: 13px;">        num.sort()</font></div><div><font face="Courier New" style="font-size: 13px;">        for i in range(len(num)):</font></div><div><font face="Courier New" style="font-size: 13px;">            left=i+1</font></div><div><font face="Courier New" style="font-size: 13px;">            right=len(num)-1</font></div><div><font face="Courier New" style="font-size: 13px;">            if i&gt;0 and num[i]==num[i-1]: # 去重1</font></div><div><font face="Courier New" style="font-size: 13px;">                continue</font></div><div><font face="Courier New" style="font-size: 13px;">            while left&lt;right: # 不一定只有一组left,right，用while找到所有的</font></div><div><font face="Courier New" style="font-size: 13px;">                total=num[i]+num[left]+num[right]</font></div><div><font face="Courier New" style="font-size: 13px;">                if total&lt;0:</font></div><div><font face="Courier New" style="font-size: 13px;">                    left=left+1</font></div><div><font face="Courier New" style="font-size: 13px;">                elif total&gt;0:</font></div><div><font face="Courier New" style="font-size: 13px;">                    right=right-1</font></div><div><font face="Courier New" style="font-size: 13px;">                else:</font></div><div><font face="Courier New" style="font-size: 13px;">                    result.append([num[i],num[left],num[right]])</font></div><div><font face="Courier New" style="font-size: 13px;">                    while left!=right and num[left]==num[left+1]: # 去重2，<b>一定要注意，先写left!=right</b></font></div><div><font face="Courier New" style="font-size: 13px;"><b>                        left=left+1</b></font></div><div><font face="Courier New" style="font-size: 13px;">                    while left!=right and num[right]==num[right-1]:</font></div><div><font face="Courier New" style="font-size: 13px;">                        right=right-1</font></div><div><font face="Courier New" style="font-size: 13px;">                    left=left+1</font></div><div><font face="Courier New" style="font-size: 13px;">                    right=right-1</font></div><div><font face="Courier New" style="font-size: 13px;">        return result</font></div></div></div><div><span style="font-size: 13px;">最接近的三数之和：</span></div><div><span style="font-size: 13px;">找三个数，最接近target。还是套用三数之和的思想，移动指针之前多一步判断。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font face="Courier New" style="font-size: 13px;">    def threeSumClosest(self, nums, target):</font></div><div><font face="Courier New" style="font-size: 13px;">        nums.sort()</font></div><div><font face="Courier New" style="font-size: 13px;">        result = -9999999</font></div><div><font face="Courier New" style="font-size: 13px;">        for i in range(len(nums)):</font></div><div><font face="Courier New" style="font-size: 13px;">            left = i + 1</font></div><div><font face="Courier New" style="font-size: 13px;">            right = len(nums)-1</font></div><div><font face="Courier New" style="font-size: 13px;">            if i&gt;0 and nums[i-1]==nums[i]:</font></div><div><font face="Courier New" style="font-size: 13px;">                continue</font></div><div><font face="Courier New" style="font-size: 13px;">            while left&lt;right:</font></div><div><font face="Courier New" style="font-size: 13px;">                sum_ = nums[i]+nums[left]+nums[right]</font></div><div><font face="Courier New" style="font-size: 13px;">                if abs(target-sum_)&lt;abs(target-result): # 一定要先判断这一步</font></div><div><font face="Courier New" style="font-size: 13px;">                    result = sum_</font></div><div><font face="Courier New" style="font-size: 13px;">                if sum_&gt;target:  # 再移动指针</font></div><div><font face="Courier New" style="font-size: 13px;">                    right = right-1</font></div><div><font face="Courier New" style="font-size: 13px;">                elif sum_&lt;target:</font></div><div><font face="Courier New" style="font-size: 13px;">                    left = left + 1</font></div><div><font face="Courier New" style="font-size: 13px;">                else:</font></div><div><font face="Courier New" style="font-size: 13px;">                    return result</font></div><div><font face="Courier New" style="font-size: 13px;">        return result</font></div></div><div><span style="font-size: 13px; font-weight: bold;">5.合并两个有序数组（不创建新数组）</span></div><div><span style="font-size: 13px;">给出一个有序的整数数组 A 和有序的整数数组 B ，请将数组 B 合并到数组 A 中，变成一个有序的升序数组</span></div><div><span style="font-size: 13px;"><img src="%E5%8F%8C%E6%8C%87%E9%92%88.resources/B62CFA9C-ECBE-4B0A-B55D-AD84517F7E9E.png" height="102" width="867"/><br/></span></div><div><span style="font-size: 13px;">思路：zz1指向原来的A数组末尾，zz2指向B数组末尾，k指向扩容后的A数组末尾。</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font face="Courier New" style="font-size: 13px;">    def merge(self , A, m, B, n):</font></div><div><font face="Courier New" style="font-size: 13px;">        k = m + n - 1</font></div><div><font face="Courier New" style="font-size: 13px;">        zz1 = m-1</font></div><div><font face="Courier New" style="font-size: 13px;">        zz2 = n-1</font></div><div><font face="Courier New" style="font-size: 13px;">        while zz1 &gt;= 0 and zz2 &gt;=0:</font></div><div><font face="Courier New" style="font-size: 13px;">            if A[zz1] &gt; B[zz2]:</font></div><div><font face="Courier New" style="font-size: 13px;">                A[k] = A[zz1]</font></div><div><font face="Courier New" style="font-size: 13px;">                zz1 = zz1 - 1</font></div><div><font face="Courier New" style="font-size: 13px;">                k = k - 1</font></div><div><font face="Courier New" style="font-size: 13px;">            else:</font></div><div><font face="Courier New" style="font-size: 13px;">                A[k] = B[zz2]</font></div><div><font face="Courier New" style="font-size: 13px;">                zz2 = zz2-1</font></div><div><font face="Courier New" style="font-size: 13px;">                k = k-1</font></div><div><font face="Courier New" style="font-size: 13px;">        if zz1&lt;0: # 若B数组的已经放完，说明A数组前面的就是最小的，不用考虑。</font></div><div><font face="Courier New" style="font-size: 13px;">            while zz2&gt;=0:</font></div><div><font face="Courier New" style="font-size: 13px;">                A[k] = B[zz2]</font></div><div><font face="Courier New" style="font-size: 13px;">                k = k-1</font></div><div><font face="Courier New" style="font-size: 13px;">                zz2 = zz2-1</font></div><div><font face="Courier New" style="font-size: 13px;">        return A</font></div></div><div><font style="font-size: 13px;"><br/></font></div><div style="text-align: start;"><span style="font-size: 13px; font-weight: bold;">6.接雨水问题</span></div><div style="text-align: start;"><span style="font-size: 13px;"><img src="%E5%8F%8C%E6%8C%87%E9%92%88.resources/36340D03-E5ED-46DF-8230-01AF4F2F6B0B.png" height="315" width="409"/><br/></span></div><div style="text-align: start;"><span style="font-size: 14px; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240); color: rgb(38, 38, 38); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-variant-caps: normal; font-variant-ligatures: normal;">给定 </span><span style="border: 1px solid rgba(0, 0, 0, 0.05); box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 0.75rem; background-color: rgba(0, 10, 32, 0.03); border-top-left-radius: 5px; border-top-right-radius: 5px; border-bottom-right-radius: 5px; border-bottom-left-radius: 5px; white-space: pre-wrap; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; color: rgba(38, 38, 38, 0.75); font-family: Menlo, sans-serif; font-variant-caps: normal; font-variant-ligatures: normal; line-height: 1rem;">n</span><span style="font-size: 14px; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240); color: rgb(38, 38, 38); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-variant-caps: normal; font-variant-ligatures: normal;"> </span><span style="font-size: 14px; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240); color: rgb(38, 38, 38); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-variant-caps: normal; font-variant-ligatures: normal;">个非负整数表示每个宽度为</span><span style="font-size: 14px; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240); color: rgb(38, 38, 38); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-variant-caps: normal; font-variant-ligatures: normal;"> </span><span style="border: 1px solid rgba(0, 0, 0, 0.05); box-sizing: border-box; overflow-wrap: break-word; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset-width:  0px; --tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; font-size: 0.75rem; background-color: rgba(0, 10, 32, 0.03); border-top-left-radius: 5px; border-top-right-radius: 5px; border-bottom-right-radius: 5px; border-bottom-left-radius: 5px; white-space: pre-wrap; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; color: rgba(38, 38, 38, 0.75); font-family: Menlo, sans-serif; font-variant-caps: normal; font-variant-ligatures: normal; line-height: 1rem;">1</span><span style="font-size: 14px; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240); color: rgb(38, 38, 38); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-variant-caps: normal; font-variant-ligatures: normal;"> </span><span style="font-size: 14px; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240); color: rgb(38, 38, 38); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-variant-caps: normal; font-variant-ligatures: normal;">的柱子的高度图，计算按此排列的柱子，下雨之后能接多少雨水。</span></div><div style="text-align: start;"><span style="font-size: 13px;">思路：双指针，往中间走，计算每列的储水量。cur=min(l_max, r_max)，用它和当前列高度相比，如果cur&lt;当前列高度，当前列储水量为0；如果cur&gt;当前列高度，储水量=(cur-当前列高度)*宽度。</span></div><div style="text-align: start;"><span style="font-size: 16px; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(240, 240, 240); color: rgba(60, 60, 67, 0.6); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-variant-caps: normal; font-variant-ligatures: normal;">左右各设置一个指针用于左和右的存储最大高度。将较低高度的指针向中间移动</span></div><div style="text-align: start;"><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div>def trap(self, height):</div><div>        """</div><div>        :type height: List[int]</div><div>        :rtype: int</div><div>        """</div><div>        sum_size=0</div><div>        l, r = 1, len(height)-2 # 左右指针分别在第二个和倒数第二个</div><div>        ml, mr = height[0], height[len(height)-1] # 左侧最大高度和右侧最大高度</div><div>        while(l&lt;=r): </div><div>            if ml&lt;=mr: # 移动小的那个</div><div>                size=max(ml-height[l],0)</div><div>                ml=max(height[l],ml)</div><div>                l+=1</div><div>            else:</div><div>                size=max(mr-height[r],0)</div><div>                mr=max(height[r],mr)</div><div>                r-=1</div><div>            sum_size+=size</div><div>        return sum_size</div></div></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><font style="font-size: 13px;"><br/></font></div><div style="text-align: start;"><span style="font-size: 13px; font-weight: bold;">6.盛水最多的容器</span></div><div style="text-align: start;"><span style="font-size: 16px;"><img src="%E5%8F%8C%E6%8C%87%E9%92%88.resources/6F795063-6369-436E-B386-EFD5E5FE9CC8.png" height="658" width="1278"/><br/></span></div><div style="text-align: start;"><br/></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div>def maxArea(self, height):</div><div>    left = 0</div><div>    right = len(height) - 1</div><div>    ans = 0</div><div>    while left &lt; right:</div><div>        cur = min(height[right], height[left]) * (right - left)</div><div>        ans = max(cur, ans)</div><div>        if height[left] &lt; height[right]:  # 优先舍弃较小的边</div><div>            left = left + 1</div><div>        else:</div><div>            right = right - 1</div><div>    return ans</div></div><div><br/></div></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><span style="font-size: 13px; font-weight: bold;">7.旋转数组，轮转数组</span></div><div style="text-align: start;"><span style="font-size: 13px;">把前面k个数放后面</span></div><div style="text-align: start;"><span style="font-size: 13px;">输入: nums = [1,2,3,4,5,6,7], k = 3</span></div><div style="text-align: start;"><span style="font-size: 13px;">输出: [5,6,7,1,2,3,4]</span></div><div style="text-align: start;"><span style="font-size: 13px;">思路： 1. 三次旋转：先转整体，再转前n-k，再转最后k个      2.切片</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><span style="font-family: &quot;Courier New&quot;;"><font style="font-size: 13px;">def solve(self, n: int, m: int, a: List[int]) -&gt; List[int]:</font></span></div><div><span style="font-family: &quot;Courier New&quot;;"><font style="font-size: 13px;">    # 取余，因为每次长度为n的旋转数组相当于没有变化</font></span></div><div><span style="font-family: &quot;Courier New&quot;;"><font style="font-size: 13px;">    m = m % n</font></span></div><div><span style="font-family: &quot;Courier New&quot;;"><font style="font-size: 13px;">    # 第一次逆转全部数组元素</font></span></div><div><span style="font-family: &quot;Courier New&quot;;"><font style="font-size: 13px;">    a.reverse()</font></span></div><div><span style="font-family: &quot;Courier New&quot;;"><font style="font-size: 13px;">    b = a[:m]</font></span></div><div><span style="font-family: &quot;Courier New&quot;;"><font style="font-size: 13px;">    # 第二次只逆转开头m个</font></span></div><div><span style="font-family: &quot;Courier New&quot;;"><font style="font-size: 13px;">    b.reverse()</font></span></div><div><span style="font-family: &quot;Courier New&quot;;"><font style="font-size: 13px;">    c = a[m:]</font></span></div><div><span style="font-family: &quot;Courier New&quot;;"><font style="font-size: 13px;">    # 第三次只逆转结尾m个</font></span></div><div><span style="font-family: &quot;Courier New&quot;;"><font style="font-size: 13px;">    c.reverse()</font></span></div><div><span style="font-family: &quot;Courier New&quot;;"><font style="font-size: 13px;">    a[:m] = b</font></span></div><div><span style="font-family: &quot;Courier New&quot;;"><font style="font-size: 13px;">    a[m:] = c</font></span></div><div><span style="font-family: &quot;Courier New&quot;;"><font style="font-size: 13px;">    return a</font></span></div></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"/><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><font style="font-size: 13px;"><br/></font></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><span style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal;"><span style="font-size: 13px; color: black; font-variant-caps: normal; font-variant-ligatures: normal; font-weight: bold; line-height: 1.5;">八、颜色分类（经典荷兰国旗问题）</span></span></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><font style="font-size: 13px;"><span style="letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; white-space: normal; widows: 2; word-spacing: 0px; font-size: 13px; color: rgb(38, 38, 38); font-variant-caps: normal; font-variant-ligatures: normal;">给定一个包含红色、白色和蓝色、共 </span><span style="border: 1px solid rgba(0, 0, 0, 0.05); box-sizing: border-box; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset---tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; overflow-wrap: break-word; background-color: rgba(0, 10, 32, 0.03); border-top-left-radius: 5px; border-top-right-radius: 5px; border-bottom-right-radius: 5px; border-bottom-left-radius: 5px; white-space: pre-wrap; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; font-size: 13px; color: rgba(38, 38, 38, 0.75); font-variant-caps: normal; font-variant-ligatures: normal; line-height: 1rem;">n</span><span style="border-color: var(--chakra-colors-chakra-border-color); box-sizing: border-box; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset---tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; overflow-wrap: break-word; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; white-space: normal; widows: 2; word-spacing: 0px; font-size: 13px; color: rgb(38, 38, 38); font-style: italic; font-variant-caps: normal; font-variant-ligatures: normal;"> </span><span style="letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; white-space: normal; widows: 2; word-spacing: 0px; font-size: 13px; color: rgb(38, 38, 38); font-variant-caps: normal; font-variant-ligatures: normal;">个元素的数组</span><span style="letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; white-space: normal; widows: 2; word-spacing: 0px; font-size: 13px; color: rgb(38, 38, 38); font-variant-caps: normal; font-variant-ligatures: normal;"> </span><span style="border: 1px solid rgba(0, 0, 0, 0.05); box-sizing: border-box; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset---tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; overflow-wrap: break-word; background-color: rgba(0, 10, 32, 0.03); border-top-left-radius: 5px; border-top-right-radius: 5px; border-bottom-right-radius: 5px; border-bottom-left-radius: 5px; white-space: pre-wrap; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; font-size: 13px; color: rgba(38, 38, 38, 0.75); font-variant-caps: normal; font-variant-ligatures: normal; line-height: 1rem;">nums</span><span style="letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; white-space: normal; widows: 2; word-spacing: 0px; font-size: 13px; color: rgb(38, 38, 38); font-variant-caps: normal; font-variant-ligatures: normal;"> ，</span><a href="https://baike.baidu.com/item/%E5%8E%9F%E5%9C%B0%E7%AE%97%E6%B3%95" style="border-color: var(--chakra-colors-chakra-border-color); box-sizing: border-box; --tw-border-spacing-x:  0; --tw-border-spacing-y:  0; --tw-translate-x:  0; --tw-translate-y:  0; --tw-rotate:  0; --tw-skew-x:  0; --tw-skew-y:  0; --tw-scale-x:  1; --tw-scale-y:  1; --tw-scroll-snap-strictness:  proximity; --tw-ring-offset---tw-ring-offset-color:  #fff; --tw-ring-color:  #3b82f680; --tw-ring-offset-shadow:  0 0 #0000; --tw-ring-shadow:  0 0 #0000; --tw-shadow:  0 0 #0000; --tw-shadow-colored:  0 0 #0000; overflow-wrap: break-word; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; white-space: normal; widows: 2; word-spacing: 0px; cursor: pointer; outline: none; touch-action: manipulation; --tw-text-opacity:  1; font-size: 13px; color: rgb(0 122 255/var(--tw-text-opacity)); font-variant-caps: normal; font-variant-ligatures: normal; font-weight: bold;">原地</a><span style="letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; white-space: normal; widows: 2; word-spacing: 0px; font-size: 13px; color: rgb(38, 38, 38); font-variant-caps: normal; font-variant-ligatures: normal;">对它们进行排序，使得相同颜色的元素相邻，并按照红色、白色、蓝色顺序排列。</span></font></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><span style="font-size: 13px; color: rgb(38, 38, 38);">输入：nums = [2,0,2,1,1,0]</span></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><span style="font-size: 13px; color: rgb(38, 38, 38);">输出：[0,0,1,1,2,2]</span></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><span style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal;"><span style="font-size: 13px;">四个区域：</span></span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font face="Courier New" style="font-size: 13px;">    def sortColors(self, nums):</font></div><div><font face="Courier New" style="font-size: 13px;">        '''</font></div><div><font face="Courier New" style="font-size: 13px;">        四个区域：[0,left-1]是全部的0，[0,cur-1]是已经处理的元素，</font></div><div><font face="Courier New" style="font-size: 13px;">        那么[left,cur-1]是全部的1，[right+1, len(nums)-1]是全部的2</font></div><div><font face="Courier New" style="font-size: 13px;">        '''</font></div><div><font face="Courier New" style="font-size: 13px;">        left = 0    </font></div><div><font face="Courier New" style="font-size: 13px;">        right = len(nums)-1</font></div><div><font face="Courier New" style="font-size: 13px;">        cur = 0  # 当前位置</font></div><div><font face="Courier New" style="font-size: 13px;">        while cur&lt;=right:</font></div><div><font face="Courier New" style="font-size: 13px;">            if nums[cur]==0:</font></div><div><font face="Courier New" style="font-size: 13px;">                nums[cur], nums[left] = nums[left], nums[cur]</font></div><div><font face="Courier New" style="font-size: 13px;">                left = left + 1</font></div><div><font face="Courier New" style="font-size: 13px;">                cur = cur + 1</font></div><div><font face="Courier New" style="font-size: 13px;">            elif nums[cur]==1:</font></div><div><font face="Courier New" style="font-size: 13px;">                cur = cur + 1</font></div><div><font face="Courier New" style="font-size: 13px;">            elif nums[cur]==2:# 注意，此时换回来的不知道是什么东西，所以cur不变</font></div><div><font face="Courier New" style="font-size: 13px;">                nums[cur], nums[right] = nums[right], nums[cur]</font></div><div><font face="Courier New" style="font-size: 13px;">                right = right - 1</font></div><div><font face="Courier New" style="font-size: 13px;">        return nums</font></div></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><font style="font-size: 13px;"><br/></font></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><span style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal;"><span style="font-size: 13px; font-weight: bold;">九、至少有K个重复字符的最长子串</span></span></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><span style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal;"><span style="font-size: 13px;">给一个字符串 s 和一个整数 k ，请你找出 s 中的最长子串， 要求该子串中的每一字符出现次数都不少于 k 。返回这一子串的长度。</span></span></div><div style="text-align: start;"><span style="font-size: 13px;">输入：s = "aaabb", k = 3</span></div><div style="text-align: start;"><span style="font-size: 13px;">输出：3</span></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><span style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal;"><span style="font-size: 13px;">解释：最长子串为 "aaa" ，其中 'a' 重复了 3 次。</span></span></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><span style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; font-size: 13px;">思路：不满足“二段性”，不能用滑动窗口或者双指针。( </span><span style="font-size: 13px;">如果新位置的字符在原有区间出现过，那必然还是满足出现次数大于 k，这时候 t + 1 的长度满足要求如果新位置的字符在原有区间没出现过，</span></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><span style="word-spacing: normal; font-size: 13px; color: unset;">那新字符的出现次数只有一次，</span><span style="word-spacing: normal; font-size: 13px; color: unset;">这时候 t + 1 的长度不满足要求)。</span></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><span style="font-size: 13px;">用递归：</span></div><div style="white-space: pre; text-align: start;"><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font face="Courier New" style="font-size: 13px;">def longestSubstring(self, s, k):</font></div><div><font face="Courier New" style="font-size: 13px;">    if len(s) &lt; k:</font></div><div><font face="Courier New" style="font-size: 13px;">        return 0</font></div><div><font face="Courier New" style="font-size: 13px;">    l_set = list(set(s))</font></div><div><font face="Courier New" style="font-size: 13px;">    for i in l_set:</font></div><div><font face="Courier New" style="font-size: 13px;">        if s.count(i) &lt; k:  # s中某个字符出现次数小于k，</font></div><div><font face="Courier New" style="font-size: 13px;">            l_split = s.split(i)  # 按照这个字符进行分割，得到很多子串</font></div><div><font face="Courier New" style="font-size: 13px;">            max_len = 0  # 子串中的max_len就是总的s的max_len</font></div><div><font face="Courier New" style="font-size: 13px;">            for j in range(len(l_split)):</font></div><div><font face="Courier New" style="font-size: 13px;">                max_len = max(max_len, self.longestSubstring(l_split[j], k))</font></div><div><font face="Courier New" style="font-size: 13px;">            return max_len</font></div><div><font face="Courier New" style="font-size: 13px;">    return len(s)  # s中的所有字符都符合条件</font></div></div></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><font style="font-size: 13px;"><br/></font></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><span style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal;"><span style="font-size: 13px; font-weight: bold;">十、等差数列划分</span></span></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><span style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal;"><span style="font-size: 13px;">给定一个nums，判断包含多少长度大于3的等差数列。</span></span></div><div style="text-align: start;"><span style="font-size: 13px;">输入：nums = [1,2,3,4]</span></div><div style="text-align: start;"><span style="font-size: 13px;">输出：3</span></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><span style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal;"><span style="font-size: 13px;">解释：nums 中有三个子等差数组：[1, 2, 3]、[2, 3, 4] 和 [1,2,3,4] 自身。</span></span></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><font style="font-size: 13px;"><br/></font></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><span style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal;"><span style="font-size: 13px;">思路：1.先用left和right指针，从头开始找等差数列，然后计算这个等差数列中包含多少个长度大于3的子数列(梯形公式)。</span></span></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><span style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal;"><span style="font-size: 13px;">2.left=right（因为right可能是下一个等差数列头），再继续找。</span></span></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"/><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font face="Courier New" style="font-size: 13px;">def numberOfArithmeticSlices(self, nums):</font></div><div><font face="Courier New" style="font-size: 13px;">    res = 0</font></div><div><font face="Courier New" style="font-size: 13px;">    left = 0</font></div><div><font face="Courier New" style="font-size: 13px;">    right = 0</font></div><div><font face="Courier New" style="font-size: 13px;">    while left &lt; len(nums) - 2:</font></div><div><font face="Courier New" style="font-size: 13px;">        gap = nums[left + 1] - nums[left]</font></div><div><font face="Courier New" style="font-size: 13px;">        while right + 1 &lt; len(nums) and nums[right + 1] - nums[right] == gap:</font></div><div><font face="Courier New" style="font-size: 13px;">            right = right + 1</font></div><div><font face="Courier New" style="font-size: 13px;">        # 计算长为right-left + 1的等差数列，有多少个大于3的子序列</font></div><div><font face="Courier New" style="font-size: 13px;">        shangdi = 1 # 所有数只能组成一条序列</font></div><div><font face="Courier New" style="font-size: 13px;">        xiadi = right - left + 1 - 3 + 1 # 长度为3的序列有这么多个</font></div><div><font face="Courier New" style="font-size: 13px;">        gao = right - left + 1 - 3 + 1</font></div><div><font face="Courier New" style="font-size: 13px;">        res = res + (right - left) * (right - left - 1) / 2</font></div><div><font face="Courier New" style="font-size: 13px;">        left = right</font></div><div><font face="Courier New" style="font-size: 13px;">    return res</font></div></div></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"/><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><font style="font-size: 13px;"><br/></font></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><span style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal;"><span style="font-size: 13px; font-weight: bold;">十一、替换后的最长重复字符</span></span></div><div style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal; text-align: start;"><span style="letter-spacing: normal; orphans: 2; white-space: pre; widows: 2; word-spacing: normal;"><span style="font-size: 13px;">给你一个字符串 s 和一个整数 k 。你可以选择任一字符，更改为任何其他字符。该操作最多可执行 k 次。</span></span></div><div style="text-align: start;"><span style="font-size: 13px;">输入：s = "AABABBA", k = 1</span></div><div style="text-align: start;"><span style="font-size: 13px;">输出：4</span></div><div style="text-align: start;"><span style="font-size: 13px;">解释：</span><span style="font-size: 13px; color: unset;">将中间的一个'A'替换为'B',字符串变为 "AABBBBA"。</span><span style="orphans: 2; white-space: pre; widows: 2; word-spacing: normal; font-size: 13px; color: unset;">子串 "BBBB" 有最长重复字母, 答案为 4。</span></div><div style="text-align: start;"><span style="orphans: 2; white-space: pre; widows: 2; word-spacing: normal;"><span style="font-size: 13px; color: unset;">思路：滑动窗口。</span></span></div><div style="text-align: start;"><span style="orphans: 2; white-space: pre; widows: 2; word-spacing: normal;"><span style="font-size: 13px; color: unset;">right右移一格，然后找窗口内出现次数最多的字符，判断窗口长度-次数-k是否大于0，否就left左移，直到是为止。</span></span></div><div style="text-align: start;"/><div style="text-align: start;"><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font face="Courier New" style="font-size: 13px;">def characterReplacement(s, k):</font></div><div><font face="Courier New" style="font-size: 13px;">    hs = dict()</font></div><div><font face="Courier New" style="font-size: 13px;">    left = 0</font></div><div><font face="Courier New" style="font-size: 13px;">    right = 0</font></div><div><font face="Courier New" style="font-size: 13px;">    max_num = 0</font></div><div><font face="Courier New" style="font-size: 13px;"><br/></font></div><div><font face="Courier New" style="font-size: 13px;">    def check(left, right):</font></div><div><font face="Courier New" style="font-size: 13px;">        a = max(hs.values())</font></div><div><font face="Courier New" style="font-size: 13px;">        if right - left + 1 - a - k &gt; 0:  # 说明此时用k次修改无法将字符串全都改成一样的。</font></div><div><font face="Courier New" style="font-size: 13px;">            return False</font></div><div><font face="Courier New" style="font-size: 13px;">        return True</font></div><div><font face="Courier New" style="font-size: 13px;"><br/></font></div><div><font face="Courier New" style="font-size: 13px;">    while right &lt; len(s):</font></div><div><font face="Courier New" style="font-size: 13px;">        if s[right] in hs:</font></div><div><font face="Courier New" style="font-size: 13px;">            hs[s[right]] = hs[s[right]] + 1</font></div><div><font face="Courier New" style="font-size: 13px;">        else:</font></div><div><font face="Courier New" style="font-size: 13px;">            hs[s[right]] = 1</font></div><div><font face="Courier New" style="font-size: 13px;">        while left &lt; right and not check(left, right):</font></div><div><font face="Courier New" style="font-size: 13px;">            hs[s[left]] = hs[s[left]] - 1</font></div><div><font face="Courier New" style="font-size: 13px;">            left = left + 1</font></div><div><font face="Courier New" style="font-size: 13px;"><br/></font></div><div><font face="Courier New" style="font-size: 13px;">        max_num = max(max_num, right - left + 1)</font></div><div><font face="Courier New" style="font-size: 13px;">        right = right + 1</font></div><div><font face="Courier New" style="font-size: 13px;">    return max_num</font></div></div></div><div style="text-align: start;"><font style="font-size: 13px;"><br/></font></div><div style="text-align: start;"><span style="font-size: 13px; font-weight: bold;">12.调整数组顺序使奇数位于偶数前面</span></div><div style="text-align: start;"><span style="font-size: 13px;">思路：双指针，一个指向头，一个指向尾。一直交换奇数和偶数</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 13px;">    def exchange(self, nums):</font></div><div><font style="font-size: 13px;">        i = 0</font></div><div><font style="font-size: 13px;">        j = len(nums) - 1</font></div><div><font style="font-size: 13px;">        while i&lt;j:</font></div><div><font style="font-size: 13px;">            while nums[i]%2==1 and i&lt;j:</font></div><div><font style="font-size: 13px;">                i = i + 1</font></div><div><font style="font-size: 13px;">            while nums[j]%2==0 and i&lt;j:    </font></div><div><font style="font-size: 13px;">                j = j - 1</font></div><div><font style="font-size: 13px;">            nums[i], nums[j] = nums[j], nums[i]</font></div><div><font style="font-size: 13px;">        return nums</font></div></div><div style="text-align: start;"><span style="font-size: 13px;">奇偶相对位置不变</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 13px;">    def reOrderArray(self , array: List[int]) -&gt; List[int]:</font></div><div><font style="font-size: 13px;">        n = len(array)</font></div><div><font style="font-size: 13px;">        res = [0 for i in range(n)]</font></div><div><font style="font-size: 13px;"><br/></font></div><div><font style="font-size: 13px;">        #统计奇数个数</font></div><div><font style="font-size: 13px;">        odd = 0</font></div><div><font style="font-size: 13px;">        #遍历统计</font></div><div><font style="font-size: 13px;">        for a in array:</font></div><div><font style="font-size: 13px;">            if a % 2:</font></div><div><font style="font-size: 13px;">                odd += 1</font></div><div><font style="font-size: 13px;">        #x与y分别表示答案中奇偶数的坐标</font></div><div><font style="font-size: 13px;">        x = 0</font></div><div><font style="font-size: 13px;">        y = odd</font></div><div><font style="font-size: 13px;">        for i in range(n):</font></div><div><font style="font-size: 13px;">            #奇数在前</font></div><div><font style="font-size: 13px;">            if array[i] % 2:</font></div><div><font style="font-size: 13px;">                res[x] = array[i]</font></div><div><font style="font-size: 13px;">                x += 1</font></div><div><font style="font-size: 13px;">            #偶数在后</font></div><div><font style="font-size: 13px;">            else:</font></div><div><font style="font-size: 13px;">                res[y] = array[i]</font></div><div><font style="font-size: 13px;">                y += 1</font></div><div><font style="font-size: 13px;">        return res</font></div></div><div style="text-align: start;"><font style="font-size: 13px;"><br/></font></div><div style="text-align: start;"><span style="font-size: 13px;">13.找到k个最接近的元素</span></div><div style="text-align: start;"><span style="font-size: 13px;">给定排序数组，找到最接近x的k个数。</span><span style="font-size: 13px; color: unset;">整数 a 比整数 b 更接近 x 需要满足：</span></div><div style="text-align: start;"><span style="font-size: 13px;">|a - x| &lt; |b - x| 或者</span></div><div style="text-align: start;"><span style="font-size: 13px;">|a - x| == |b - x| 且 a &lt; b</span></div><div style="text-align: start;"><span style="font-size: 13px;">思路：二分找到x的左右边界。 开始用双指针扩展</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div>from bisect import bisect_left</div><div>def findClosestElements(arr, k, x):</div><div>    right = bisect_left(arr, x)</div><div>    left = right - 1</div><div>    while k != 0:</div><div>        if left &lt; 0:  # 左边到头了，只能从右边加</div><div>            right += 1</div><div>        elif right == len(arr):  # 右边到头了，只能从左边加</div><div>            left -= 1</div><div>        else:</div><div>            if x - arr[left] &lt;= arr[right] - x:</div><div>                left -= 1</div><div>            else:</div><div>                right += 1</div><div>        k -= 1</div><div>    return arr[left + 1:right]</div></div><div style="text-align: start;"><font style="font-size: 13px;"><br/></font></div><div style="text-align: start;"><span style="font-size: 13px; font-weight: bold;">15.重构字符串</span></div><div style="text-align: start;"><span style="font-size: 13px;">给定一个字符串 s ，检查是否能重新排布其中的字母，使得两相邻的字符不同。返回 s 的任意可能的重新排列。若不可行，返回空字符串 "" 。</span></div><div style="text-align: start;"><span style="font-size: 13px;">输入: s = "aab"</span></div><div style="text-align: start;"><span style="font-size: 13px;">输出: "aba"</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 13px; font-family: &quot;Courier New&quot;;">from collections import Counter</font></div><div><font face="Courier New" style="font-size: 13px;">def reorganizeString(s):</font></div><div><font face="Courier New" style="font-size: 13px;">    cnt = Counter(s) # </font> Counter 中的键为待计数的元素，而 value 值为对应元素出现的次数 count，</div><div><font face="Courier New" style="font-size: 13px;">    idx = 0   # 控制是哪个桶</font></div><div><font style="font-size: 13px; font-family: &quot;Courier New&quot;;">    bucketNum = cnt.most_common(1) # 第一多的元素</font></div><div><font face="Courier New" style="font-size: 13px;">    bucketNum = bucketNum[0][1]  # 桶的数目等于字符串中最多的元素的数目</font></div><div><font face="Courier New" style="font-size: 13px;">    buckets = [[] for _ in range(bucketNum)]</font></div><div><font face="Courier New" style="font-size: 13px;">    for c, num in cnt.most_common():  # 优先填充数目最多的元素。cnt.most_common()记录了每个元素的是啥，出现次数</font></div><div><font face="Courier New" style="font-size: 13px;">        for _ in range(num):</font></div><div><font face="Courier New" style="font-size: 13px;">            buckets[idx].append(c) </font></div><div><font face="Courier New" style="font-size: 13px;">            idx = (idx + 1) % bucketNum  # 循环在不同桶中进行填充</font></div><div><font style="font-size: 13px; font-family: &quot;Courier New&quot;;">    return "".join(["".join(bucket) for bucket in buckets]) if list(map(len, buckets)).count(1) &lt;= 1 else "" # 元素的个数为1的桶少于2个</font></div><div><font face="Courier New" style="font-size: 13px;"><br/></font></div><div><font face="Courier New" style="font-size: 13px;">print(reorganizeString('aba'))</font></div></div><div><font style="font-size: 13px;"><br/></font></div><div><br/></div><div><br/></div></body></html>