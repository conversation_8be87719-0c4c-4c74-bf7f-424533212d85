<svg width="800" height="400" xmlns="http://www.w3.org/2000/svg">
  <!-- 用户输入 -->
  <rect x="20" y="50" width="100" height="40" fill="#90EE90" stroke="#000" rx="5"/>
  <text x="70" y="75" text-anchor="middle" font-size="12">用户查询</text>
  
  <!-- 意图理解 -->
  <rect x="160" y="50" width="100" height="40" fill="#FFB6C1" stroke="#000" rx="5"/>
  <text x="210" y="75" text-anchor="middle" font-size="12">大模型意图理解</text>
  
  <!-- 向量化处理 -->
  <rect x="300" y="20" width="120" height="40" fill="#87CEEB" stroke="#000" rx="5"/>
  <text x="360" y="45" text-anchor="middle" font-size="11">CoSENT文本编码</text>
  
  <rect x="300" y="80" width="120" height="40" fill="#87CEEB" stroke="#000" rx="5"/>
  <text x="360" y="105" text-anchor="middle" font-size="11">CLIP图像编码</text>
  
  <!-- 向量库 -->
  <rect x="460" y="20" width="100" height="40" fill="#DDA0DD" stroke="#000" rx="5"/>
  <text x="510" y="40" text-anchor="middle" font-size="10">文本向量库</text>
  <text x="510" y="55" text-anchor="middle" font-size="10">Faiss</text>
  
  <rect x="460" y="80" width="100" height="40" fill="#DDA0DD" stroke="#000" rx="5"/>
  <text x="510" y="100" text-anchor="middle" font-size="10">图像向量库</text>
  <text x="510" y="115" text-anchor="middle" font-size="10">Faiss</text>
  
  <!-- 相似度计算 -->
  <rect x="600" y="50" width="100" height="40" fill="#F0E68C" stroke="#000" rx="5"/>
  <text x="650" y="70" text-anchor="middle" font-size="10">相似度计算</text>
  <text x="650" y="85" text-anchor="middle" font-size="10">阈值过滤</text>
  
  <!-- 结果返回 -->
  <rect x="600" y="150" width="100" height="40" fill="#98FB98" stroke="#000" rx="5"/>
  <text x="650" y="175" text-anchor="middle" font-size="12">检索结果</text>
  
  <!-- GPU并行处理 -->
  <rect x="300" y="150" width="120" height="60" fill="#FFE4B5" stroke="#000" stroke-dasharray="5,5" rx="5"/>
  <text x="360" y="170" text-anchor="middle" font-size="10">多卡GPU并行</text>
  <text x="360" y="185" text-anchor="middle" font-size="10">AutoFaiss优化</text>
  <text x="360" y="200" text-anchor="middle" font-size="10">多算法集成</text>
  
  <!-- 箭头连接 -->
  <path d="M120 70 L160 70" stroke="#000" marker-end="url(#arrowhead)"/>
  <path d="M260 70 L300 40" stroke="#000" marker-end="url(#arrowhead)"/>
  <path d="M260 70 L300 100" stroke="#000" marker-end="url(#arrowhead)"/>
  <path d="M420 40 L460 40" stroke="#000" marker-end="url(#arrowhead)"/>
  <path d="M420 100 L460 100" stroke="#000" marker-end="url(#arrowhead)"/>
  <path d="M560 40 L600 60" stroke="#000" marker-end="url(#arrowhead)"/>
  <path d="M560 100 L600 80" stroke="#000" marker-end="url(#arrowhead)"/>
  <path d="M650 90 L650 150" stroke="#000" marker-end="url(#arrowhead)"/>
  
  <!-- 性能指标标注 -->
  <text x="510" y="140" text-anchor="middle" font-size="9" fill="#FF0000">50ms响应</text>
  <text x="360" y="230" text-anchor="middle" font-size="9" fill="#FF0000">性能提升300%</text>
  <text x="650" y="210" text-anchor="middle" font-size="9" fill="#FF0000">准确率95%+</text>
  
  <!-- 箭头定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#000"/>
    </marker>
  </defs>
</svg>