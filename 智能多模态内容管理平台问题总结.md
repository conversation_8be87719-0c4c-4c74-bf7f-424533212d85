---
tags:
  - project
area: "[[工作]]"
createTime: 2025-04-12T20:46:17
doneTime:
---
- **SpringAI** : 提供向量模型处理框架，支持文件的解析、拆分和向量化操作。
- **TikaDocumentReader** : 用于解析上传的文件，支持多种文件格式（如 MD、TXT、SQL 等）。
- **TokenTextSplitter** : 用于将解析后的文本内容拆分为更小的片段，便于后续处理和存储。
- **PostgreSQL向量库** : 用于存储处理后的文本向量数据，支持高效的相似性搜索和检索。

RAG：实现了除普通文档知识解析外，增加了 Git 代码库的拉取和解析，并提供操作接口。为工程师做项目开发时，`需求分析`、`研发设计`、`辅助编码`、`代码评审`、`风险评估`、`上线检测` 等，做工程交付提效。

MCP：用不了多久，各大互联网企业都将大量的推进落地，自有 MCP 服务的实现，用于增强企业 AI 应用的提效能力。因为 MCP 的加入，可以让你；一条命令`帮研发`，调用应用系统日志、排查系统CPU负载、自主选择是否调度数据库信息。也可以一条命令`帮运营`，搞定复杂的SQL执行、导出报表、分析数据、完成促活营销券的自动化配置上架。这就是 MCP的魅力
#### **1. 多模态数据融合引擎中，你提到使用 Spring AI 统一编码不同模态数据。请详细说明 Spring AI 如何实现文本、代码、图片的统一编码？在跨模态检索时，如何保证不同模态向量的语义对齐？**
- **参考答案**：  
  Spring AI 通过模块化的 Encoder 接口抽象不同模态的编码过程。文本和代码使用 TikaDocumentReader 解析后，通过预训练语言模型（如 BERT）生成向量；图片则通过 CLIP 模型的视觉编码器生成向量。统一编码的核心在于将不同模态的向量映射到同一语义空间，例如 CLIP 的联合训练机制天然支持图文对齐。对于代码，可能需要额外设计领域适配层（如 CodeBERT）来对齐文本语义。跨模态检索时，需通过余弦相似度或双塔模型进行向量匹配，并通过人工标注数据微调对齐效果。

编码上传到知识库：
文本：nomic-embed-text
图片：CLIP
代码：CodeBERT

对文本查询时（输出老人周报告分析），同时检索文本向量库和图片向量库，通过
- 加权融合（文本 0.6/图像 0.4）排序结果。混合检索框架（如Milvus）减少计算开销
- Top 3 图像➕Top 3 文本，for 循环添加

---

#### **2. 在高性能多模态检索中，你采用 Reactor 响应式编程拆分任务到独立线程池。请解释 Reactor 的线程调度模型，以及如何避免不同线程池间的资源竞争？**
- **参考答案**：  
  Reactor 基于事件循环（EventLoop）和非阻塞 IO，通过 Schedulers（如 elastic、parallel）分配任务到指定线程池。例如，文本解析使用 Schedulers.BoundedElastic () 弹性线程池，图片向量化使用 Schedulers.NewParallel ()。资源竞争需通过以下手段解决：  
  1. **隔离线程池**：不同任务分配独立线程池，避免 CPU 密集型与 IO 密集型任务互相干扰；  
  2. **背压控制**：通过 Flux.OnBackpressureBuffer () 防止生产者过载；  
  3. **锁粒度优化**：对共享资源（如 Redis 连接）使用细粒度锁或 CAS 操作。

线程池隔离：为不同任务分配独立线程池，例如：
Text-pool：小线程池（如 5 个线程）处理轻量级文本解析。
Code-pool：中型线程池（如 10 个线程）处理 JGit 拉取（可能涉及 IO 密集型操作）。
Image-pool：大线程池（如 20 个线程）处理 CLIP 模型的 GPU 计算（计算密集型）。

1、编程模型：
- Spring MVC： 采用同步的、阻塞的编程模型。每个请求都会在一个单独的线程中处理，线程会一直阻塞直到请求完成。
- Spring WebFlux： 采用异步的、非阻塞的编程模型。它基于Reactive Streams标准，使用反应式编程的理念，可以更有效地处理大量并发请求，减少线程资源的浪费。
2、并发处理：
- Spring MVC： 使用Servlet API中的阻塞IO来处理请求，每个请求需要一个独立的线程，如果线程池中的线程用尽，新的请求就会被阻塞。
- Spring WebFlux： 使用非阻塞IO，通过少量的线程处理大量的并发请求。这可以提高系统的吞吐量，因为不需要为每个请求分配一个独立的线程。

Project Reactor 是一个用于构建响应式应用程序的库，Flux 是 Reactor 中的一个核心组件，用于表示一个异步序列，可以发出 0 到 N 个元素，并且可以是有限的或无限的流。

---

#### **3. 你提到通过 Caffeine+Redis 构建多级缓存，接口响应耗时降低 400%。请说明缓存层级的设计策略，以及如何解决缓存穿透与数据一致性问题？**
- **参考答案**：  
  **设计策略**：  
  - 一级缓存（Caffeine）存储高频热点数据，TTL 短（如 10 s），基于 LFU 淘汰策略；  
  - 二级缓存（Redis）存储次热点数据，TTL 较长（如 5 分钟），支持分布式共享。  
  **穿透解决**：  
  - 布隆过滤器拦截无效请求；  
  - 空值缓存（缓存 NULL，TTL 1 分钟）。  
  **一致性解决**：  
  - 双删策略（先删 DB 再删缓存，延迟双删兜底）；  
  - 订阅数据库 Binlog，通过 Canal 同步缓存失效。

---

#### **4. 在 RPC 框架中，你基于 Vert. X RecordParser 解决半包粘包问题。请说明其底层实现原理，并对比 Netty 的 ByteToMessageDecoder 的异同。**
- **参考答案**：  
  **Vert. X RecordParser**：基于固定长度分隔符或长度字段（如 LengthFieldBasedFrameDecoder），通过累积缓冲区动态解析完整帧。Vert. X 使用异步回调模型，通过 `fetch` 方法逐次读取数据。  
  **Netty ByteToMessageDecoder**：基于责任链模式，在 `decode` 方法中循环处理 ByteBuf，支持更灵活的协议扩展。  
  **核心差异**：Vert. X 的 RecordParser 更轻量，适合简单协议；Netty 的 Decoder 支持更复杂的拆包逻辑（如动态长度），但需要手动管理缓冲区释放。

---

#### **5. 在 RAG 项目中，你使用 MD 5 分片上传实现秒传与断点续传。请详细说明分片上传的流程，以及如何保证分片上传的原子性和幂等性？**
- **参考答案**：  
  **流程**：  
  1. 客户端计算文件 MD 5 并查询服务端是否存在；  
  2. 若不存在，按固定大小（如 5 MB）分片上传，每片附带 MD 5 和序号；  
  3. 服务端校验分片 MD 5，存储至临时目录；  
  4. 所有分片上传完成后合并文件。  
  **原子性**：通过数据库事务记录分片状态，合并失败时回滚；  
  **幂等性**：客户端上传分片时携带唯一 UUID，服务端通过 Redis SETNX 去重。

---

#### **6. 你提到通过 TransactionTemplate 编程式事务确保存储额度占用。请说明编程式事务与声明式事务的适用场景，以及在分布式事务中的局限性。**
- **参考答案**：  
  **编程式事务**：通过 TransactionManager 手动控制事务边界，适合细粒度控制（如条件回滚）。  
  **声明式事务**：基于 AOP 自动管理，适合简单 ACID 场景。  
  **分布式局限性**：两者均无法直接解决跨服务事务，需引入 Seata 的 AT 模式或 TCC 补偿事务，结合本地消息表最终一致性。

---

#### **7. 在 RPC 框架中，你通过 Etcd 实现服务注册与发现。请说明 Etcd 的 Watch 机制如何实现服务实时上下线感知，并对比 ZooKeeper 的临时节点方案。**
- **参考答案**：  
  **Etcd Watch**：客户端监听特定前缀的 Key，当服务提供者注册/注销时，Etcd 推送事件通知消费者。通过 Lease 机制（TTL）自动删除失效节点。  
  **对比 ZooKeeper**：  
  - Etcd 使用 Raft 协议，ZK 使用 ZAB 协议，Etcd 读写性能更高；  
  - ZK 的临时节点在会话结束时自动删除，Etcd 需依赖 Lease 续期；  
  - Etcd 支持范围查询和事务操作，更适合大规模服务发现。

---

#### **8. 你提到使用 CLIP 模型生成图片语义向量。请说明 CLIP 的跨模态对齐训练过程，以及如何针对业务数据优化向量质量？**
- **参考答案**：  
  **训练过程**：CLIP 通过对比学习，将图片-文本对映射到同一空间，最大化匹配对的余弦相似度，最小化非匹配对的相似度。  
  **优化策略**：  
  1. 领域适配：在业务数据上微调 CLIP 的 Proj 层；  
  2. 数据增强：添加人工标注的负面样本（如相似但无关的图文对）；  
  3. 混合检索：结合向量相似度与人工元数据（如标签权重）加权排序。

---

#### **9. 在 WebSocket 多人协作编辑中，你设计“编辑锁”避免冲突。请说明其实现细节，并分析在分布式场景下可能存在的脑裂问题及解决方案。**
- **参考答案**：  
  **实现细节**：  
  - 用户进入编辑时，向 Redis 申请分布式锁（RedLock 算法），锁 Key 为资源 ID，Value 为 SessionID+时间戳；  
  - 通过 WatchDog 线程续期锁，超时自动释放。  
  **脑裂问题**：网络分区导致多个客户端持有锁。  
  **解决方案**：  
  1. 使用 Etcd 或 ZooKeeper 的强一致性锁；  
  2. 客户端操作前校验锁持有者，服务端二次确认。

---

#### **10. 在 RPC 框架中，你通过 SPI 机制支持自定义序列化器。请说明自实现 SPI 的核心原理，并对比 JDK SPI 与 Dubbo SPI 的设计差异。**
- **参考答案**：  
  **自实现 SPI**：通过类路径扫描（如 ClassGraph 库）加载 META-INF/services 下的实现类，结合缓存提升性能。  
  **JDK SPI**：基于 ServiceLoader，按配置顺序加载全部实现类，无法按需加载。  
  **Dubbo SPI**：支持按名称激活扩展点，自适应扩展（@Adaptive 注解），依赖注入更灵活。

---

#### **11. 你提到通过 TopBufferHandlerWrapper 装饰器增强请求处理器。请说明装饰者模式在此处的优势，并给出一个违反开闭原则的反例。**
- **参考答案**：  
  **优势**：动态扩展处理器功能（如日志、限流），无需修改原有类，符合开闭原则。  
  **反例**：直接修改处理器源码添加新功能，导致所有调用方被迫升级，且无法灵活组合功能。

---

#### **12. 在 RAG 项目中，冷热数据分离采用 30 天未访问自动降频存储。请说明降频存储的具体实现，并分析其对系统吞吐量的影响。**
- **参考答案**：  
  **实现**：  
  1. 通过 Redis 记录文件最后访问时间；  
  2. 定时任务扫描超过 30 天的文件，迁移至 S 3 低频存储；  
  3. 元数据更新为冷数据标识。  
  **吞吐量影响**：迁移任务需在低峰期执行，避免占用业务 IOPS；冷数据访问时延增加，需结合预加载策略补偿。

---

#### **13. 你提到使用 Spring Session 实现分布式登录管理。请说明其如何集成 Redis，并对比 JWT 令牌方案的优缺点。**
- **参考答案**：  
  **Redis 集成**：Spring Session 将 Session 数据存储为 Hash 结构，Key 为 sessionId，Value 包含属性、创建时间、最大存活期。  
  **对比 JWT**：  
  - **优点**：服务端无状态，适合水平扩展；  
  - **缺点**：令牌无法主动失效，需结合黑名单或短 TTL；数据膨胀导致传输开销。

---

#### **14. 在 RPC 框架中，你通过 Kryo 序列化并解决线程安全问题。请说明 Kryo 为何非线程安全，以及 ThreadLocal 方案的潜在内存泄漏风险。**
- **参考答案**：  
  **非线程安全**：Kryo 实例内部维护序列化状态（如对象缓存），多线程并发修改会导致数据污染。  
  **内存泄漏**：ThreadLocal 未调用 remove () 时，线程池复用导致 Kryo 实例累积。解决方案：使用 try-finally 块在 finally 中调用 remove ()。

---

#### **15. 你采用 Reactor 模型优化端到端延迟。请分析 Reactor 在 CPU 密集型任务中的局限性，并提出改进方案。**
- **参考答案**：  
  **局限性**：Reactor 的 EventLoop 线程被阻塞时（如大量计算），会导致整体吞吐量下降。  
  **改进方案**：  
  1. 将 CPU 密集型任务提交至独立的 Schedulers.Parallel () 线程池；  
  2. 使用 Project Loom 的虚拟线程（协程）减少上下文切换开销。

---

#### **16. 在 AI 工作流引擎中，你抽象可编排节点。请说明如何通过 DAG（有向无环图）实现工作流调度，并解决循环依赖问题。**
- **参考答案**：  
  **DAG 调度**：  
  1. 解析节点依赖关系，拓扑排序生成执行顺序；  
  2. 并行执行无依赖节点，通过 CompletableFuture 或 Reactor 调度。  
  **循环依赖检测**：在 DAG 构建阶段，使用 Tarjan 算法检测强连通分量，拒绝非法工作流。

---

#### **17. 你提到通过限流与预校验防止超额存储。请说明具体限流算法选型，并对比漏桶与令牌桶的适用场景。**
- **参考答案**：  
  **选型**：  
  - 令牌桶（Guava RateLimiter）：允许突发流量，适合秒传等高并发场景；  
  - 漏桶：恒定速率，适合平滑流量（如图片上传）。  
  **实现**：结合 Redis+Lua 脚本实现分布式限流，Key 为用户 ID，计数器原子递增。

---

#### **18. 在 RPC 框架中，你实现重试与容错策略。请说明如何设计指数退避重试，并避免雪崩效应。**
- **参考答案**：  
  **指数退避**：初始间隔 1 s，最大间隔 30 s，重试次数 3 次，公式：interval = min (2^retryCount * base, max)。  
  **防雪崩**：  
  1. 熔断机制（如 Hystrix）：错误率超阈值时熔断，拒绝请求；  
  2. 随机化重试间隔，避免集群级同步重试。

---

#### **19. 你使用 TikaDocumentReader 解析文本和代码。请说明 Tika 如何处理嵌套文档（如 ZIP 中的 PDF），以及性能优化点。**
- **参考答案**：  
  **嵌套解析**：Tika 通过 Parser 接口递归解析复合文档（如 ZIP→PDF→Text）。  
  **优化点**：  
  1. 限制递归深度（setRecursionLimit）；  
  2. 禁用非必要解析器（如视频）；  
  3. 复用 Parser 实例（避免重复加载）。

---

#### **20. 在 RAG 项目中，日均处理 5 W+图文请求。请估算系统 QPS，并说明如何进行全链路压测设计。**
- **参考答案**：  
  **QPS 估算**：5 W 请求/86400 秒 ≈ 0.58 QPS（但实际可能存在峰值，如白天 10 小时，则峰值 QPS=5 W/(10*3600)≈1.38）。  
  **压测设计**：  
  1. 使用 JMeter 模拟多模态请求混合场景；  
  2. 监控链路上各组件（Redis、PG、线程池）的瓶颈；  
  3. 逐步加压至 200%峰值流量，观察熔断、降级、扩容策略有效性。

--- 

以上问题覆盖分布式系统、高并发、框架原理、算法设计等核心领域，能够全面考察候选人的技术深度与实战经验。