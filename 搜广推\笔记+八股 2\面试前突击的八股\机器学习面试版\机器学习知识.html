<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/><meta name="exporter-version" content="Evernote Mac 9.7.14 (474683)"/><meta name="author" content="安能行叹复坐愁"/><meta name="created" content="2022-04-20 10:34:47 +0000"/><meta name="source" content="desktop.mac"/><meta name="source-url" content="https://blog.csdn.net/chenweijiSun/article/details/106343969"/><meta name="updated" content="2023-08-20 15:20:02 +0000"/><meta name="application-data:yinxiang.superNote" content="{&quot;aiMode&quot;:false}"/><title>机器学习知识</title></head><body style="font-size: 16px;"><div><div><span style="font-size: 13px; color: unset;"><br/></span></div><div><span style="font-size: 13px; color: unset;">NDCG:</span></div><div><font style="font-size: 13px;"><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/84CAAA38-E63E-49B5-AA61-E9B2A810D89A.png" height="140" width="436"/><span style="font-size: 13px; font-weight: bold;">，</span><span style="font-size: 13px; color: unset; font-weight: bold;">分子是第i个物品的得分</span></font></div><div><font style="font-size: 13px;"><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/A6A9FBF8-C877-42A6-B5B8-4A8D1CED0EC2.png" height="110" width="338"/><span style="font-size: 13px; font-weight: bold;">，当前得分/最佳得分</span></font></div><div><font style="font-size: 13px;"><span style="font-size: 13px; color: unset; font-family: unset; font-weight: bold;">f(x)在x=0处展开的泰勒公式：</span><span style="font-size: 13px; color: unset; font-family: unset; font-weight: bold;"><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/8B51E942-C609-4B7D-9425-6DAC3FB0851E.png" height="74" width="642"/><br/></span></font></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px;">多头注意力机制：k组Wq、Wk、Wv，产生k组结果。</span></div><div><span style="font-size: 13px;">softmax函数：</span><span style="font-size: 13px;"><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/1216BD1B-602B-4C25-B989-A79DD0D5806D.png" height="120" width="286"/><br/></span></div><div><span style="font-size: 13px;"><br/></span></div><div><span style="font-size: 13px; font-weight: bold;">一、LR</span></div><div><span style="font-size: 13px; font-weight: bold;">1.LR是一个假设样本服从伯努利分布，利用极大似然法和梯度下降法进行求解的二分类模型。</span></div><div><span style="font-size: 13px; color: unset; font-family: unset; font-weight: bold;">2.为什么回归问题用均方误差，分类模型用交叉熵损失函数？</span></div><div><span style="font-size: 13px;">均方误差：最小化预测值和目标值的距离。</span></div><div><span style="font-size: 13px;">交叉熵：不需要最小化距离，只需要预测对类别即可。</span></div><div><span style="font-size: 13px; font-weight: bold;">3.为什么LR不用平方误差损失函数呢？</span></div><ul><li><div><span style="font-size: 13px;">平方损失函数的二阶导小于0，是非凸的，容易陷入局部最小值。</span></div></li><li><div><span style="font-size: 13px;">使用平方误差损失函数，得到的梯度和sigmoid的导数有关，特别是当输出接近0,1时，梯度趋于0，收敛慢。</span></div></li><li><div><span style="font-size: 13px;">loss增大，MSE损失函数，参数的调整幅度先变大后变小，loss过大，会自暴自弃不肯学习。</span></div></li></ul><div><span style="font-size: 13px;">      对于交叉熵损失函数来说，</span><span style="font-size: 13px;">随着loss增大，</span><span style="font-size: 13px;">参数的调整幅度也会增大。</span></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px;">LR使用平方误差损失，梯度下降推导：</span></div><div><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/201A7DEE-F0E5-4C31-9179-6E56600F57E4.png" height="72" width="840"/><span style="font-size: 13px;">，其中y是真实值，y’是预测值</span></div><div><span style="font-size: 13px;">y1'=</span><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/03169D84-F06B-4D7D-8724-9A4AB42426D0.png" height="26" width="148"/><br/></div><div><span style="font-size: 13px;">梯度下降：</span></div><div><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/3BDC7E80-C7A3-4587-9131-610D54557A73.png" height="214" width="682"/><span style="font-size: 13px;">（中间这一步变换，sigmoid求导后变为</span><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/32415C97-B970-4D58-BB59-B7237E66239E.png" height="52" width="174"/><span style="font-size: 13px;">）</span></div><div><span style="font-size: 13px;">我们想要探究，</span><span style="font-size: 13px; font-weight: bold;">参数w11调整幅度和</span><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/E037B69A-70A7-4802-9D5D-6F17EFBCCF55.png" height="56" width="132"/><span style="font-size: 13px; font-weight: bold;">的关系</span><span style="font-size: 13px;">。 记</span><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/1BD4575E-4672-478B-B081-31D06D30CD67.png" height="52" width="230"/><br/></div><div><span style="font-size: 13px;">真实的y只能取0和1，所以上述函数，</span></div><div><font style="font-size: 13px;"><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/2477F8D1-E9EC-408C-B1ED-76F32F90DA40.png" height="246" width="1054"/><br/></font></div><div><span style="font-size: 13px;">函数图：</span></div><div><font style="font-size: 13px;"><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/C6FFC445-D815-469B-BD6C-3EB16688DDF5.png" height="872" width="1146"/><br/></font></div><div><span style="font-size: 13px;">所以，随着loss增大，参数调整幅度先变大后变小，loss很大时，模型开始自暴自弃，不肯学习。</span></div><div><span style="font-size: 13px; font-weight: bold;">交叉熵梯度下降推导：</span></div><div><font style="font-size: 13px;"><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/1B9EABC9-1FA0-471D-B9C2-9DC7661A36AA.png" height="272" width="522"/><br/></font></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px; font-weight: bold;">4.LR的输入特征要做什么处理</span></div><div><span style="font-size: 13px;">对特征进行离散化处理，例如分桶。</span></div><div><span style="font-size: 13px;">1.运算速度更快。 </span></div><div><span style="font-size: 13px;">2.对异常值有鲁棒性。比如，age，把所有大于100的看作一个桶，即使年龄=200，也不会对模型有干扰。</span></div><div><span style="font-size: 13px;">但是LR无法拟合高阶特征，无法做特征交叉。</span></div><div><span style="font-size: 13px; font-weight: bold;">5.如何求解极大似然函数？</span></div><div><span style="font-size: 13px;">梯度下降法</span></div><div><span style="font-size: 13px;">1）批梯度下降法(BGD)：在每次更新时用所有样本</span></div><div><span style="font-size: 13px;">优点：目标函数是凸函数时，一定能收敛到全局最小值，非凸函数则会收敛到局部最小值。</span></div><div><span style="font-size: 13px;">     使用固定的学习率，不用担心学习率衰退现象。</span></div><div><span style="font-size: 13px;">缺点：一次学习的速度慢，内存开销大，容易陷入局部最小值。</span></div><div><span style="font-size: 13px;">2）随机梯度下降法(SGD)：在每次更新时用1个样本。</span></div><div><span style="font-size: 13px;">优点：一次学习的速度快，内存开销小。频繁的更新使参数具有高方差，有助于发现更好的局部最小值。</span></div><div><span style="font-size: 13px;">缺点：走入山谷，会发生震荡，收敛速度变慢；走入平原，无法感知梯度的微小变化，停滞。</span></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px;">3）小批量梯度下降法（MBDG)：在每次更新使用部分样本。batch选择合理的话</span></div><div><span style="font-size: 13px;">优点：收敛速度快，震荡也没有随机梯度下降法那么大。</span></div><div><span style="font-size: 13px;">如何选择b个样本：对所有数据进行随机排序，按顺序选择</span></div><div><font style="font-size: 13px;"><br clear="none"/></font></div><div><span style="font-size: 13px;">其他优化器：</span></div><div><span style="font-size: 13px; font-weight: bold;">AdaGrad和RMSProp为不同的参数产生了自适应的学习率。</span></div><div><span style="font-size: 13px; font-weight: bold;">改进一：AdaGrad：历史梯度和当前梯度的平方和作为学习率的分母。 缺点：随着分母越来越大，梯度更新越来越慢。</span></div><div><font style="font-size: 13px;"><span style="font-size: 13px; color: unset; font-family: unset; font-weight: bold;">改进二：RMSProp：使用指数衰减去计算 历史梯度和当前梯度的平方和，会重点考虑距离它较近的梯度</span><span style="font-size: 13px; color: unset; font-family: unset; font-weight: bold;">。</span></font></div><div><font style="font-size: 13px;"><span style="font-size: 13px; font-weight: bold;">改进三：Momentum：刻画了惯性。若当前梯度方向与累积历史梯度方向一致，则当前梯度会被加强，这一步下降幅度变大；若当前梯度与累积历史梯度方向相反，这一步下降幅度变小。好处：减弱了震荡，</span><span style="font-size: 13px; font-weight: bold;">收敛速度更快，</span><span style="font-size: 13px; font-weight: bold;">梯度可以平滑地过度。</span></font></div><div><span style="font-size: 13px; font-weight: bold;">改进四：Adam：使用指数衰减计算一阶矩和二阶矩。既为不同的参数产生了自适应的学习率，又刻画了动量。</span></div><div><font style="font-size: 13px;"><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/3BA96359-7C05-48EA-908E-C263060A62C5.png" height="650" width="508"/><br/></font></div><div><font style="font-size: 13px;"><br/></font></div><div><font style="font-size: 13px;"><span style="font-size: 13px; font-weight: bold;">牛顿法：</span></font></div><div><span style="font-size: 13px;">用牛顿法求解f(x)=0：</span></div><div><span style="font-size: 13px;">1.将f(x)在x0处泰勒展开：</span><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/3794AF62-0399-4646-BCCA-6328046B548D.png" height="58" width="522"/><span style="font-size: 13px;">，</span></div><div><span style="font-size: 13px;">  所以求解f(x)=0相当于求解</span><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/6A3DB4FD-56EC-4226-BD66-6957156752B3.png" height="60" width="466"/><span style="font-size: 13px;">，</span></div><div><span style="font-size: 13px;">  解得：</span><span style="font-size: 13px; color: unset;"><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/75AE6F7D-A351-45C2-9463-B34FC7FB38DB.png" height="42" width="478"/><br/></span></div><div><span style="font-size: 13px; color: unset;">得到的x1是比x0更加接近正确结果的解。同理可得x2</span><span style="font-size: 13px;">…</span><span style="font-size: 13px; color: unset;">xn，直到达到要求为止。</span></div><div><font style="font-size: 13px;"><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/6ECEA437-85FB-49A8-A322-62E2FDE28D3E.png" height="56" width="452"/><br/></font></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div># 求根号2，f(x)=x^2-C，让f(x)=0即可</div><div>def mySqrt(x):</div><div>    C = float(x)  # 求C的平方根</div><div>    x0 = float(x)  # 假设第一个解是x0，在x0处开始，进行牛顿迭代法</div><div>    while True:</div><div>        xi = 0.5 * (x0 + C / x0)  # xi就是xn+1，x0就是xn</div><div>        if abs(x0 - xi) &lt; 1e-7:</div><div>            break</div><div>        x0 = xi </div><div>    return x0</div><div>print(mySqrt(3))</div></div><div><span style="font-size: 13px;">为什么牛顿法是二阶的？</span></div><div><span style="font-size: 13px;">因为求损失函数最小，minf(x)，是要求f(x)的极值点，让它导数f’(x)=0。</span></div><div><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/09259D16-C425-4C09-921C-E866AA874453.png" height="116" width="332"/><span style="font-size: 13px;">，所以牛顿法是二阶的。</span></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px; font-weight: bold;">6.激活函数</span></div><div><span style="font-size: 13px;">激活函数的作用：引入</span><span style="font-size: 13px; font-weight: bold;">非线性</span><span style="font-size: 13px;">，提高了模型的表达能力。</span></div><div><span style="font-size: 13px; font-weight: bold;">1.sigmoid激活函数</span></div><div><font style="font-size: 13px;"><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/87C04C60-6B80-4BDF-9C4D-10EF3607704E.png" height="68" width="224"/><br/></font></div><div><span style="font-size: 13px;">将输入变换到（0，1）之间，适合输出为概率的情况。</span><span style="font-size: 13px;">sigmoid作为激活函数的本质是<span style="font-size: 13px; font-weight: bold;">拟合伯努利分布，</span></span><span style="font-size: 13px;">伯努力分布在实际生活的意义就是表达正面事件发生的概率和反面事件发生的概率。所以，sigmoid常用于二元分类问题。</span></div><div><span style="font-size: 13px;"><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/4B0D7060-F32F-4FF1-9169-B7468FD3C12B.png" height="896" width="830"/><br/></span></div><div><span style="font-size: 13px;">缺点：</span></div><div><span style="font-size: 13px;">1.在0和1处会发生梯度消失。</span></div><div><span style="font-size: 13px;">2.</span><span style="font-size: 13px; font-weight: bold;">均值不为0</span><span style="font-size: 13px;">的情况。（因为sigmoid的输出始终是正数，这会导致在反向传播过程中，参数w要么都往正方向更新，要么都往负方向更新，导致收敛缓慢）</span></div><div><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/17DF4FA7-AB88-4FE4-AD84-4AC5A724D4B7.png" height="90" width="554"/><span style="font-size: 13px;">，其中f是sigmoid激活函数。对于所有的参数wi来说，它们的更新方向只取决于xi的正负，因为xi是上一层的输出，sigmoid函数的输出始终为正，所以导致所有参数要么都往正方向更新，要么都往负方向更新。</span></div><div><font style="font-size: 13px;"><br clear="none"/></font></div><div><span style="font-size: 13px; font-weight: bold;">2.tanh激活函数</span></div><div><font style="font-size: 13px;"><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/1B26E2A8-EF50-43FB-A5E4-B493931E86BA.png" height="49" width="163"/><br/></font></div><div><span style="font-size: 13px;">将输入变换到（-1，1）之间，解决了sigmoid激活函数的均值不为0的情况。梯度消失问题比sigmoid轻一些。 </span></div><div><font style="font-size: 13px;"><br/></font></div><div><font style="font-size: 13px;"><br clear="none"/></font></div><div><span style="font-size: 13px; font-weight: bold;">3.ReLU激活函数</span></div><div><span style="font-size: 13px;">优点：</span></div><div><span style="font-size: 13px;">1.计算速度快。</span></div><div><span style="font-size: 13px;">2.解决了梯度消失问题。</span></div><div><span style="font-size: 13px;">3.增大了网络的稀疏性。由于神经元死亡问题，会让很多网络节点输出为0，起到了dropout的作用，提高了泛化能力。</span></div><div><span style="font-size: 13px;">缺点：</span></div><div><span style="font-size: 13px;">1.均值不为0，导致收敛缓慢。</span></div><div><span style="font-size: 13px;">2.神经元死亡问题，破坏了数据的多样化。</span></div><div><font style="font-size: 13px;"><br clear="none"/></font></div><div><span style="font-size: 13px; font-weight: bold;">4.LeakyReLU</span></div><div><span style="font-size: 13px;">在神经元未激活时，它仍允许赋予一个很小的梯度，避免ReLU死掉的问题。</span></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px; font-weight: bold;">5.Dice激活函数</span></div><div><span style="font-size: 13px;">ReLU类函数的阶跃变化点在x=0处，面对不同的输入阶跃点不变。Dice激活函数的阶跃点随着输入数据的均值和方差动态变化，使其和输入数据有更好的适配性。</span></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px; font-weight: bold;">6.你对batchsize的大小怎么看？</span></div><div><span style="font-size: 13px;">梯度下降是先算出一个batch内的所有样本的loss的平均值，再求梯度。batchsize太小，相邻的batch算出的梯度差异过大，会发生震荡。</span></div><div><span style="font-size: 13px;">batchsize太大，容易收敛到局部最优点。</span></div><div><font style="font-size: 13px;"><br clear="none"/></font></div><div><font style="font-size: 13px;"><span style="font-size: 13px; font-weight: bold;">7.</span><span style="font-size: 13px; font-weight: bold;">给你一个有1000列和1百万行的训练数据集，这个数据集是基于分类问题的。经理要求你来降低该数据集的维度以减少模型计算时间，但你的机器内存有限。你会怎么做？</span></font></div><div><span style="font-size: 13px;">1.对数据集进行随机采样。采样100列，30万。</span></div><div><span style="font-size: 13px;">2.降低维度。计算变量之间的相关性，把相关性高的去除掉。对于数值变量，我们将使用相关性分析；对于类型变量，我们可以用卡方检验。</span></div><div><font style="font-size: 13px;"><br/></font></div><div><font style="font-size: 13px;"><span style="font-size: 13px; font-weight: bold;">8.</span><span style="font-size: 13px; font-weight: bold;">逻辑回归在训练的过程当中，如果有很多的特征高度相关或者说有一个特征重复了很多遍，会造成怎样的影响？</span></font></div><div><span style="font-size: 13px;">没有影响。特征重复100遍，相当于将原来的特征分成100份，每一份的权重都是原来的1/100。</span></div><div><span style="font-size: 13px;">y=wx+b，无非就是x的维度多一点，但是wx+b的和永远是label，所以只能是w变为原来的1/100。</span></div><div><span style="font-size: 13px; font-weight: bold;">为什么我们还是会在训练的过程当中将高度相关的特征去掉？</span></div><div><span style="font-size: 13px;">可解释性好、提高训练速度。</span></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px; font-weight: bold;">二、欠拟合和过拟合</span></div><div><span style="font-size: 13px; font-weight: bold;">1.过拟合：</span></div><div><span style="font-size: 13px;">在训练集上表现好，测试集上表现差。   </span><span style="font-size: 13px; color: unset; font-family: unset; font-weight: bold;">原因：</span><span style="font-size: 13px; color: unset; font-family: unset;">模型过于复杂，数据过少。</span></div><div><span style="font-size: 13px; color: unset; font-family: unset; font-weight: bold;">模型角度：</span></div><div><span style="font-size: 13px;">1）添加正则化。</span></div><div><span style="font-size: 13px;">2）添加dropout、early stopping。</span></div><div><span style="font-size: 13px;">具体做法：每个epoch结束后，在验证集上获取结果，如果发现上发现测试误差在上升，则停止训练。</span></div><div><span style="font-size: 13px;">Dropout:让每个神经元的输出以概率k变为原来的1/k倍，以概率1-k变为0。让一些神经元随机失活，从而让每一个神经元都有机会得到更高效的学习，会让网络更加健壮，减小过拟合。反向传播时，也不会用失活的神经元。</span></div><div><span style="font-size: 13px; font-weight: bold;">数据角度：</span></div><div><span style="font-size: 13px;">1）增加数据量</span></div><div><span style="font-size: 13px;">2）增加噪声：为输入数据添加高斯分布产生的数据</span></div><div><span style="font-size: 13px; font-weight: bold;">2.欠拟合：</span></div><div><span style="font-size: 13px;">在训练集和测试集上表现都差，训练不充分。  </span><span style="font-size: 13px; color: unset; font-family: unset; font-weight: bold;">解决方法：</span><span style="font-size: 13px; color: unset; font-family: unset;">加数据、加特征。</span></div><div><font style="font-size: 13px;"><br clear="none"/></font></div><div><span style="font-size: 13px; font-weight: bold;">三、梯度消失和梯度爆炸</span></div><div><span style="font-size: 13px;">原理：在反向传播过程中，网络层数很深，参数值和激活函数的导数值相乘如果大于1，层数很深的情况下，会使得梯度值越来越大，发生梯度爆炸；如果小于1，梯度值会越来越小，发生梯度消失。</span></div><div><span style="font-size: 13px;">解决梯度消失：</span></div><div><span style="font-size: 13px;">1. 用ReLU激活函数。</span></div><div><span style="font-size: 13px;">2. 使用残差结构。</span></div><div><span style="font-size: 13px;">解决梯度爆炸：</span></div><div><span style="font-size: 13px;">1. 加正则项。</span></div><div><span style="font-size: 13px;">2. 梯度裁剪（给梯度设一个阈值，将它限制在这个范围内）。</span></div><div><font style="font-size: 13px;"><br clear="none"/></font></div><div><span style="font-size: 13px; font-weight: bold;">四、L1和L2正则化</span></div><div><span style="font-size: 13px;">L1范数：损失函数+每个参数的绝对值之和。L2范数：损失函数+每个参数的平方和的开方值。</span><span style="font-size: 13px; color: unset;">其中1/C控制正则化的强度。</span></div><div><span style="font-size: 13px; color: unset;">线性回归+L1正则化=Lasso回归。       线性回归+L2正则化=岭回归</span></div><div><span style="font-size: 13px;">L1正则的本质是为模型增加了“模型参数服从零均值拉普拉斯分布”这一先验知识。相当于每次从权重中减去一个常数。L1正则化越强，就越多的参数被压缩为0，从而可以进行特征选择，产生稀疏模型。计算效率较低。</span></div><div><span style="font-size: 13px;">L2正则的本质是为模型增加了“模型服从零均值高斯分布”这一先验知识。相当于每次移除权重的x%。但不会变为0。权重变小，网络复杂度降低，可以防止过拟合，计算效率高。</span></div><div><span style="font-size: 13px; font-weight: bold;">为什么参数权值降低就能防止过拟合？</span></div><div><span style="font-size: 13px;">假设对一个线性回归方程，如果参数很大，那数据偏移一点点，就会对结果造成很大影响。参数小的话，抗扰动能力比较强。</span></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px; font-weight: bold;">L1正则化，损失函数不可导怎么办？</span></div><div><span style="font-size: 13px;">梯度下降是沿着当前点的负梯度方向进行参数更新,而坐标轴下降法是沿着坐标轴的方向,假设有m个特征个数,坐标轴下降法进参数更新的时候,先固定m-1个值,然后再求另外一个的局部最优解,从而避免损失函数不可导问题。</span></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px;">为什么要生成稀疏模型？</span></div><div><span style="font-size: 13px;">绝大多数特征是没有贡献的，只能增加复杂度而已。</span></div><div><span style="font-size: 13px; font-weight: bold;">五、经验风险最小化和结构风险最小化都是对于损失函数而言的。</span></div><div><span style="font-size: 13px;">经验风险最小化：只侧重数据集上的损失降到最低。</span></div><div><span style="font-size: 13px;">结构风险最小化：在经验风险最小化的基础上约束模型的复杂度，使损失降到最低的同时，模型不至于过于复杂，相当于在损失函数上增加了正则项，防止模型出现过拟合。</span></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px; font-weight: bold;">六、AUC面试</span></div><div><span style="font-size: 13px;">假阳性率：</span><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/72F45FF8-EE23-4C6C-A578-3B624FF783A9.png" height="58" width="176"/><span style="font-size: 13px;">，真阳性率：</span><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/5CC96BB7-B521-4935-AAD7-7F7126C38BB6.png" height="52" width="175"/><br/></div><div><span style="font-size: 13px;">假阳性率：在所有</span><span style="font-size: 13px; font-weight: bold;">真实的负样本</span><span style="font-size: 13px;">中，预测结果为正的比例。（越小越好）</span></div><div><span style="font-size: 13px;">真阳性率：在所有</span><span style="font-size: 13px; font-weight: bold;">真实的正样本</span><span style="font-size: 13px;">中，预测结果为正的比例。（越大越好）</span></div><div><span style="font-size: 13px;">ROC曲线的横坐标是假阳性率FPR，纵坐标是真阳性率TPR。</span></div><div><span style="font-size: 13px; font-weight: bold;">如何做ROC曲线图？ </span></div><div><span style="font-size: 13px;">选不同的阈值，预测值大于阈值的为1，小于阈值的为0，通过选取不同的阈值，画出ROC曲线图。</span></div><div><span style="font-size: 13px;">AUC：ROC曲线下的面积。常用定义：随机选取一个正样本，一个负样本，模型对正样本的预测值大于负样本的预测值的概率。</span></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px; font-weight: bold;">优点：</span></div><div><span style="font-size: 13px;">1.AUC衡量的是一种排序能力，因此特别适合排序类业务；</span></div><div><span style="font-size: 13px;">2.AUC对正负样本均衡并不敏感，在样本不均衡的情况下，也可以做出合理的评估</span></div><div><span style="font-size: 13px; font-weight: bold;">缺点：</span></div><div><span style="font-size: 13px; color: unset; font-family: unset;">AUC只关注正负样本之间的排序，无法衡量样本的好坏程度。</span></div><div><font style="font-size: 13px;"><br clear="none"/></font></div><div><span style="font-size: 13px; font-weight: bold;">AUC快速计算方法</span></div><div><font style="font-size: 13px;"><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/5B20D558-A43B-4B67-81A4-03DB56BF7AED.png" height="60" width="388"/><br/></font></div><div><span style="font-size: 13px;">先按得分对所有样本排序，再带公式计算即可。</span></div><div><span style="font-size: 13px;">公式中：分子第一项：正样本的序号和，M：正样本的个数，N：负样本的个数</span></div><div><span style="font-size: 13px; font-weight: bold;">能不能用Accuracy？为什么？</span></div><div><span style="font-size: 13px;">不能。当类别不均衡时，占比大的类别成为影响准确率的主要因素。比如负样本占99%时，我们把所有样本预测为负样本，准确率也能达到99%。</span></div><div><span style="font-size: 13px;">现在我们要投放一个奢侈品给用户，有钱人只占一小部分，整体Accuracy高不能代表奢侈品投放得准确。</span></div><div><span style="font-size: 13px;">精确率、召回率、AUC都能处理类别不均衡问题。</span></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px;">混淆矩阵：</span></div><div><font style="font-size: 13px;"><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/Image.png" height="252" width="669"/><br/></font></div><div><font style="font-size: 13px;"><br/></font></div><div><font style="font-size: 13px;"><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/97849B8D-48A8-4AAC-A953-807E77F8619B.png" height="120" width="390"/><br/></font></div><div><span style="font-size: 13px;">精确率：在所有预测为正的样本中，真实正样本的比例。</span></div><div><span style="font-size: 13px;">召回率：在所有真实的正样本中，预测为正的比例。</span></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px; font-weight: bold;">精确率和召回率的关系？</span></div><div><span style="font-size: 13px; color: unset; font-family: unset;">精确率和召回率都是越高越好，但是当精确率很高时，召回率就很低；当召回率很高时，精确率就很低。</span></div><div><span style="font-size: 13px; font-family: 微软雅黑; font-weight: bold;">如何平衡精确率和召回率？</span></div><div><span style="font-size: 13px; font-family: 微软雅黑;">阈值越大，精确率越高、召回率越低。F1分数同时考虑精确率和召回率，是它俩的调和平均数，F1最大时，精确率和召回率达到最佳平衡。</span></div><div><span style="font-size: 13px; font-weight: bold;">F1-score的知识点：</span></div><div><font style="font-size: 13px;"><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/941948F1-54F1-4AB4-A84E-4EE77B121D52.png" height="50" width="262"/><br/></font></div><div><span style="font-size: 13px;">F-score本质是精确率和召回率的调和平均数，所以只有在精确率和召回率都大的时候，F-score才会大。</span></div><div><span style="font-size: 13px;">调和平均数：</span></div><div><font style="font-size: 13px;"><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/1AE06574-F2AB-4232-BB34-AEDB218AAB40.png" height="316" width="751"/><br/></font></div><div><span style="font-size: 13px;">PR曲线：横轴是召回率，纵轴是精确率。</span></div><div><span style="font-size: 13px;">F1-Score和AUC的关系：F1-Score优化的是召回率和精确率，AUC优化的是召回率和（1-假阳性率）。所以F1分数希望训练一个不放过任何可能的模型（疫情模型），AUC希望训练一个不误报的模型。</span></div><div><font style="font-size: 13px;"><br/></font></div></div><div><span style="font-size: 13px;">Focal Loss:</span></div><div><span style="font-size: 13px; color: unset; font-family: unset;">为了解决正负样本不均衡，交叉熵损失函数的改进：</span></div><div><span style="font-size: 13px;">正常的交叉熵损失函数：</span><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/847FDC18-C071-40EA-AA01-54C6332FF545.png" height="66" width="473"/><br/></div><div><span style="font-size: 13px;">可见普通的交叉熵对于</span><span style="font-size: 13px; font-weight: bold;">正样本</span><span style="font-size: 13px;">而言，预测值越大损失越小。对于负样本而言，预测值越小则损失越小。</span></div><div><font style="font-size: 13px;"><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/5AE13757-67F1-4B4D-BA4C-B3EADE0E0A07.png" height="69" width="301"/><br/></font></div><div><span style="font-size: 13px;">Focal Loss，在交叉熵的基础上，加了gamma，减少了“简单”样本的损失，增加了“困难”样本的损失。（如果正负样本不均值，负样本的数量远大于正样本，那么负样本就是“简单”的，正样本就是“困难”的）</span></div><div><span style="font-size: 13px;">例如gamma为2，对于正类样本而言，预测结果为0.95肯定是简单样本，所以（1-0.95）的gamma次方就会很小，这时损失函数值就变得更小。对于正类样本而言，预测结果为0.1肯定是困难样本，所以（1-0.1）的gamma次方就会很大，这时损失函数值就变得更大。 对于负类样本而言同样，预测0.1的结果应当远比预测0.7的样本损失值要小得多。</span></div><div><span style="font-size: 13px;">alpha是平衡因子。加入gamma以后，原本“弱势”一方的样本有可能变为“强势”一方，所以要加平衡因子控制。</span></div><div><font style="font-size: 13px;"><br/></font></div><div><font style="font-size: 13px;"><span style="font-size: 13px; color: unset; font-weight: bold;">召回的样本：</span><span style="font-size: 13px; color: unset; font-weight: bold;">正:曝光并点击。负:随机采样。不能用曝光没点击作为负样本。因为但凡曝光的样本，都和用户比较相关。而召回场景下，上百万的候选item中，绝大部分item都是与user兴趣“八杆子打不着”的。</span></font></div><div><span style="font-size: 13px; font-weight: bold;">但随机采样并非均匀采样，这样会导致热门item霸占召回结果。所以随机采样必须配“热门item打压”。</span></div><div><span style="font-size: 13px; font-weight: bold;">采样还要考虑easy负样本和hard负样本。正样本是一只狗，那负样本不能全是猫、大象，要让模型开开眼界，所以还要引入狼、狐狸等hard负样本。</span></div><div><span style="font-size: 13px;">hard负样本采样方法：</span></div><div><span style="font-size: 13px;">1.爱彼迎：与正样本同城的房间，当做负样本。</span></div><div><span style="font-size: 13px;">2.曝光但未点击。</span></div><div><span style="font-size: 13px;">3.召回位置在100-500的样本，作为负样本。因为没那么相关。</span></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px; font-weight: bold;">什么情况下使用深度学习，什么情况使用机器学习？</span></div><div><span style="font-size: 13px; font-weight: bold;">1.数据量大，使用深度学习。数据量小，使用机器学习。</span></div><div><span style="font-size: 13px; font-weight: bold;">2.深度学习需要进行大量矩阵运算，对设备要求高。</span></div><div><span style="font-size: 13px; font-weight: bold;">3.机器学习需要人工特征工程，深度学习的特征是自己学出来的。</span></div><div><span style="font-size: 13px; font-weight: bold;">4.机器学习可解释性高，深度学习是黑盒，难以解释。</span></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px; color: unset; font-family: unset;">信息增益存在的问题？/ID3算法存在的问题？</span></div><div><span style="font-size: 13px;">答：使用信息增益会倾向于选</span><span style="font-size: 13px; font-weight: bold;">特征取值较多</span><span style="font-size: 13px;">的特征作为最优特征。 坏处：比如要划分用户，身份证号是取值最多的特征，信息增益最大。但是划分之后，毫无意义。</span></div><div><span style="font-size: 13px;">信息增益率：信息增益除以纯度。如果特征取值越多，纯度越大，分母就越大。</span></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px;">神经网络的参数初始化问题</span></div><div><span style="font-size: 13px;">初始化为固定常数：所有的神经元都具有相同的输出和更新梯度，并进行完全相同的更新，使得神经元不存在非对称性，效果大打折扣。</span></div><div><span style="font-size: 13px;">初始化过小：会导致输入过小，sigmoid函数发生梯度消失问题。</span></div><div><span style="font-size: 13px;">初始化过大：会导致输入过大，sigmoid函数落入饱和区，发生梯度消失问题。</span></div><div><span style="font-size: 13px;">全初始化为0：参数初始化为0，会导致后续所有的网络中的权重失效，即反向传播时，参数的偏导数为0，模型参数无法更新，出现梯度消失的问题。</span></div><div><font style="font-size: 13px;"><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/6D0888AF-2456-434A-9EF7-DF508B56AB9F.png" height="1715" width="1500"/><br/></font></div><div><span style="font-size: 13px;">初始化为0.1：缺点1：初始化为固定常数的缺点。2.前向传播过程中，随着层数增加，神经元的输出值也是会迅速向0靠拢的，同样会导致反向传播过程中梯度很小，导致梯度消失的问题。</span></div><div><span style="font-size: 13px;">通常要用高斯分布来随机初始化神经元权重。</span></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px;">BN和LN公式：</span></div><div><font style="font-size: 13px;"><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/530601A2-3C2C-4981-8E5A-1B71FD0EB84F.png" height="904" width="1052"/><br/></font></div><div><span style="font-size: 13px;">打压热门：</span></div><div><span style="font-size: 13px;">打压正样本中热门商品的权重。</span></div><div><span style="font-size: 13px;">增大负样本中热门商品的权重。</span></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px;">假设检验：</span></div><div><span style="font-size: 13px;">a1是base的准确率，a2是新的推荐算法的准确率。如果新算法的准确率比base高3%，就上线模型。显著性水平设为0.05。</span></div><div><span style="font-size: 13px;">原假设H0：a2-a1≤3%</span></div><div><span style="font-size: 13px;">备择假设H1：a2-a1＞3%</span></div><div><span style="font-size: 13px;">方法：做AB实验，分别打小流量，做一周实验，收集样本。计算P(a2-a1≤3%)，如果大于0.05，落入拒绝域内，就拒绝原假设H0。说明新算法比base的准确率显著高出3%以上，可以上线模型。</span></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px;">LR多分类：</span></div><div><font style="font-size: 13px;"><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/287440BC-F4A5-439C-8636-4A0691FB8FD0.png" height="158" width="524"/><br/></font></div><div><font style="font-size: 13px;"><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/7A17070A-A459-4B03-86DA-0E92250B1198.png" height="224" width="982"/><br/></font></div><div><span style="font-size: 13px;"><img src="%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86.resources/7823583F-51D8-4445-80B1-2DA7ED892393.png" height="103" width="1280"/><br/></span></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px;"><br/></span></div></body></html>