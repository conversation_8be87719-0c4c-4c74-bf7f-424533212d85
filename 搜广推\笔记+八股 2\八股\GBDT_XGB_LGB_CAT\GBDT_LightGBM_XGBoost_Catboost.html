<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/><meta name="exporter-version" content="Evernote Mac 9.7.14 (474683)"/><meta name="created" content="2021-11-14 07:44:14 +0000"/><meta name="updated" content="2022-06-11 07:36:01 +0000"/><meta name="content-class" content="yinxiang.markdown"/><title>GBDT/LightGBM/XGBoost/Catboost</title></head><body><div style="font-size: 14px; margin: 0; padding: 0; width: 100%;"><p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">GBDT/LightGBM/XGBoost/Catboost</strong><br/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">GBDT三大实现</strong>：Catboost,XGboost,Lightgbm<br/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">什么是梯度提升树？（Gradient Boosting Decison Tree，GBDT）</strong><br/>
以决策树为基函数的提升方法称为提升树，它迭代多棵回归树来实现共同决策。提升树利用加法模型和前向分步算法实现学习的优化过程。当损失函数时平方损失和指数损失函数（此时GBDT退化为Adaboost算法）时，每一步的优化很简单。对于一般的损失函数提出梯度提升算法，这是一种利用最速下降的近似方法，将损失函数的负梯度为回归问题中提升树算法的残差的近似值，拟合一个回归树。<br/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">为什么GBDT用回归树不用分类树？</strong><br/>
GBDT是加法模型，主要是利用负梯度逼近的方式，这就意味每棵树的值是连续的可叠加的，这一点和回归树输出连续值不谋而合，如果采用分类树，那么残差逼近进行叠加就会使得这种叠加没有意义。</p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">GBDT的梯度提升体现在哪？</strong><br/>
每一棵树都在拟合上一棵树的负梯度（当损失函数为平方差损失函数的时候，负梯度就是残差，对于一般损失函数，负梯度是残差的近似值）<br/>
残差：真实值-预测值<br/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">1.&nbsp;&nbsp;简介XGBoost?</strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <br/>
XGBoost属于集成学习中的boosting，将弱学习器提升为强学习器来完成学习任务，这些弱学习器称为基学习器。 XGBoost的基学习器是CART回归树（二叉树），与一般的回归树不同，XGBoost使用的是模型树，树模型的叶结点的输出值不是分到该叶节点的所有样本点的均值，<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">而是由一个函数生成的值（推导得到的信息增益）</strong>。&nbsp;&nbsp;&nbsp;&nbsp;另外，XGB支持<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">子采样</strong>，每轮计算不使用全部样本，采样样本是<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">不放回</strong>的，减少过拟合。XGB支持<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">列采样</strong>（和随机森林一样，列采样相当于在做随机特征筛选），提高计算速度又减少过拟合。<img src="GBDT_LightGBM_XGBoost_Catboost.resources/16A82542-80D5-4936-AC4D-FEFBB3102C21.png" height="182" width="724"/><br/>
第二项是正则项，控制模型的复杂度。（xgboost的复杂度体现在叶子结点个数以及叶子结点值）<br/>
<img src="GBDT_LightGBM_XGBoost_Catboost.resources/7E1D9E10-000B-47C2-8ED4-3DEC440C29DE.png" height="470" width="749"/><br/>
<img src="GBDT_LightGBM_XGBoost_Catboost.resources/2842B812-84C9-441E-B577-F70FCF1D384B.png" height="424" width="734"/><br/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">其中w是叶子结点权重，就是叶子节点的输出值</strong><br/>
<img src="GBDT_LightGBM_XGBoost_Catboost.resources/B01AF1D2-6E6C-45F6-AB8E-B9FF976E5AFD.png" height="282" width="784"/><br/>
<img src="GBDT_LightGBM_XGBoost_Catboost.resources/8587A16A-643A-49B8-A895-4CB4E556FBA8.png" height="447" width="1010"/><br/>
如何确定树的结构呢？换句话说，我们该用什么特征分割节点、分割节点的特征值又是多少呢？XGBoost用贪心算法遍历特征，用<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">损失函数增益值</strong>（注意这里是经过推导后的，不是一般的信息增益）判断该在哪个特征的何处分裂。损失增益函数是分裂后树的损失函数值和分裂前树的损失函数值之差，形式如下：<img src="GBDT_LightGBM_XGBoost_Catboost.resources/A366618F-84B5-42BD-8181-73E2239C3832.png" height="96" width="492"/><br/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">2.为什么XGBoost要二阶泰勒展开？ 为什么用二阶信息不用一阶？</strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <br/>
<img src="GBDT_LightGBM_XGBoost_Catboost.resources/2254C2E1-95F8-4FEA-85AB-E27CC930EC1A.png" height="82" width="744"/></p>
<ul style="line-height: 160%; box-sizing: content-box; display: block; list-style-type: disc; padding-left: 30px; margin: 6px 0 10px; color: #333;">
<li style="line-height: 160%; box-sizing: content-box; position: relative;">自适应损失函数：当目标函数是MSE时，展开是一阶项（残差）+二阶项的形式（官网说这是一个nice form），而其他目标函数，如logloss的展开式就没有这样的形式。&nbsp;&nbsp;为了能有个统一的形式，所以采用泰勒展开来得到二阶项，这样就能把MSE推导的那套直接复用到其他自定义损失函数上。而且泰勒的本质是尽量去模仿一个函数，我猜二阶泰勒展开已经足以近似大量损失函数了，典型的还有基于分类的对数似然损失函数。</li>
<li style="line-height: 160%; box-sizing: content-box; position: relative;">加快收敛：二阶信息本身能够让梯度收敛的更快更准确（优化算法中牛顿法证实）可以简单认为一阶导数引导梯度方向，二阶导数引导梯度方向如何变化。&nbsp;&nbsp;&nbsp;&nbsp; <br/>
平方损失函数展开：<br/>
<img src="GBDT_LightGBM_XGBoost_Catboost.resources/516CBA28-9A0E-40E9-B617-4B92ADD45110.png" height="381" width="883"/></li>
</ul>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">为什么要在形式上与MSE统一？</strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <br/>
因为MSE是最普遍且常用的损失函数，而且求导最容易，求导后的形式也十分简单。所以理论上只要损失函数形式与MSE统一了，那就只用推导MSE就好</p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">XGBoost里处理缺失值的方法？</strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <br/>
xgboost模型能够处理缺失值，也就是说<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">模型允许缺失值存在</strong>。论文中关于缺失值的处理与稀疏矩阵的处理看作一样。在分裂节点的时候不会对缺失值遍历，减少开销。会分别将缺失该特征值的样本分配到左节点和右节点两种情形，分别计算信息增益，沿着增益大的方向进行分裂。如果训练期间没有缺失值，预测时候有，自动将缺失值划分到右子树。</p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;">一棵树中每个结点在分裂时，寻找的是某个特征的最佳分裂点（特征值），完全可以不考虑存在特征值缺失的样本，也就是说，如果某些样本的特征值缺失，对寻找最佳分割点的影响不是很大。<br/>
<img src="GBDT_LightGBM_XGBoost_Catboost.resources/2069A320-7265-4802-B89E-2F0CED207599.png" height="914" width="1388"/></p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">GBDT和XGBoost的区别是什么？</strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <br/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">XGboost对异常值敏感</strong>，如果使用了平方损失函数<br/>
xgboost类似于gbdt的优化版，不论是精度还是效率上都有了提升。<br/>
与gbdt相比，具体的优点有：</p>
<ul style="line-height: 160%; box-sizing: content-box; display: block; list-style-type: disc; padding-left: 30px; margin: 6px 0 10px; color: #333;">
<li style="line-height: 160%; box-sizing: content-box; position: relative;">GBDT使用CART作为基分类器，XGboost还支持线性分类器，这时候XGboost相当于带L1和L2正则化的逻辑回归或者线性回归。</li>
<li style="line-height: 160%; box-sizing: content-box; position: relative;">gbdt在优化时只用到一阶导数信息，而XGBoost利用牛顿法优化，对损失函数进行泰勒二阶展开，优化时用到了一阶和二阶导数信息。一阶导数引导梯度方向，二阶导数引导梯度方向如何变化。可以加快收敛速度。</li>
<li style="line-height: 160%; box-sizing: content-box; position: relative;">对树的结构进行了<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">正则化约束</strong>，防止模型过度复杂，降低了过拟合的可能性。<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">l1正则化相当于减少叶子结点个数，L2正则化相当于限制叶子节点的输出值。</strong></li>
<li style="line-height: 160%; box-sizing: content-box; position: relative;">GBDT没有考虑缺失值，xgboost对缺失值进行了处理。</li>
<li style="line-height: 160%; box-sizing: content-box; position: relative;">缩减，相当于学习速率，每次迭代增加新的模型在前面乘上一个小于1的系数，降低优化的速度，每次走一小步逐步逼近最优模型比每一次走一大步逼近更容易避免过拟合。<br/>
<img src="GBDT_LightGBM_XGBoost_Catboost.resources/C7BDCB5D-FD84-4F45-A4D4-68495FB8C2DF.png" height="111" width="465"/></li>
<li style="line-height: 160%; box-sizing: content-box; position: relative;">列抽样。XGboost借鉴随机森林的做法，支持列抽样，即每次的输入特征不是全部特征，不仅能降低过拟合，还能减少计算</li>
<li style="line-height: 160%; box-sizing: content-box; position: relative;">XGBoost是level-wise的生长策略， 可以同时分裂同一层的叶子，容易进行多线程优化，也好控制模型复杂度，不容易过拟合。</li>
<li style="line-height: 160%; box-sizing: content-box; position: relative;">支持并行计算：在<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">特征粒度上并行，XGboost在计算最佳分裂属性和最佳切割点时</strong>可以调用CPU进行多线程的并行计算，极大的节约了时间，加速了训练过程。<br/>
缺点：<br/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">决策树的学习最耗时的一个步骤就是对特征的值进行排序</strong>（因为要确定最佳分割点），xgboost在训练之前，预先对数据进行排序，然后保存为block结构，后面的迭代中重复地使用这个结构，大大减小计算量。这个block结构也使得并行成为了可能，在进行节点分裂时，需要计算每个特征的增益，最终选增益最大的那个特征去做分裂，那么各个特征的增益计算就可以开多线程进行。<br/>
Xgboost使用了多个Block，存在多个机器上或者磁盘中。每个Block对应原来数据的子集。不同的Block可以在不同的机器上计算。</li>
</ul>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><img src="GBDT_LightGBM_XGBoost_Catboost.resources/6546937C-DC9B-404E-89E4-F6B7DD996ACE.png" height="632" width="857"/><br/>
<img src="GBDT_LightGBM_XGBoost_Catboost.resources/559539EB-A0FE-43DD-9590-EB2AE188D6AD.png" height="652" width="837"/><br/>
<img src="GBDT_LightGBM_XGBoost_Catboost.resources/6788F990-6E26-4773-81CD-DE9B3C61D764.png" height="511" width="884"/></p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">XGBoost如何选择特征：</strong></p>
<ul style="line-height: 160%; box-sizing: content-box; display: block; list-style-type: disc; padding-left: 30px; margin: 6px 0 10px; color: #333;">
<li style="line-height: 160%; box-sizing: content-box; position: relative;">该特征作为分裂特征在所有树中出现的次数</li>
<li style="line-height: 160%; box-sizing: content-box; position: relative;">该特征作为分裂特征的增益</li>
<li style="line-height: 160%; box-sizing: content-box; position: relative;">该特征作为分裂特征对样本的覆盖</li>
</ul>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">XGBoost节点分裂方式：</strong><br/>
level-wise，同时分裂同一层的叶子节点</p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">简介LightGBM？</strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <br/>
GBDT在每一次迭代的时候，都需要遍历整个训练数据多次。如果把整个训练数据装进内存则会限制训练数据的大小；如果不装进内存，反复地读写训练数据又会消耗非常大的时间。LightGBM是一个实现 GBDT算法的框架，<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">LightGBM提出的主要原因是为了解决GBDT在海量数据遇到的问题</strong>。&nbsp;&nbsp;&nbsp;&nbsp;<br/>
Xgboost不足：树节点在进行分裂时，我们需要计算每个特征的每个分割点对应的增益，即用贪心法枚举所有可能的分割点。当数据无法一次载入内存或者在分布式情况下，贪心算法效率就会变得很低。<br/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">LightGBM针对XGBoost做了以下优化：</strong></p>
<ul style="line-height: 160%; box-sizing: content-box; display: block; list-style-type: disc; padding-left: 30px; margin: 6px 0 10px; color: #333;">
<li style="line-height: 160%; box-sizing: content-box; position: relative;">基于直方图Histogram的决策树算法：<br/>
分桶思想，把连续特征值离散化成k个整数，将特征的个数降为k，而这个值一般用 8 位整型存储就足够了，内存消耗可以降低为原来的1/8</li>
<li style="line-height: 160%; box-sizing: content-box; position: relative;">带深度限制的Leaf-wise的叶子生长策略:&nbsp;&nbsp;在 Histogram 算法之上，LightGBM 进行进一步的优化。首先它抛弃了大多数 GBDT 工具使用的按层生长 (level-wise) 的决策树生长策略，而使用了带有深度限制的按叶子生长 (leaf-wise) 算法。  Leaf-wise 的缺点是可能会长出比较深的决策树，产生过拟合。因此 LightGBM 在 Leaf-wise 之上增加了一个最大深度的限制，在保证高效率的同时防止过拟合。<br/>
level-wise：遍历一次数据可以<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">同时分裂同一层的叶子</strong>，容易进行多线程优化，也好控制模型复杂度，不容易过拟合。但Level-wise 是一种低效的算法，因为它不加区分的对待同一层的叶子，带来了很多没必要的开销，因为实际上很多叶子的分裂增益较低，没必要进行搜索和分裂。<br/>
leaf-wise：从当前所有叶子中，<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">找到分裂增益最大的一个叶子</strong>，然后分裂，如此循环。</li>
<li style="line-height: 160%; box-sizing: content-box; position: relative;">直方图做差加速直接支持类别特征： LightGBM 优化了对类别特征的支持，<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">可以直接输入类别特征</strong>，不需要额外的0/1 展开。Cache命中率优化基于直方图的稀疏特征优化多线程优化</li>
<li style="line-height: 160%; box-sizing: content-box; position: relative;">LightGBM 原生支持并行学习，目前支持特征并行和数据并行的两种。<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">特征并行的主要思想是在不同机器在不同的特征集合上分别寻找最优的分割点</strong>，然后在机器间同步最优的分割点。数据并行则是让不同的机器先在本地构造直方图，然后进行全局的合并，最后在合并的直方图上面寻找最优分割点。</li>
</ul>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">传统的特征并行算法</strong></p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;">根据不同的特征子集，将数据集进行垂直切分。（不同机器worker有不同的特征子集）<br/>
每个worker寻找局部的最优分裂特征以及分裂点。<br/>
不同worker之间进行网络传输，交换最优分裂信息，最终得到最优的分裂信息。<br/>
具有最优分裂特征的worker，局部进行分裂，并将分裂结果广播到其他worker。<br/>
其他worker根据接收到的数据进行切分数据。<br/>
该方法不能有效地加速特征选择的效率，当数据量#data很大时，该并行方法不能加快效率。并且，最优的分裂结果需要在worker之间进行传输，需要消耗很多的传输资源以及传输时间。</p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">LightGBM的特征并行算法</strong><br/>
LightGBM并没有垂直的切分数据集，而是每个worker都有全量的训练数据，因此最优的特征分裂结果不需要传输到其他worker中，只需要将最优特征以及分裂点告诉其他worker，worker随后本地自己进行处理。处理过程如下：</p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;">每个worker在<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">基于局部的特征集合</strong>找到最优分裂特征。<br/>
workder间传输最优分裂信息，并得到全局最优分裂信息。<br/>
每个worker基于全局最优分裂信息，在本地进行数据分裂，生成决策树。<br/>
然而，当数据量很大时，特征并行算法还是受限于特征分裂效率。因此，当数据量大时，推荐使用数据并行算法。</p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">Xgboost/GBDT在调参时为什么树的深度很少时就能达到很高的精度？</strong><br/>
他们俩都属于Boosting,Boosting每一步都在上一轮的基础上更加拟合原数据，所以Bossting方法可以保证偏差小，因此对于每个基分类器来说重点就在于选择方差更小的分类器，即更简单的分类器，所以会选择深度很浅的决策树。<br/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">XGBoost如何处理数据不平衡问题？</strong><br/>
权重调整：<br/>
小样本量类别权重高，大样本量类别权重低<br/>
focal loss:解决难易样本数量不平衡问题</p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">比较LR和GBDT，什么情境下LR好于GBDT？</strong><br/>
（1）比较</p>
<ul style="line-height: 160%; box-sizing: content-box; display: block; list-style-type: disc; padding-left: 30px; margin: 6px 0 10px; color: #333;">
<li style="line-height: 160%; box-sizing: content-box; position: relative;">LR是线性模型，GBDT是非线性模型，因此为了增强模型的非线性表达能力，使用LR模型之前会有非常繁重的特征工程任务</li>
<li style="line-height: 160%; box-sizing: content-box; position: relative;">LR是单模，GBDT是集成模型，在数据低噪的情况下，GBDT的效果会优于LR</li>
<li style="line-height: 160%; box-sizing: content-box; position: relative;">LR使用梯度下降法进行训练，需要对特征进行归一化，而GBDT基于基尼系数选择特征，不需要做特征归一化<br/>
（2）优点</li>
<li style="line-height: 160%; box-sizing: content-box; position: relative;">当需要对模型进行解释的时候，GBDT比LR更加黑盒，因为我们不能去解释每一棵树。相比之下，LR的特征权重能够很直观地反映出特征对不同类样本的贡献程度</li>
<li style="line-height: 160%; box-sizing: content-box; position: relative;">LR可以并行化处理，GBDT是串行很难并行处理</li>
<li style="line-height: 160%; box-sizing: content-box; position: relative;">对于高维稀疏数据，GBDT很容易过拟合，得到很深的树，LR可以通过加入正则化防止过拟合</li>
</ul>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">GBDT与RF的区别</strong><br/>
相同点：<br/>
1、GBDT与RF都是采用多棵树组合作为最终结果；这是两者共同点。<br/>
不同点：<br/>
1、RF的树可以是回归树也可以是分类树，而GBDT只能是回归树。<br/>
2、RF中树是独立的，相互之间不影响，可以并行；而GBDT树之间有依赖，是串行。<br/>
3、RF最终的结果是多棵树表决决定，而GBDT是多棵树叠加组合最终的结果。<br/>
4、<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">RF对异常值不敏感，原因是多棵树表决</strong>，而GBDT使用平方损失函数的时候对异常值比较敏感。<br/>
5、RF是通过减少模型的方差来提高性能，而GBDT是减少模型的偏差来提高性能的。</p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">为什么bagging可以降低方差？</strong><br/>
<img src="GBDT_LightGBM_XGBoost_Catboost.resources/519FF54D-F366-40AD-A035-0CFCE0C61A69.png" height="289" width="680"/><br/>
<img src="GBDT_LightGBM_XGBoost_Catboost.resources/30BC0B2C-0A14-4006-837C-8E3B7FB51ABA.png" height="517" width="683"/><br/>
<img src="GBDT_LightGBM_XGBoost_Catboost.resources/2AB16B78-7242-49CB-930C-E5F979B1ADB8.png" height="239" width="672"/></p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">随机森林为什么要进行有放回的随机抽样？</strong></p>
<ol style="line-height: 160%; box-sizing: content-box; display: block; padding-left: 30px; margin: 6px 0 10px; color: #333; list-style-type: decimal;">
<li style="line-height: 160%; box-sizing: content-box;">如果不放回抽样，那么每棵树用的样本完全不同，基学习器之间的<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">相似性小</strong>，投票结果差**，模型偏差大**；</li>
<li style="line-height: 160%; box-sizing: content-box;">如果不抽样，那么基学习器用所有样本训练，基学习器太相似<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">差异性太小</strong>，模型的泛化性就很差，<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">方差大</strong>；</li>
<li style="line-height: 160%; box-sizing: content-box;">为什么不随机抽样？这里自助采样可以产生一部分袋外样本，可以用来做袋外估计；另外自助采样一定程度上改变了每个基学习器所用数据的样本分布，一定程度上引入了噪音，增加了模型的泛化能力。</li>
</ol>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">随机森林解决不平衡数据问题？</strong><br/>
自助采样法，每次随机选取一部分子集，相对来说可以均衡的使用每个类别数据。？？？？<br/>
在每次生成训练集时使用所有分类中的小样本量，同时从分类中的大样本量中随机抽取数据来与小样本量合并构成训练集，这样反复多次会得到很多训练集和训练模型。</p>
<ul style="line-height: 160%; box-sizing: content-box; display: block; list-style-type: disc; padding-left: 30px; margin: 6px 0 10px; color: #333;">
<li style="line-height: 160%; box-sizing: content-box; position: relative;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">Stacking</strong></li>
</ul>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">有缺失值的数据在经过缺失处理后：</strong><br/>
数据量很小，朴素贝叶斯，贝叶斯模型对于缺失数据也比较稳定<br/>
数据量适中或者较大，用树模型 ，优先xgboost<br/>
数据量较大，也可以用神经网络<br/>
避免使用距离度量相关的模型，如KNN和SVM</p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">为什么随机森林的树深度往往大于 GBDT 的树深度？</strong></p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;">对于 Bagging 算法来说，其可以保证方差。所以对于每个基分类器来说，目标就是如何降低这个偏差，所以我们会采用深度很深甚至不剪枝的决策树。<br/>
对于 Boosting 来说，每一步我们都会在上一轮的基础上更加拟合原数据，所以可以保证偏差，所以对于每个基分类器来说，问题就在于如何选择方差 更小的分类器，即更简单的分类器，所以我们选择了深度很浅的决策树。</p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">交叉验证和自助采样区别？</strong><br/>
交叉验证随机使用不同的划分重复p次，最终的评估结果是这p次k折交叉验证结果的均值，目的是减小因为样本不同而引入的差别。<br/>
自助法能从初始数据集中产生多个不同的训练集，可以充分利用数据</p>
</div><center style="display:none !important;visibility:collapse !important;height:0 !important;white-space:nowrap;width:100%;overflow:hidden">**GBDT%2FLightGBM%2FXGBoost%2FCatboost**%0A**GBDT%E4%B8%89%E5%A4%A7%E5%AE%9E%E7%8E%B0**%EF%BC%9ACatboost%2CXGboost%2CLightgbm%0A**%E4%BB%80%E4%B9%88%E6%98%AF%E6%A2%AF%E5%BA%A6%E6%8F%90%E5%8D%87%E6%A0%91%EF%BC%9F%EF%BC%88Gradient%20Boosting%20Decison%20Tree%EF%BC%8CGBDT%EF%BC%89**%0A%E4%BB%A5%E5%86%B3%E7%AD%96%E6%A0%91%E4%B8%BA%E5%9F%BA%E5%87%BD%E6%95%B0%E7%9A%84%E6%8F%90%E5%8D%87%E6%96%B9%E6%B3%95%E7%A7%B0%E4%B8%BA%E6%8F%90%E5%8D%87%E6%A0%91%EF%BC%8C%E5%AE%83%E8%BF%AD%E4%BB%A3%E5%A4%9A%E6%A3%B5%E5%9B%9E%E5%BD%92%E6%A0%91%E6%9D%A5%E5%AE%9E%E7%8E%B0%E5%85%B1%E5%90%8C%E5%86%B3%E7%AD%96%E3%80%82%E6%8F%90%E5%8D%87%E6%A0%91%E5%88%A9%E7%94%A8%E5%8A%A0%E6%B3%95%E6%A8%A1%E5%9E%8B%E5%92%8C%E5%89%8D%E5%90%91%E5%88%86%E6%AD%A5%E7%AE%97%E6%B3%95%E5%AE%9E%E7%8E%B0%E5%AD%A6%E4%B9%A0%E7%9A%84%E4%BC%98%E5%8C%96%E8%BF%87%E7%A8%8B%E3%80%82%E5%BD%93%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%E6%97%B6%E5%B9%B3%E6%96%B9%E6%8D%9F%E5%A4%B1%E5%92%8C%E6%8C%87%E6%95%B0%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%EF%BC%88%E6%AD%A4%E6%97%B6GBDT%E9%80%80%E5%8C%96%E4%B8%BAAdaboost%E7%AE%97%E6%B3%95%EF%BC%89%E6%97%B6%EF%BC%8C%E6%AF%8F%E4%B8%80%E6%AD%A5%E7%9A%84%E4%BC%98%E5%8C%96%E5%BE%88%E7%AE%80%E5%8D%95%E3%80%82%E5%AF%B9%E4%BA%8E%E4%B8%80%E8%88%AC%E7%9A%84%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%E6%8F%90%E5%87%BA%E6%A2%AF%E5%BA%A6%E6%8F%90%E5%8D%87%E7%AE%97%E6%B3%95%EF%BC%8C%E8%BF%99%E6%98%AF%E4%B8%80%E7%A7%8D%E5%88%A9%E7%94%A8%E6%9C%80%E9%80%9F%E4%B8%8B%E9%99%8D%E7%9A%84%E8%BF%91%E4%BC%BC%E6%96%B9%E6%B3%95%EF%BC%8C%E5%B0%86%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%E7%9A%84%E8%B4%9F%E6%A2%AF%E5%BA%A6%E4%B8%BA%E5%9B%9E%E5%BD%92%E9%97%AE%E9%A2%98%E4%B8%AD%E6%8F%90%E5%8D%87%E6%A0%91%E7%AE%97%E6%B3%95%E7%9A%84%E6%AE%8B%E5%B7%AE%E7%9A%84%E8%BF%91%E4%BC%BC%E5%80%BC%EF%BC%8C%E6%8B%9F%E5%90%88%E4%B8%80%E4%B8%AA%E5%9B%9E%E5%BD%92%E6%A0%91%E3%80%82%0A**%E4%B8%BA%E4%BB%80%E4%B9%88GBDT%E7%94%A8%E5%9B%9E%E5%BD%92%E6%A0%91%E4%B8%8D%E7%94%A8%E5%88%86%E7%B1%BB%E6%A0%91%EF%BC%9F**%0AGBDT%E6%98%AF%E5%8A%A0%E6%B3%95%E6%A8%A1%E5%9E%8B%EF%BC%8C%E4%B8%BB%E8%A6%81%E6%98%AF%E5%88%A9%E7%94%A8%E8%B4%9F%E6%A2%AF%E5%BA%A6%E9%80%BC%E8%BF%91%E7%9A%84%E6%96%B9%E5%BC%8F%EF%BC%8C%E8%BF%99%E5%B0%B1%E6%84%8F%E5%91%B3%E6%AF%8F%E6%A3%B5%E6%A0%91%E7%9A%84%E5%80%BC%E6%98%AF%E8%BF%9E%E7%BB%AD%E7%9A%84%E5%8F%AF%E5%8F%A0%E5%8A%A0%E7%9A%84%EF%BC%8C%E8%BF%99%E4%B8%80%E7%82%B9%E5%92%8C%E5%9B%9E%E5%BD%92%E6%A0%91%E8%BE%93%E5%87%BA%E8%BF%9E%E7%BB%AD%E5%80%BC%E4%B8%8D%E8%B0%8B%E8%80%8C%E5%90%88%EF%BC%8C%E5%A6%82%E6%9E%9C%E9%87%87%E7%94%A8%E5%88%86%E7%B1%BB%E6%A0%91%EF%BC%8C%E9%82%A3%E4%B9%88%E6%AE%8B%E5%B7%AE%E9%80%BC%E8%BF%91%E8%BF%9B%E8%A1%8C%E5%8F%A0%E5%8A%A0%E5%B0%B1%E4%BC%9A%E4%BD%BF%E5%BE%97%E8%BF%99%E7%A7%8D%E5%8F%A0%E5%8A%A0%E6%B2%A1%E6%9C%89%E6%84%8F%E4%B9%89%E3%80%82%0A%0A**GBDT%E7%9A%84%E6%A2%AF%E5%BA%A6%E6%8F%90%E5%8D%87%E4%BD%93%E7%8E%B0%E5%9C%A8%E5%93%AA%EF%BC%9F**%0A%E6%AF%8F%E4%B8%80%E6%A3%B5%E6%A0%91%E9%83%BD%E5%9C%A8%E6%8B%9F%E5%90%88%E4%B8%8A%E4%B8%80%E6%A3%B5%E6%A0%91%E7%9A%84%E8%B4%9F%E6%A2%AF%E5%BA%A6%EF%BC%88%E5%BD%93%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%E4%B8%BA%E5%B9%B3%E6%96%B9%E5%B7%AE%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%E7%9A%84%E6%97%B6%E5%80%99%EF%BC%8C%E8%B4%9F%E6%A2%AF%E5%BA%A6%E5%B0%B1%E6%98%AF%E6%AE%8B%E5%B7%AE%EF%BC%8C%E5%AF%B9%E4%BA%8E%E4%B8%80%E8%88%AC%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%EF%BC%8C%E8%B4%9F%E6%A2%AF%E5%BA%A6%E6%98%AF%E6%AE%8B%E5%B7%AE%E7%9A%84%E8%BF%91%E4%BC%BC%E5%80%BC%EF%BC%89%0A%E6%AE%8B%E5%B7%AE%EF%BC%9A%E7%9C%9F%E5%AE%9E%E5%80%BC-%E9%A2%84%E6%B5%8B%E5%80%BC%0A**1.%C2%A0%E7%AE%80%E4%BB%8BXGBoost%3F**%C2%A0%C2%A0%20%C2%A0%0A%20%20%20%20XGBoost%E5%B1%9E%E4%BA%8E%E9%9B%86%E6%88%90%E5%AD%A6%E4%B9%A0%E4%B8%AD%E7%9A%84boosting%EF%BC%8C%E5%B0%86%E5%BC%B1%E5%AD%A6%E4%B9%A0%E5%99%A8%E6%8F%90%E5%8D%87%E4%B8%BA%E5%BC%BA%E5%AD%A6%E4%B9%A0%E5%99%A8%E6%9D%A5%E5%AE%8C%E6%88%90%E5%AD%A6%E4%B9%A0%E4%BB%BB%E5%8A%A1%EF%BC%8C%E8%BF%99%E4%BA%9B%E5%BC%B1%E5%AD%A6%E4%B9%A0%E5%99%A8%E7%A7%B0%E4%B8%BA%E5%9F%BA%E5%AD%A6%E4%B9%A0%E5%99%A8%E3%80%82%20XGBoost%E7%9A%84%E5%9F%BA%E5%AD%A6%E4%B9%A0%E5%99%A8%E6%98%AFCART%E5%9B%9E%E5%BD%92%E6%A0%91%EF%BC%88%E4%BA%8C%E5%8F%89%E6%A0%91%EF%BC%89%EF%BC%8C%E4%B8%8E%E4%B8%80%E8%88%AC%E7%9A%84%E5%9B%9E%E5%BD%92%E6%A0%91%E4%B8%8D%E5%90%8C%EF%BC%8CXGBoost%E4%BD%BF%E7%94%A8%E7%9A%84%E6%98%AF%E6%A8%A1%E5%9E%8B%E6%A0%91%EF%BC%8C%E6%A0%91%E6%A8%A1%E5%9E%8B%E7%9A%84%E5%8F%B6%E7%BB%93%E7%82%B9%E7%9A%84%E8%BE%93%E5%87%BA%E5%80%BC%E4%B8%8D%E6%98%AF%E5%88%86%E5%88%B0%E8%AF%A5%E5%8F%B6%E8%8A%82%E7%82%B9%E7%9A%84%E6%89%80%E6%9C%89%E6%A0%B7%E6%9C%AC%E7%82%B9%E7%9A%84%E5%9D%87%E5%80%BC%EF%BC%8C**%E8%80%8C%E6%98%AF%E7%94%B1%E4%B8%80%E4%B8%AA%E5%87%BD%E6%95%B0%E7%94%9F%E6%88%90%E7%9A%84%E5%80%BC%EF%BC%88%E6%8E%A8%E5%AF%BC%E5%BE%97%E5%88%B0%E7%9A%84%E4%BF%A1%E6%81%AF%E5%A2%9E%E7%9B%8A%EF%BC%89**%E3%80%82%C2%A0%C2%A0%E5%8F%A6%E5%A4%96%EF%BC%8CXGB%E6%94%AF%E6%8C%81**%E5%AD%90%E9%87%87%E6%A0%B7**%EF%BC%8C%E6%AF%8F%E8%BD%AE%E8%AE%A1%E7%AE%97%E4%B8%8D%E4%BD%BF%E7%94%A8%E5%85%A8%E9%83%A8%E6%A0%B7%E6%9C%AC%EF%BC%8C%E9%87%87%E6%A0%B7%E6%A0%B7%E6%9C%AC%E6%98%AF**%E4%B8%8D%E6%94%BE%E5%9B%9E**%E7%9A%84%EF%BC%8C%E5%87%8F%E5%B0%91%E8%BF%87%E6%8B%9F%E5%90%88%E3%80%82XGB%E6%94%AF%E6%8C%81**%E5%88%97%E9%87%87%E6%A0%B7**%EF%BC%88%E5%92%8C%E9%9A%8F%E6%9C%BA%E6%A3%AE%E6%9E%97%E4%B8%80%E6%A0%B7%EF%BC%8C%E5%88%97%E9%87%87%E6%A0%B7%E7%9B%B8%E5%BD%93%E4%BA%8E%E5%9C%A8%E5%81%9A%E9%9A%8F%E6%9C%BA%E7%89%B9%E5%BE%81%E7%AD%9B%E9%80%89%EF%BC%89%EF%BC%8C%E6%8F%90%E9%AB%98%E8%AE%A1%E7%AE%97%E9%80%9F%E5%BA%A6%E5%8F%88%E5%87%8F%E5%B0%91%E8%BF%87%E6%8B%9F%E5%90%88%E3%80%82!%5B213f369b5318847eacde426e299b5fa6.png%5D(en-resource%3A%2F%2Fdatabase%2F2016%3A1)%0A%20%E7%AC%AC%E4%BA%8C%E9%A1%B9%E6%98%AF%E6%AD%A3%E5%88%99%E9%A1%B9%EF%BC%8C%E6%8E%A7%E5%88%B6%E6%A8%A1%E5%9E%8B%E7%9A%84%E5%A4%8D%E6%9D%82%E5%BA%A6%E3%80%82%EF%BC%88xgboost%E7%9A%84%E5%A4%8D%E6%9D%82%E5%BA%A6%E4%BD%93%E7%8E%B0%E5%9C%A8%E5%8F%B6%E5%AD%90%E7%BB%93%E7%82%B9%E4%B8%AA%E6%95%B0%E4%BB%A5%E5%8F%8A%E5%8F%B6%E5%AD%90%E7%BB%93%E7%82%B9%E5%80%BC%EF%BC%89%0A!%5B83834534dec2acaec8de81f39b702e2d.png%5D(en-resource%3A%2F%2Fdatabase%2F2020%3A1)%0A!%5B27fad2c091f19f373280113a492219d8.png%5D(en-resource%3A%2F%2Fdatabase%2F2017%3A1)%0A**%E5%85%B6%E4%B8%ADw%E6%98%AF%E5%8F%B6%E5%AD%90%E7%BB%93%E7%82%B9%E6%9D%83%E9%87%8D%EF%BC%8C%E5%B0%B1%E6%98%AF%E5%8F%B6%E5%AD%90%E8%8A%82%E7%82%B9%E7%9A%84%E8%BE%93%E5%87%BA%E5%80%BC**%0A!%5Bcd686d7c23a8a22d87a6782b0062c089.png%5D(en-resource%3A%2F%2Fdatabase%2F2027%3A1)%0A!%5Bc6c13095269c785bbdc11ab01f3995f6.png%5D(en-resource%3A%2F%2Fdatabase%2F2025%3A1)%0A%20%20%E5%A6%82%E4%BD%95%E7%A1%AE%E5%AE%9A%E6%A0%91%E7%9A%84%E7%BB%93%E6%9E%84%E5%91%A2%EF%BC%9F%E6%8D%A2%E5%8F%A5%E8%AF%9D%E8%AF%B4%EF%BC%8C%E6%88%91%E4%BB%AC%E8%AF%A5%E7%94%A8%E4%BB%80%E4%B9%88%E7%89%B9%E5%BE%81%E5%88%86%E5%89%B2%E8%8A%82%E7%82%B9%E3%80%81%E5%88%86%E5%89%B2%E8%8A%82%E7%82%B9%E7%9A%84%E7%89%B9%E5%BE%81%E5%80%BC%E5%8F%88%E6%98%AF%E5%A4%9A%E5%B0%91%E5%91%A2%EF%BC%9FXGBoost%E7%94%A8%E8%B4%AA%E5%BF%83%E7%AE%97%E6%B3%95%E9%81%8D%E5%8E%86%E7%89%B9%E5%BE%81%EF%BC%8C%E7%94%A8**%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%E5%A2%9E%E7%9B%8A%E5%80%BC**%EF%BC%88%E6%B3%A8%E6%84%8F%E8%BF%99%E9%87%8C%E6%98%AF%E7%BB%8F%E8%BF%87%E6%8E%A8%E5%AF%BC%E5%90%8E%E7%9A%84%EF%BC%8C%E4%B8%8D%E6%98%AF%E4%B8%80%E8%88%AC%E7%9A%84%E4%BF%A1%E6%81%AF%E5%A2%9E%E7%9B%8A%EF%BC%89%E5%88%A4%E6%96%AD%E8%AF%A5%E5%9C%A8%E5%93%AA%E4%B8%AA%E7%89%B9%E5%BE%81%E7%9A%84%E4%BD%95%E5%A4%84%E5%88%86%E8%A3%82%E3%80%82%E6%8D%9F%E5%A4%B1%E5%A2%9E%E7%9B%8A%E5%87%BD%E6%95%B0%E6%98%AF%E5%88%86%E8%A3%82%E5%90%8E%E6%A0%91%E7%9A%84%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%E5%80%BC%E5%92%8C%E5%88%86%E8%A3%82%E5%89%8D%E6%A0%91%E7%9A%84%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%E5%80%BC%E4%B9%8B%E5%B7%AE%EF%BC%8C%E5%BD%A2%E5%BC%8F%E5%A6%82%E4%B8%8B%EF%BC%9A!%5B961a5e59cc526445d65792664f9f0633.png%5D(en-resource%3A%2F%2Fdatabase%2F2022%3A1)%0A**2.%E4%B8%BA%E4%BB%80%E4%B9%88XGBoost%E8%A6%81%E4%BA%8C%E9%98%B6%E6%B3%B0%E5%8B%92%E5%B1%95%E5%BC%80%EF%BC%9F%20%E4%B8%BA%E4%BB%80%E4%B9%88%E7%94%A8%E4%BA%8C%E9%98%B6%E4%BF%A1%E6%81%AF%E4%B8%8D%E7%94%A8%E4%B8%80%E9%98%B6%EF%BC%9F**%C2%A0%C2%A0%20%C2%A0%C2%A0%C2%A0%20%0A!%5Bcac82c6d001f0712d0a4d7a4a424ecfe.png%5D(en-resource%3A%2F%2Fdatabase%2F2026%3A1)%0A*%20%E8%87%AA%E9%80%82%E5%BA%94%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%EF%BC%9A%E5%BD%93%E7%9B%AE%E6%A0%87%E5%87%BD%E6%95%B0%E6%98%AFMSE%E6%97%B6%EF%BC%8C%E5%B1%95%E5%BC%80%E6%98%AF%E4%B8%80%E9%98%B6%E9%A1%B9%EF%BC%88%E6%AE%8B%E5%B7%AE%EF%BC%89%2B%E4%BA%8C%E9%98%B6%E9%A1%B9%E7%9A%84%E5%BD%A2%E5%BC%8F%EF%BC%88%E5%AE%98%E7%BD%91%E8%AF%B4%E8%BF%99%E6%98%AF%E4%B8%80%E4%B8%AAnice%20form%EF%BC%89%EF%BC%8C%E8%80%8C%E5%85%B6%E4%BB%96%E7%9B%AE%E6%A0%87%E5%87%BD%E6%95%B0%EF%BC%8C%E5%A6%82logloss%E7%9A%84%E5%B1%95%E5%BC%80%E5%BC%8F%E5%B0%B1%E6%B2%A1%E6%9C%89%E8%BF%99%E6%A0%B7%E7%9A%84%E5%BD%A2%E5%BC%8F%E3%80%82%C2%A0%E4%B8%BA%E4%BA%86%E8%83%BD%E6%9C%89%E4%B8%AA%E7%BB%9F%E4%B8%80%E7%9A%84%E5%BD%A2%E5%BC%8F%EF%BC%8C%E6%89%80%E4%BB%A5%E9%87%87%E7%94%A8%E6%B3%B0%E5%8B%92%E5%B1%95%E5%BC%80%E6%9D%A5%E5%BE%97%E5%88%B0%E4%BA%8C%E9%98%B6%E9%A1%B9%EF%BC%8C%E8%BF%99%E6%A0%B7%E5%B0%B1%E8%83%BD%E6%8A%8AMSE%E6%8E%A8%E5%AF%BC%E7%9A%84%E9%82%A3%E5%A5%97%E7%9B%B4%E6%8E%A5%E5%A4%8D%E7%94%A8%E5%88%B0%E5%85%B6%E4%BB%96%E8%87%AA%E5%AE%9A%E4%B9%89%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%E4%B8%8A%E3%80%82%E8%80%8C%E4%B8%94%E6%B3%B0%E5%8B%92%E7%9A%84%E6%9C%AC%E8%B4%A8%E6%98%AF%E5%B0%BD%E9%87%8F%E5%8E%BB%E6%A8%A1%E4%BB%BF%E4%B8%80%E4%B8%AA%E5%87%BD%E6%95%B0%EF%BC%8C%E6%88%91%E7%8C%9C%E4%BA%8C%E9%98%B6%E6%B3%B0%E5%8B%92%E5%B1%95%E5%BC%80%E5%B7%B2%E7%BB%8F%E8%B6%B3%E4%BB%A5%E8%BF%91%E4%BC%BC%E5%A4%A7%E9%87%8F%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%E4%BA%86%EF%BC%8C%E5%85%B8%E5%9E%8B%E7%9A%84%E8%BF%98%E6%9C%89%E5%9F%BA%E4%BA%8E%E5%88%86%E7%B1%BB%E7%9A%84%E5%AF%B9%E6%95%B0%E4%BC%BC%E7%84%B6%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%E3%80%82%C2%A0%C2%A0%20%C2%A0%0A*%20%E5%8A%A0%E5%BF%AB%E6%94%B6%E6%95%9B%EF%BC%9A%E4%BA%8C%E9%98%B6%E4%BF%A1%E6%81%AF%E6%9C%AC%E8%BA%AB%E8%83%BD%E5%A4%9F%E8%AE%A9%E6%A2%AF%E5%BA%A6%E6%94%B6%E6%95%9B%E7%9A%84%E6%9B%B4%E5%BF%AB%E6%9B%B4%E5%87%86%E7%A1%AE%EF%BC%88%E4%BC%98%E5%8C%96%E7%AE%97%E6%B3%95%E4%B8%AD%E7%89%9B%E9%A1%BF%E6%B3%95%E8%AF%81%E5%AE%9E%EF%BC%89%E5%8F%AF%E4%BB%A5%E7%AE%80%E5%8D%95%E8%AE%A4%E4%B8%BA%E4%B8%80%E9%98%B6%E5%AF%BC%E6%95%B0%E5%BC%95%E5%AF%BC%E6%A2%AF%E5%BA%A6%E6%96%B9%E5%90%91%EF%BC%8C%E4%BA%8C%E9%98%B6%E5%AF%BC%E6%95%B0%E5%BC%95%E5%AF%BC%E6%A2%AF%E5%BA%A6%E6%96%B9%E5%90%91%E5%A6%82%E4%BD%95%E5%8F%98%E5%8C%96%E3%80%82%C2%A0%20%C2%A0%20%0A%E5%B9%B3%E6%96%B9%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%E5%B1%95%E5%BC%80%EF%BC%9A%0A!%5Bd0aaf452ad861dfc6a206823e33d20a1.png%5D(en-resource%3A%2F%2Fdatabase%2F2028%3A1)%0A%0A**%E4%B8%BA%E4%BB%80%E4%B9%88%E8%A6%81%E5%9C%A8%E5%BD%A2%E5%BC%8F%E4%B8%8A%E4%B8%8EMSE%E7%BB%9F%E4%B8%80%EF%BC%9F**%C2%A0%20%C2%A0%20%C2%A0%20%C2%A0%20%C2%A0%0A%E5%9B%A0%E4%B8%BAMSE%E6%98%AF%E6%9C%80%E6%99%AE%E9%81%8D%E4%B8%94%E5%B8%B8%E7%94%A8%E7%9A%84%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%EF%BC%8C%E8%80%8C%E4%B8%94%E6%B1%82%E5%AF%BC%E6%9C%80%E5%AE%B9%E6%98%93%EF%BC%8C%E6%B1%82%E5%AF%BC%E5%90%8E%E7%9A%84%E5%BD%A2%E5%BC%8F%E4%B9%9F%E5%8D%81%E5%88%86%E7%AE%80%E5%8D%95%E3%80%82%E6%89%80%E4%BB%A5%E7%90%86%E8%AE%BA%E4%B8%8A%E5%8F%AA%E8%A6%81%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%E5%BD%A2%E5%BC%8F%E4%B8%8EMSE%E7%BB%9F%E4%B8%80%E4%BA%86%EF%BC%8C%E9%82%A3%E5%B0%B1%E5%8F%AA%E7%94%A8%E6%8E%A8%E5%AF%BCMSE%E5%B0%B1%E5%A5%BD%20%C2%A0%0A%0A**XGBoost%E9%87%8C%E5%A4%84%E7%90%86%E7%BC%BA%E5%A4%B1%E5%80%BC%E7%9A%84%E6%96%B9%E6%B3%95%EF%BC%9F**%C2%A0%C2%A0%20%C2%A0%C2%A0%C2%A0%20%C2%A0%0Axgboost%E6%A8%A1%E5%9E%8B%E8%83%BD%E5%A4%9F%E5%A4%84%E7%90%86%E7%BC%BA%E5%A4%B1%E5%80%BC%EF%BC%8C%E4%B9%9F%E5%B0%B1%E6%98%AF%E8%AF%B4**%E6%A8%A1%E5%9E%8B%E5%85%81%E8%AE%B8%E7%BC%BA%E5%A4%B1%E5%80%BC%E5%AD%98%E5%9C%A8**%E3%80%82%E8%AE%BA%E6%96%87%E4%B8%AD%E5%85%B3%E4%BA%8E%E7%BC%BA%E5%A4%B1%E5%80%BC%E7%9A%84%E5%A4%84%E7%90%86%E4%B8%8E%E7%A8%80%E7%96%8F%E7%9F%A9%E9%98%B5%E7%9A%84%E5%A4%84%E7%90%86%E7%9C%8B%E4%BD%9C%E4%B8%80%E6%A0%B7%E3%80%82%E5%9C%A8%E5%88%86%E8%A3%82%E8%8A%82%E7%82%B9%E7%9A%84%E6%97%B6%E5%80%99%E4%B8%8D%E4%BC%9A%E5%AF%B9%E7%BC%BA%E5%A4%B1%E5%80%BC%E9%81%8D%E5%8E%86%EF%BC%8C%E5%87%8F%E5%B0%91%E5%BC%80%E9%94%80%E3%80%82%E4%BC%9A%E5%88%86%E5%88%AB%E5%B0%86%E7%BC%BA%E5%A4%B1%E8%AF%A5%E7%89%B9%E5%BE%81%E5%80%BC%E7%9A%84%E6%A0%B7%E6%9C%AC%E5%88%86%E9%85%8D%E5%88%B0%E5%B7%A6%E8%8A%82%E7%82%B9%E5%92%8C%E5%8F%B3%E8%8A%82%E7%82%B9%E4%B8%A4%E7%A7%8D%E6%83%85%E5%BD%A2%EF%BC%8C%E5%88%86%E5%88%AB%E8%AE%A1%E7%AE%97%E4%BF%A1%E6%81%AF%E5%A2%9E%E7%9B%8A%EF%BC%8C%E6%B2%BF%E7%9D%80%E5%A2%9E%E7%9B%8A%E5%A4%A7%E7%9A%84%E6%96%B9%E5%90%91%E8%BF%9B%E8%A1%8C%E5%88%86%E8%A3%82%E3%80%82%E5%A6%82%E6%9E%9C%E8%AE%AD%E7%BB%83%E6%9C%9F%E9%97%B4%E6%B2%A1%E6%9C%89%E7%BC%BA%E5%A4%B1%E5%80%BC%EF%BC%8C%E9%A2%84%E6%B5%8B%E6%97%B6%E5%80%99%E6%9C%89%EF%BC%8C%E8%87%AA%E5%8A%A8%E5%B0%86%E7%BC%BA%E5%A4%B1%E5%80%BC%E5%88%92%E5%88%86%E5%88%B0%E5%8F%B3%E5%AD%90%E6%A0%91%E3%80%82%C2%A0%20%C2%A0%0A%0A%E4%B8%80%E6%A3%B5%E6%A0%91%E4%B8%AD%E6%AF%8F%E4%B8%AA%E7%BB%93%E7%82%B9%E5%9C%A8%E5%88%86%E8%A3%82%E6%97%B6%EF%BC%8C%E5%AF%BB%E6%89%BE%E7%9A%84%E6%98%AF%E6%9F%90%E4%B8%AA%E7%89%B9%E5%BE%81%E7%9A%84%E6%9C%80%E4%BD%B3%E5%88%86%E8%A3%82%E7%82%B9%EF%BC%88%E7%89%B9%E5%BE%81%E5%80%BC%EF%BC%89%EF%BC%8C%E5%AE%8C%E5%85%A8%E5%8F%AF%E4%BB%A5%E4%B8%8D%E8%80%83%E8%99%91%E5%AD%98%E5%9C%A8%E7%89%B9%E5%BE%81%E5%80%BC%E7%BC%BA%E5%A4%B1%E7%9A%84%E6%A0%B7%E6%9C%AC%EF%BC%8C%E4%B9%9F%E5%B0%B1%E6%98%AF%E8%AF%B4%EF%BC%8C%E5%A6%82%E6%9E%9C%E6%9F%90%E4%BA%9B%E6%A0%B7%E6%9C%AC%E7%9A%84%E7%89%B9%E5%BE%81%E5%80%BC%E7%BC%BA%E5%A4%B1%EF%BC%8C%E5%AF%B9%E5%AF%BB%E6%89%BE%E6%9C%80%E4%BD%B3%E5%88%86%E5%89%B2%E7%82%B9%E7%9A%84%E5%BD%B1%E5%93%8D%E4%B8%8D%E6%98%AF%E5%BE%88%E5%A4%A7%E3%80%82%0A!%5B94167270999dd225bf10e8f074c18e63.png%5D(en-resource%3A%2F%2Fdatabase%2F2021%3A1)%0A%0A**GBDT%E5%92%8CXGBoost%E7%9A%84%E5%8C%BA%E5%88%AB%E6%98%AF%E4%BB%80%E4%B9%88%EF%BC%9F**%C2%A0%C2%A0%20%C2%A0%C2%A0%C2%A0%0A**XGboost%E5%AF%B9%E5%BC%82%E5%B8%B8%E5%80%BC%E6%95%8F%E6%84%9F**%EF%BC%8C%E5%A6%82%E6%9E%9C%E4%BD%BF%E7%94%A8%E4%BA%86%E5%B9%B3%E6%96%B9%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%0Axgboost%E7%B1%BB%E4%BC%BC%E4%BA%8Egbdt%E7%9A%84%E4%BC%98%E5%8C%96%E7%89%88%EF%BC%8C%E4%B8%8D%E8%AE%BA%E6%98%AF%E7%B2%BE%E5%BA%A6%E8%BF%98%E6%98%AF%E6%95%88%E7%8E%87%E4%B8%8A%E9%83%BD%E6%9C%89%E4%BA%86%E6%8F%90%E5%8D%87%E3%80%82%0A%E4%B8%8Egbdt%E7%9B%B8%E6%AF%94%EF%BC%8C%E5%85%B7%E4%BD%93%E7%9A%84%E4%BC%98%E7%82%B9%E6%9C%89%EF%BC%9A%0A*%20GBDT%E4%BD%BF%E7%94%A8CART%E4%BD%9C%E4%B8%BA%E5%9F%BA%E5%88%86%E7%B1%BB%E5%99%A8%EF%BC%8CXGboost%E8%BF%98%E6%94%AF%E6%8C%81%E7%BA%BF%E6%80%A7%E5%88%86%E7%B1%BB%E5%99%A8%EF%BC%8C%E8%BF%99%E6%97%B6%E5%80%99XGboost%E7%9B%B8%E5%BD%93%E4%BA%8E%E5%B8%A6L1%E5%92%8CL2%E6%AD%A3%E5%88%99%E5%8C%96%E7%9A%84%E9%80%BB%E8%BE%91%E5%9B%9E%E5%BD%92%E6%88%96%E8%80%85%E7%BA%BF%E6%80%A7%E5%9B%9E%E5%BD%92%E3%80%82%0A*%20gbdt%E5%9C%A8%E4%BC%98%E5%8C%96%E6%97%B6%E5%8F%AA%E7%94%A8%E5%88%B0%E4%B8%80%E9%98%B6%E5%AF%BC%E6%95%B0%E4%BF%A1%E6%81%AF%EF%BC%8C%E8%80%8CXGBoost%E5%88%A9%E7%94%A8%E7%89%9B%E9%A1%BF%E6%B3%95%E4%BC%98%E5%8C%96%EF%BC%8C%E5%AF%B9%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%E8%BF%9B%E8%A1%8C%E6%B3%B0%E5%8B%92%E4%BA%8C%E9%98%B6%E5%B1%95%E5%BC%80%EF%BC%8C%E4%BC%98%E5%8C%96%E6%97%B6%E7%94%A8%E5%88%B0%E4%BA%86%E4%B8%80%E9%98%B6%E5%92%8C%E4%BA%8C%E9%98%B6%E5%AF%BC%E6%95%B0%E4%BF%A1%E6%81%AF%E3%80%82%E4%B8%80%E9%98%B6%E5%AF%BC%E6%95%B0%E5%BC%95%E5%AF%BC%E6%A2%AF%E5%BA%A6%E6%96%B9%E5%90%91%EF%BC%8C%E4%BA%8C%E9%98%B6%E5%AF%BC%E6%95%B0%E5%BC%95%E5%AF%BC%E6%A2%AF%E5%BA%A6%E6%96%B9%E5%90%91%E5%A6%82%E4%BD%95%E5%8F%98%E5%8C%96%E3%80%82%E5%8F%AF%E4%BB%A5%E5%8A%A0%E5%BF%AB%E6%94%B6%E6%95%9B%E9%80%9F%E5%BA%A6%E3%80%82%0A*%20%E5%AF%B9%E6%A0%91%E7%9A%84%E7%BB%93%E6%9E%84%E8%BF%9B%E8%A1%8C%E4%BA%86**%E6%AD%A3%E5%88%99%E5%8C%96%E7%BA%A6%E6%9D%9F**%EF%BC%8C%E9%98%B2%E6%AD%A2%E6%A8%A1%E5%9E%8B%E8%BF%87%E5%BA%A6%E5%A4%8D%E6%9D%82%EF%BC%8C%E9%99%8D%E4%BD%8E%E4%BA%86%E8%BF%87%E6%8B%9F%E5%90%88%E7%9A%84%E5%8F%AF%E8%83%BD%E6%80%A7%E3%80%82**l1%E6%AD%A3%E5%88%99%E5%8C%96%E7%9B%B8%E5%BD%93%E4%BA%8E%E5%87%8F%E5%B0%91%E5%8F%B6%E5%AD%90%E7%BB%93%E7%82%B9%E4%B8%AA%E6%95%B0%EF%BC%8CL2%E6%AD%A3%E5%88%99%E5%8C%96%E7%9B%B8%E5%BD%93%E4%BA%8E%E9%99%90%E5%88%B6%E5%8F%B6%E5%AD%90%E8%8A%82%E7%82%B9%E7%9A%84%E8%BE%93%E5%87%BA%E5%80%BC%E3%80%82**%0A*%20GBDT%E6%B2%A1%E6%9C%89%E8%80%83%E8%99%91%E7%BC%BA%E5%A4%B1%E5%80%BC%EF%BC%8Cxgboost%E5%AF%B9%E7%BC%BA%E5%A4%B1%E5%80%BC%E8%BF%9B%E8%A1%8C%E4%BA%86%E5%A4%84%E7%90%86%E3%80%82%0A*%20%E7%BC%A9%E5%87%8F%EF%BC%8C%E7%9B%B8%E5%BD%93%E4%BA%8E%E5%AD%A6%E4%B9%A0%E9%80%9F%E7%8E%87%EF%BC%8C%E6%AF%8F%E6%AC%A1%E8%BF%AD%E4%BB%A3%E5%A2%9E%E5%8A%A0%E6%96%B0%E7%9A%84%E6%A8%A1%E5%9E%8B%E5%9C%A8%E5%89%8D%E9%9D%A2%E4%B9%98%E4%B8%8A%E4%B8%80%E4%B8%AA%E5%B0%8F%E4%BA%8E1%E7%9A%84%E7%B3%BB%E6%95%B0%EF%BC%8C%E9%99%8D%E4%BD%8E%E4%BC%98%E5%8C%96%E7%9A%84%E9%80%9F%E5%BA%A6%EF%BC%8C%E6%AF%8F%E6%AC%A1%E8%B5%B0%E4%B8%80%E5%B0%8F%E6%AD%A5%E9%80%90%E6%AD%A5%E9%80%BC%E8%BF%91%E6%9C%80%E4%BC%98%E6%A8%A1%E5%9E%8B%E6%AF%94%E6%AF%8F%E4%B8%80%E6%AC%A1%E8%B5%B0%E4%B8%80%E5%A4%A7%E6%AD%A5%E9%80%BC%E8%BF%91%E6%9B%B4%E5%AE%B9%E6%98%93%E9%81%BF%E5%85%8D%E8%BF%87%E6%8B%9F%E5%90%88%E3%80%82%0A!%5B624883a13e7cc5100c1b5a7c3bbe0c8d.png%5D(en-resource%3A%2F%2Fdatabase%2F2019%3A1)%0A*%20%E5%88%97%E6%8A%BD%E6%A0%B7%E3%80%82XGboost%E5%80%9F%E9%89%B4%E9%9A%8F%E6%9C%BA%E6%A3%AE%E6%9E%97%E7%9A%84%E5%81%9A%E6%B3%95%EF%BC%8C%E6%94%AF%E6%8C%81%E5%88%97%E6%8A%BD%E6%A0%B7%EF%BC%8C%E5%8D%B3%E6%AF%8F%E6%AC%A1%E7%9A%84%E8%BE%93%E5%85%A5%E7%89%B9%E5%BE%81%E4%B8%8D%E6%98%AF%E5%85%A8%E9%83%A8%E7%89%B9%E5%BE%81%EF%BC%8C%E4%B8%8D%E4%BB%85%E8%83%BD%E9%99%8D%E4%BD%8E%E8%BF%87%E6%8B%9F%E5%90%88%EF%BC%8C%E8%BF%98%E8%83%BD%E5%87%8F%E5%B0%91%E8%AE%A1%E7%AE%97%20%C2%A0%20%0A*%20XGBoost%E6%98%AFlevel-wise%E7%9A%84%E7%94%9F%E9%95%BF%E7%AD%96%E7%95%A5%EF%BC%8C%20%E5%8F%AF%E4%BB%A5%E5%90%8C%E6%97%B6%E5%88%86%E8%A3%82%E5%90%8C%E4%B8%80%E5%B1%82%E7%9A%84%E5%8F%B6%E5%AD%90%EF%BC%8C%E5%AE%B9%E6%98%93%E8%BF%9B%E8%A1%8C%E5%A4%9A%E7%BA%BF%E7%A8%8B%E4%BC%98%E5%8C%96%EF%BC%8C%E4%B9%9F%E5%A5%BD%E6%8E%A7%E5%88%B6%E6%A8%A1%E5%9E%8B%E5%A4%8D%E6%9D%82%E5%BA%A6%EF%BC%8C%E4%B8%8D%E5%AE%B9%E6%98%93%E8%BF%87%E6%8B%9F%E5%90%88%E3%80%82%C2%A0%20%0A*%20%E6%94%AF%E6%8C%81%E5%B9%B6%E8%A1%8C%E8%AE%A1%E7%AE%97%EF%BC%9A%E5%9C%A8**%E7%89%B9%E5%BE%81%E7%B2%92%E5%BA%A6%E4%B8%8A%E5%B9%B6%E8%A1%8C%EF%BC%8CXGboost%E5%9C%A8%E8%AE%A1%E7%AE%97%E6%9C%80%E4%BD%B3%E5%88%86%E8%A3%82%E5%B1%9E%E6%80%A7%E5%92%8C%E6%9C%80%E4%BD%B3%E5%88%87%E5%89%B2%E7%82%B9%E6%97%B6**%E5%8F%AF%E4%BB%A5%E8%B0%83%E7%94%A8CPU%E8%BF%9B%E8%A1%8C%E5%A4%9A%E7%BA%BF%E7%A8%8B%E7%9A%84%E5%B9%B6%E8%A1%8C%E8%AE%A1%E7%AE%97%EF%BC%8C%E6%9E%81%E5%A4%A7%E7%9A%84%E8%8A%82%E7%BA%A6%E4%BA%86%E6%97%B6%E9%97%B4%EF%BC%8C%E5%8A%A0%E9%80%9F%E4%BA%86%E8%AE%AD%E7%BB%83%E8%BF%87%E7%A8%8B%E3%80%82%0A%E7%BC%BA%E7%82%B9%EF%BC%9A%0A**%E5%86%B3%E7%AD%96%E6%A0%91%E7%9A%84%E5%AD%A6%E4%B9%A0%E6%9C%80%E8%80%97%E6%97%B6%E7%9A%84%E4%B8%80%E4%B8%AA%E6%AD%A5%E9%AA%A4%E5%B0%B1%E6%98%AF%E5%AF%B9%E7%89%B9%E5%BE%81%E7%9A%84%E5%80%BC%E8%BF%9B%E8%A1%8C%E6%8E%92%E5%BA%8F**%EF%BC%88%E5%9B%A0%E4%B8%BA%E8%A6%81%E7%A1%AE%E5%AE%9A%E6%9C%80%E4%BD%B3%E5%88%86%E5%89%B2%E7%82%B9%EF%BC%89%EF%BC%8Cxgboost%E5%9C%A8%E8%AE%AD%E7%BB%83%E4%B9%8B%E5%89%8D%EF%BC%8C%E9%A2%84%E5%85%88%E5%AF%B9%E6%95%B0%E6%8D%AE%E8%BF%9B%E8%A1%8C%E6%8E%92%E5%BA%8F%EF%BC%8C%E7%84%B6%E5%90%8E%E4%BF%9D%E5%AD%98%E4%B8%BAblock%E7%BB%93%E6%9E%84%EF%BC%8C%E5%90%8E%E9%9D%A2%E7%9A%84%E8%BF%AD%E4%BB%A3%E4%B8%AD%E9%87%8D%E5%A4%8D%E5%9C%B0%E4%BD%BF%E7%94%A8%E8%BF%99%E4%B8%AA%E7%BB%93%E6%9E%84%EF%BC%8C%E5%A4%A7%E5%A4%A7%E5%87%8F%E5%B0%8F%E8%AE%A1%E7%AE%97%E9%87%8F%E3%80%82%E8%BF%99%E4%B8%AAblock%E7%BB%93%E6%9E%84%E4%B9%9F%E4%BD%BF%E5%BE%97%E5%B9%B6%E8%A1%8C%E6%88%90%E4%B8%BA%E4%BA%86%E5%8F%AF%E8%83%BD%EF%BC%8C%E5%9C%A8%E8%BF%9B%E8%A1%8C%E8%8A%82%E7%82%B9%E5%88%86%E8%A3%82%E6%97%B6%EF%BC%8C%E9%9C%80%E8%A6%81%E8%AE%A1%E7%AE%97%E6%AF%8F%E4%B8%AA%E7%89%B9%E5%BE%81%E7%9A%84%E5%A2%9E%E7%9B%8A%EF%BC%8C%E6%9C%80%E7%BB%88%E9%80%89%E5%A2%9E%E7%9B%8A%E6%9C%80%E5%A4%A7%E7%9A%84%E9%82%A3%E4%B8%AA%E7%89%B9%E5%BE%81%E5%8E%BB%E5%81%9A%E5%88%86%E8%A3%82%EF%BC%8C%E9%82%A3%E4%B9%88%E5%90%84%E4%B8%AA%E7%89%B9%E5%BE%81%E7%9A%84%E5%A2%9E%E7%9B%8A%E8%AE%A1%E7%AE%97%E5%B0%B1%E5%8F%AF%E4%BB%A5%E5%BC%80%E5%A4%9A%E7%BA%BF%E7%A8%8B%E8%BF%9B%E8%A1%8C%E3%80%82%0AXgboost%E4%BD%BF%E7%94%A8%E4%BA%86%E5%A4%9A%E4%B8%AABlock%EF%BC%8C%E5%AD%98%E5%9C%A8%E5%A4%9A%E4%B8%AA%E6%9C%BA%E5%99%A8%E4%B8%8A%E6%88%96%E8%80%85%E7%A3%81%E7%9B%98%E4%B8%AD%E3%80%82%E6%AF%8F%E4%B8%AABlock%E5%AF%B9%E5%BA%94%E5%8E%9F%E6%9D%A5%E6%95%B0%E6%8D%AE%E7%9A%84%E5%AD%90%E9%9B%86%E3%80%82%E4%B8%8D%E5%90%8C%E7%9A%84Block%E5%8F%AF%E4%BB%A5%E5%9C%A8%E4%B8%8D%E5%90%8C%E7%9A%84%E6%9C%BA%E5%99%A8%E4%B8%8A%E8%AE%A1%E7%AE%97%E3%80%82%0A%0A!%5Bbfa8da2887ffe1dbc3289629f18757c4.png%5D(en-resource%3A%2F%2Fdatabase%2F2024%3A1)%0A!%5Bb1813dcf9133802a04ef224f2f3ae3b1.png%5D(en-resource%3A%2F%2Fdatabase%2F2023%3A1)%0A!%5Bec8e861d887160b545e6b9c7799f6c25.png%5D(en-resource%3A%2F%2Fdatabase%2F2029%3A1)%0A%0A**XGBoost%E5%A6%82%E4%BD%95%E9%80%89%E6%8B%A9%E7%89%B9%E5%BE%81%EF%BC%9A**%0A*%20%E8%AF%A5%E7%89%B9%E5%BE%81%E4%BD%9C%E4%B8%BA%E5%88%86%E8%A3%82%E7%89%B9%E5%BE%81%E5%9C%A8%E6%89%80%E6%9C%89%E6%A0%91%E4%B8%AD%E5%87%BA%E7%8E%B0%E7%9A%84%E6%AC%A1%E6%95%B0%0A*%20%E8%AF%A5%E7%89%B9%E5%BE%81%E4%BD%9C%E4%B8%BA%E5%88%86%E8%A3%82%E7%89%B9%E5%BE%81%E7%9A%84%E5%A2%9E%E7%9B%8A%0A*%20%E8%AF%A5%E7%89%B9%E5%BE%81%E4%BD%9C%E4%B8%BA%E5%88%86%E8%A3%82%E7%89%B9%E5%BE%81%E5%AF%B9%E6%A0%B7%E6%9C%AC%E7%9A%84%E8%A6%86%E7%9B%96%0A%0A**XGBoost%E8%8A%82%E7%82%B9%E5%88%86%E8%A3%82%E6%96%B9%E5%BC%8F%EF%BC%9A**%0Alevel-wise%EF%BC%8C%E5%90%8C%E6%97%B6%E5%88%86%E8%A3%82%E5%90%8C%E4%B8%80%E5%B1%82%E7%9A%84%E5%8F%B6%E5%AD%90%E8%8A%82%E7%82%B9%0A%0A**%E7%AE%80%E4%BB%8BLightGBM%EF%BC%9F**%C2%A0%C2%A0%20%C2%A0%C2%A0%C2%A0%20%0AGBDT%E5%9C%A8%E6%AF%8F%E4%B8%80%E6%AC%A1%E8%BF%AD%E4%BB%A3%E7%9A%84%E6%97%B6%E5%80%99%EF%BC%8C%E9%83%BD%E9%9C%80%E8%A6%81%E9%81%8D%E5%8E%86%E6%95%B4%E4%B8%AA%E8%AE%AD%E7%BB%83%E6%95%B0%E6%8D%AE%E5%A4%9A%E6%AC%A1%E3%80%82%E5%A6%82%E6%9E%9C%E6%8A%8A%E6%95%B4%E4%B8%AA%E8%AE%AD%E7%BB%83%E6%95%B0%E6%8D%AE%E8%A3%85%E8%BF%9B%E5%86%85%E5%AD%98%E5%88%99%E4%BC%9A%E9%99%90%E5%88%B6%E8%AE%AD%E7%BB%83%E6%95%B0%E6%8D%AE%E7%9A%84%E5%A4%A7%E5%B0%8F%EF%BC%9B%E5%A6%82%E6%9E%9C%E4%B8%8D%E8%A3%85%E8%BF%9B%E5%86%85%E5%AD%98%EF%BC%8C%E5%8F%8D%E5%A4%8D%E5%9C%B0%E8%AF%BB%E5%86%99%E8%AE%AD%E7%BB%83%E6%95%B0%E6%8D%AE%E5%8F%88%E4%BC%9A%E6%B6%88%E8%80%97%E9%9D%9E%E5%B8%B8%E5%A4%A7%E7%9A%84%E6%97%B6%E9%97%B4%E3%80%82LightGBM%E6%98%AF%E4%B8%80%E4%B8%AA%E5%AE%9E%E7%8E%B0%20GBDT%E7%AE%97%E6%B3%95%E7%9A%84%E6%A1%86%E6%9E%B6%EF%BC%8C**LightGBM%E6%8F%90%E5%87%BA%E7%9A%84%E4%B8%BB%E8%A6%81%E5%8E%9F%E5%9B%A0%E6%98%AF%E4%B8%BA%E4%BA%86%E8%A7%A3%E5%86%B3GBDT%E5%9C%A8%E6%B5%B7%E9%87%8F%E6%95%B0%E6%8D%AE%E9%81%87%E5%88%B0%E7%9A%84%E9%97%AE%E9%A2%98**%E3%80%82%C2%A0%C2%A0%20%0AXgboost%E4%B8%8D%E8%B6%B3%EF%BC%9A%E6%A0%91%E8%8A%82%E7%82%B9%E5%9C%A8%E8%BF%9B%E8%A1%8C%E5%88%86%E8%A3%82%E6%97%B6%EF%BC%8C%E6%88%91%E4%BB%AC%E9%9C%80%E8%A6%81%E8%AE%A1%E7%AE%97%E6%AF%8F%E4%B8%AA%E7%89%B9%E5%BE%81%E7%9A%84%E6%AF%8F%E4%B8%AA%E5%88%86%E5%89%B2%E7%82%B9%E5%AF%B9%E5%BA%94%E7%9A%84%E5%A2%9E%E7%9B%8A%EF%BC%8C%E5%8D%B3%E7%94%A8%E8%B4%AA%E5%BF%83%E6%B3%95%E6%9E%9A%E4%B8%BE%E6%89%80%E6%9C%89%E5%8F%AF%E8%83%BD%E7%9A%84%E5%88%86%E5%89%B2%E7%82%B9%E3%80%82%E5%BD%93%E6%95%B0%E6%8D%AE%E6%97%A0%E6%B3%95%E4%B8%80%E6%AC%A1%E8%BD%BD%E5%85%A5%E5%86%85%E5%AD%98%E6%88%96%E8%80%85%E5%9C%A8%E5%88%86%E5%B8%83%E5%BC%8F%E6%83%85%E5%86%B5%E4%B8%8B%EF%BC%8C%E8%B4%AA%E5%BF%83%E7%AE%97%E6%B3%95%E6%95%88%E7%8E%87%E5%B0%B1%E4%BC%9A%E5%8F%98%E5%BE%97%E5%BE%88%E4%BD%8E%E3%80%82%0A**LightGBM%E9%92%88%E5%AF%B9XGBoost%E5%81%9A%E4%BA%86%E4%BB%A5%E4%B8%8B%E4%BC%98%E5%8C%96%EF%BC%9A**%0A*%20%E5%9F%BA%E4%BA%8E%E7%9B%B4%E6%96%B9%E5%9B%BEHistogram%E7%9A%84%E5%86%B3%E7%AD%96%E6%A0%91%E7%AE%97%E6%B3%95%EF%BC%9A%0A%E5%88%86%E6%A1%B6%E6%80%9D%E6%83%B3%EF%BC%8C%E6%8A%8A%E8%BF%9E%E7%BB%AD%E7%89%B9%E5%BE%81%E5%80%BC%E7%A6%BB%E6%95%A3%E5%8C%96%E6%88%90k%E4%B8%AA%E6%95%B4%E6%95%B0%EF%BC%8C%E5%B0%86%E7%89%B9%E5%BE%81%E7%9A%84%E4%B8%AA%E6%95%B0%E9%99%8D%E4%B8%BAk%EF%BC%8C%E8%80%8C%E8%BF%99%E4%B8%AA%E5%80%BC%E4%B8%80%E8%88%AC%E7%94%A8%208%20%E4%BD%8D%E6%95%B4%E5%9E%8B%E5%AD%98%E5%82%A8%E5%B0%B1%E8%B6%B3%E5%A4%9F%E4%BA%86%EF%BC%8C%E5%86%85%E5%AD%98%E6%B6%88%E8%80%97%E5%8F%AF%E4%BB%A5%E9%99%8D%E4%BD%8E%E4%B8%BA%E5%8E%9F%E6%9D%A5%E7%9A%841%2F8%0A*%20%E5%B8%A6%E6%B7%B1%E5%BA%A6%E9%99%90%E5%88%B6%E7%9A%84Leaf-wise%E7%9A%84%E5%8F%B6%E5%AD%90%E7%94%9F%E9%95%BF%E7%AD%96%E7%95%A5%3A%C2%A0%E5%9C%A8%20Histogram%20%E7%AE%97%E6%B3%95%E4%B9%8B%E4%B8%8A%EF%BC%8CLightGBM%20%E8%BF%9B%E8%A1%8C%E8%BF%9B%E4%B8%80%E6%AD%A5%E7%9A%84%E4%BC%98%E5%8C%96%E3%80%82%E9%A6%96%E5%85%88%E5%AE%83%E6%8A%9B%E5%BC%83%E4%BA%86%E5%A4%A7%E5%A4%9A%E6%95%B0%20GBDT%20%E5%B7%A5%E5%85%B7%E4%BD%BF%E7%94%A8%E7%9A%84%E6%8C%89%E5%B1%82%E7%94%9F%E9%95%BF%20(level-wise)%20%E7%9A%84%E5%86%B3%E7%AD%96%E6%A0%91%E7%94%9F%E9%95%BF%E7%AD%96%E7%95%A5%EF%BC%8C%E8%80%8C%E4%BD%BF%E7%94%A8%E4%BA%86%E5%B8%A6%E6%9C%89%E6%B7%B1%E5%BA%A6%E9%99%90%E5%88%B6%E7%9A%84%E6%8C%89%E5%8F%B6%E5%AD%90%E7%94%9F%E9%95%BF%20(leaf-wise)%20%E7%AE%97%E6%B3%95%E3%80%82%20%20Leaf-wise%20%E7%9A%84%E7%BC%BA%E7%82%B9%E6%98%AF%E5%8F%AF%E8%83%BD%E4%BC%9A%E9%95%BF%E5%87%BA%E6%AF%94%E8%BE%83%E6%B7%B1%E7%9A%84%E5%86%B3%E7%AD%96%E6%A0%91%EF%BC%8C%E4%BA%A7%E7%94%9F%E8%BF%87%E6%8B%9F%E5%90%88%E3%80%82%E5%9B%A0%E6%AD%A4%20LightGBM%20%E5%9C%A8%20Leaf-wise%20%E4%B9%8B%E4%B8%8A%E5%A2%9E%E5%8A%A0%E4%BA%86%E4%B8%80%E4%B8%AA%E6%9C%80%E5%A4%A7%E6%B7%B1%E5%BA%A6%E7%9A%84%E9%99%90%E5%88%B6%EF%BC%8C%E5%9C%A8%E4%BF%9D%E8%AF%81%E9%AB%98%E6%95%88%E7%8E%87%E7%9A%84%E5%90%8C%E6%97%B6%E9%98%B2%E6%AD%A2%E8%BF%87%E6%8B%9F%E5%90%88%E3%80%82%0Alevel-wise%EF%BC%9A%E9%81%8D%E5%8E%86%E4%B8%80%E6%AC%A1%E6%95%B0%E6%8D%AE%E5%8F%AF%E4%BB%A5**%E5%90%8C%E6%97%B6%E5%88%86%E8%A3%82%E5%90%8C%E4%B8%80%E5%B1%82%E7%9A%84%E5%8F%B6%E5%AD%90**%EF%BC%8C%E5%AE%B9%E6%98%93%E8%BF%9B%E8%A1%8C%E5%A4%9A%E7%BA%BF%E7%A8%8B%E4%BC%98%E5%8C%96%EF%BC%8C%E4%B9%9F%E5%A5%BD%E6%8E%A7%E5%88%B6%E6%A8%A1%E5%9E%8B%E5%A4%8D%E6%9D%82%E5%BA%A6%EF%BC%8C%E4%B8%8D%E5%AE%B9%E6%98%93%E8%BF%87%E6%8B%9F%E5%90%88%E3%80%82%E4%BD%86Level-wise%20%E6%98%AF%E4%B8%80%E7%A7%8D%E4%BD%8E%E6%95%88%E7%9A%84%E7%AE%97%E6%B3%95%EF%BC%8C%E5%9B%A0%E4%B8%BA%E5%AE%83%E4%B8%8D%E5%8A%A0%E5%8C%BA%E5%88%86%E7%9A%84%E5%AF%B9%E5%BE%85%E5%90%8C%E4%B8%80%E5%B1%82%E7%9A%84%E5%8F%B6%E5%AD%90%EF%BC%8C%E5%B8%A6%E6%9D%A5%E4%BA%86%E5%BE%88%E5%A4%9A%E6%B2%A1%E5%BF%85%E8%A6%81%E7%9A%84%E5%BC%80%E9%94%80%EF%BC%8C%E5%9B%A0%E4%B8%BA%E5%AE%9E%E9%99%85%E4%B8%8A%E5%BE%88%E5%A4%9A%E5%8F%B6%E5%AD%90%E7%9A%84%E5%88%86%E8%A3%82%E5%A2%9E%E7%9B%8A%E8%BE%83%E4%BD%8E%EF%BC%8C%E6%B2%A1%E5%BF%85%E8%A6%81%E8%BF%9B%E8%A1%8C%E6%90%9C%E7%B4%A2%E5%92%8C%E5%88%86%E8%A3%82%E3%80%82%0Aleaf-wise%EF%BC%9A%E4%BB%8E%E5%BD%93%E5%89%8D%E6%89%80%E6%9C%89%E5%8F%B6%E5%AD%90%E4%B8%AD%EF%BC%8C**%E6%89%BE%E5%88%B0%E5%88%86%E8%A3%82%E5%A2%9E%E7%9B%8A%E6%9C%80%E5%A4%A7%E7%9A%84%E4%B8%80%E4%B8%AA%E5%8F%B6%E5%AD%90**%EF%BC%8C%E7%84%B6%E5%90%8E%E5%88%86%E8%A3%82%EF%BC%8C%E5%A6%82%E6%AD%A4%E5%BE%AA%E7%8E%AF%E3%80%82%0A*%20%E7%9B%B4%E6%96%B9%E5%9B%BE%E5%81%9A%E5%B7%AE%E5%8A%A0%E9%80%9F%E7%9B%B4%E6%8E%A5%E6%94%AF%E6%8C%81%E7%B1%BB%E5%88%AB%E7%89%B9%E5%BE%81%EF%BC%9A%20LightGBM%20%E4%BC%98%E5%8C%96%E4%BA%86%E5%AF%B9%E7%B1%BB%E5%88%AB%E7%89%B9%E5%BE%81%E7%9A%84%E6%94%AF%E6%8C%81%EF%BC%8C**%E5%8F%AF%E4%BB%A5%E7%9B%B4%E6%8E%A5%E8%BE%93%E5%85%A5%E7%B1%BB%E5%88%AB%E7%89%B9%E5%BE%81**%EF%BC%8C%E4%B8%8D%E9%9C%80%E8%A6%81%E9%A2%9D%E5%A4%96%E7%9A%840%2F1%20%E5%B1%95%E5%BC%80%E3%80%82Cache%E5%91%BD%E4%B8%AD%E7%8E%87%E4%BC%98%E5%8C%96%E5%9F%BA%E4%BA%8E%E7%9B%B4%E6%96%B9%E5%9B%BE%E7%9A%84%E7%A8%80%E7%96%8F%E7%89%B9%E5%BE%81%E4%BC%98%E5%8C%96%E5%A4%9A%E7%BA%BF%E7%A8%8B%E4%BC%98%E5%8C%96%C2%A0%20%C2%A0%20%0A*%20LightGBM%20%E5%8E%9F%E7%94%9F%E6%94%AF%E6%8C%81%E5%B9%B6%E8%A1%8C%E5%AD%A6%E4%B9%A0%EF%BC%8C%E7%9B%AE%E5%89%8D%E6%94%AF%E6%8C%81%E7%89%B9%E5%BE%81%E5%B9%B6%E8%A1%8C%E5%92%8C%E6%95%B0%E6%8D%AE%E5%B9%B6%E8%A1%8C%E7%9A%84%E4%B8%A4%E7%A7%8D%E3%80%82**%E7%89%B9%E5%BE%81%E5%B9%B6%E8%A1%8C%E7%9A%84%E4%B8%BB%E8%A6%81%E6%80%9D%E6%83%B3%E6%98%AF%E5%9C%A8%E4%B8%8D%E5%90%8C%E6%9C%BA%E5%99%A8%E5%9C%A8%E4%B8%8D%E5%90%8C%E7%9A%84%E7%89%B9%E5%BE%81%E9%9B%86%E5%90%88%E4%B8%8A%E5%88%86%E5%88%AB%E5%AF%BB%E6%89%BE%E6%9C%80%E4%BC%98%E7%9A%84%E5%88%86%E5%89%B2%E7%82%B9**%EF%BC%8C%E7%84%B6%E5%90%8E%E5%9C%A8%E6%9C%BA%E5%99%A8%E9%97%B4%E5%90%8C%E6%AD%A5%E6%9C%80%E4%BC%98%E7%9A%84%E5%88%86%E5%89%B2%E7%82%B9%E3%80%82%E6%95%B0%E6%8D%AE%E5%B9%B6%E8%A1%8C%E5%88%99%E6%98%AF%E8%AE%A9%E4%B8%8D%E5%90%8C%E7%9A%84%E6%9C%BA%E5%99%A8%E5%85%88%E5%9C%A8%E6%9C%AC%E5%9C%B0%E6%9E%84%E9%80%A0%E7%9B%B4%E6%96%B9%E5%9B%BE%EF%BC%8C%E7%84%B6%E5%90%8E%E8%BF%9B%E8%A1%8C%E5%85%A8%E5%B1%80%E7%9A%84%E5%90%88%E5%B9%B6%EF%BC%8C%E6%9C%80%E5%90%8E%E5%9C%A8%E5%90%88%E5%B9%B6%E7%9A%84%E7%9B%B4%E6%96%B9%E5%9B%BE%E4%B8%8A%E9%9D%A2%E5%AF%BB%E6%89%BE%E6%9C%80%E4%BC%98%E5%88%86%E5%89%B2%E7%82%B9%E3%80%82%0A%0A**%E4%BC%A0%E7%BB%9F%E7%9A%84%E7%89%B9%E5%BE%81%E5%B9%B6%E8%A1%8C%E7%AE%97%E6%B3%95**%0A%0A%E6%A0%B9%E6%8D%AE%E4%B8%8D%E5%90%8C%E7%9A%84%E7%89%B9%E5%BE%81%E5%AD%90%E9%9B%86%EF%BC%8C%E5%B0%86%E6%95%B0%E6%8D%AE%E9%9B%86%E8%BF%9B%E8%A1%8C%E5%9E%82%E7%9B%B4%E5%88%87%E5%88%86%E3%80%82%EF%BC%88%E4%B8%8D%E5%90%8C%E6%9C%BA%E5%99%A8worker%E6%9C%89%E4%B8%8D%E5%90%8C%E7%9A%84%E7%89%B9%E5%BE%81%E5%AD%90%E9%9B%86%EF%BC%89%0A%E6%AF%8F%E4%B8%AAworker%E5%AF%BB%E6%89%BE%E5%B1%80%E9%83%A8%E7%9A%84%E6%9C%80%E4%BC%98%E5%88%86%E8%A3%82%E7%89%B9%E5%BE%81%E4%BB%A5%E5%8F%8A%E5%88%86%E8%A3%82%E7%82%B9%E3%80%82%0A%E4%B8%8D%E5%90%8Cworker%E4%B9%8B%E9%97%B4%E8%BF%9B%E8%A1%8C%E7%BD%91%E7%BB%9C%E4%BC%A0%E8%BE%93%EF%BC%8C%E4%BA%A4%E6%8D%A2%E6%9C%80%E4%BC%98%E5%88%86%E8%A3%82%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%9C%80%E7%BB%88%E5%BE%97%E5%88%B0%E6%9C%80%E4%BC%98%E7%9A%84%E5%88%86%E8%A3%82%E4%BF%A1%E6%81%AF%E3%80%82%0A%E5%85%B7%E6%9C%89%E6%9C%80%E4%BC%98%E5%88%86%E8%A3%82%E7%89%B9%E5%BE%81%E7%9A%84worker%EF%BC%8C%E5%B1%80%E9%83%A8%E8%BF%9B%E8%A1%8C%E5%88%86%E8%A3%82%EF%BC%8C%E5%B9%B6%E5%B0%86%E5%88%86%E8%A3%82%E7%BB%93%E6%9E%9C%E5%B9%BF%E6%92%AD%E5%88%B0%E5%85%B6%E4%BB%96worker%E3%80%82%0A%E5%85%B6%E4%BB%96worker%E6%A0%B9%E6%8D%AE%E6%8E%A5%E6%94%B6%E5%88%B0%E7%9A%84%E6%95%B0%E6%8D%AE%E8%BF%9B%E8%A1%8C%E5%88%87%E5%88%86%E6%95%B0%E6%8D%AE%E3%80%82%0A%E8%AF%A5%E6%96%B9%E6%B3%95%E4%B8%8D%E8%83%BD%E6%9C%89%E6%95%88%E5%9C%B0%E5%8A%A0%E9%80%9F%E7%89%B9%E5%BE%81%E9%80%89%E6%8B%A9%E7%9A%84%E6%95%88%E7%8E%87%EF%BC%8C%E5%BD%93%E6%95%B0%E6%8D%AE%E9%87%8F%23data%E5%BE%88%E5%A4%A7%E6%97%B6%EF%BC%8C%E8%AF%A5%E5%B9%B6%E8%A1%8C%E6%96%B9%E6%B3%95%E4%B8%8D%E8%83%BD%E5%8A%A0%E5%BF%AB%E6%95%88%E7%8E%87%E3%80%82%E5%B9%B6%E4%B8%94%EF%BC%8C%E6%9C%80%E4%BC%98%E7%9A%84%E5%88%86%E8%A3%82%E7%BB%93%E6%9E%9C%E9%9C%80%E8%A6%81%E5%9C%A8worker%E4%B9%8B%E9%97%B4%E8%BF%9B%E8%A1%8C%E4%BC%A0%E8%BE%93%EF%BC%8C%E9%9C%80%E8%A6%81%E6%B6%88%E8%80%97%E5%BE%88%E5%A4%9A%E7%9A%84%E4%BC%A0%E8%BE%93%E8%B5%84%E6%BA%90%E4%BB%A5%E5%8F%8A%E4%BC%A0%E8%BE%93%E6%97%B6%E9%97%B4%E3%80%82%0A%0A**LightGBM%E7%9A%84%E7%89%B9%E5%BE%81%E5%B9%B6%E8%A1%8C%E7%AE%97%E6%B3%95**%0ALightGBM%E5%B9%B6%E6%B2%A1%E6%9C%89%E5%9E%82%E7%9B%B4%E7%9A%84%E5%88%87%E5%88%86%E6%95%B0%E6%8D%AE%E9%9B%86%EF%BC%8C%E8%80%8C%E6%98%AF%E6%AF%8F%E4%B8%AAworker%E9%83%BD%E6%9C%89%E5%85%A8%E9%87%8F%E7%9A%84%E8%AE%AD%E7%BB%83%E6%95%B0%E6%8D%AE%EF%BC%8C%E5%9B%A0%E6%AD%A4%E6%9C%80%E4%BC%98%E7%9A%84%E7%89%B9%E5%BE%81%E5%88%86%E8%A3%82%E7%BB%93%E6%9E%9C%E4%B8%8D%E9%9C%80%E8%A6%81%E4%BC%A0%E8%BE%93%E5%88%B0%E5%85%B6%E4%BB%96worker%E4%B8%AD%EF%BC%8C%E5%8F%AA%E9%9C%80%E8%A6%81%E5%B0%86%E6%9C%80%E4%BC%98%E7%89%B9%E5%BE%81%E4%BB%A5%E5%8F%8A%E5%88%86%E8%A3%82%E7%82%B9%E5%91%8A%E8%AF%89%E5%85%B6%E4%BB%96worker%EF%BC%8Cworker%E9%9A%8F%E5%90%8E%E6%9C%AC%E5%9C%B0%E8%87%AA%E5%B7%B1%E8%BF%9B%E8%A1%8C%E5%A4%84%E7%90%86%E3%80%82%E5%A4%84%E7%90%86%E8%BF%87%E7%A8%8B%E5%A6%82%E4%B8%8B%EF%BC%9A%0A%0A%E6%AF%8F%E4%B8%AAworker%E5%9C%A8**%E5%9F%BA%E4%BA%8E%E5%B1%80%E9%83%A8%E7%9A%84%E7%89%B9%E5%BE%81%E9%9B%86%E5%90%88**%E6%89%BE%E5%88%B0%E6%9C%80%E4%BC%98%E5%88%86%E8%A3%82%E7%89%B9%E5%BE%81%E3%80%82%0Aworkder%E9%97%B4%E4%BC%A0%E8%BE%93%E6%9C%80%E4%BC%98%E5%88%86%E8%A3%82%E4%BF%A1%E6%81%AF%EF%BC%8C%E5%B9%B6%E5%BE%97%E5%88%B0%E5%85%A8%E5%B1%80%E6%9C%80%E4%BC%98%E5%88%86%E8%A3%82%E4%BF%A1%E6%81%AF%E3%80%82%0A%E6%AF%8F%E4%B8%AAworker%E5%9F%BA%E4%BA%8E%E5%85%A8%E5%B1%80%E6%9C%80%E4%BC%98%E5%88%86%E8%A3%82%E4%BF%A1%E6%81%AF%EF%BC%8C%E5%9C%A8%E6%9C%AC%E5%9C%B0%E8%BF%9B%E8%A1%8C%E6%95%B0%E6%8D%AE%E5%88%86%E8%A3%82%EF%BC%8C%E7%94%9F%E6%88%90%E5%86%B3%E7%AD%96%E6%A0%91%E3%80%82%0A%E7%84%B6%E8%80%8C%EF%BC%8C%E5%BD%93%E6%95%B0%E6%8D%AE%E9%87%8F%E5%BE%88%E5%A4%A7%E6%97%B6%EF%BC%8C%E7%89%B9%E5%BE%81%E5%B9%B6%E8%A1%8C%E7%AE%97%E6%B3%95%E8%BF%98%E6%98%AF%E5%8F%97%E9%99%90%E4%BA%8E%E7%89%B9%E5%BE%81%E5%88%86%E8%A3%82%E6%95%88%E7%8E%87%E3%80%82%E5%9B%A0%E6%AD%A4%EF%BC%8C%E5%BD%93%E6%95%B0%E6%8D%AE%E9%87%8F%E5%A4%A7%E6%97%B6%EF%BC%8C%E6%8E%A8%E8%8D%90%E4%BD%BF%E7%94%A8%E6%95%B0%E6%8D%AE%E5%B9%B6%E8%A1%8C%E7%AE%97%E6%B3%95%E3%80%82%0A%0A**Xgboost%2FGBDT%E5%9C%A8%E8%B0%83%E5%8F%82%E6%97%B6%E4%B8%BA%E4%BB%80%E4%B9%88%E6%A0%91%E7%9A%84%E6%B7%B1%E5%BA%A6%E5%BE%88%E5%B0%91%E6%97%B6%E5%B0%B1%E8%83%BD%E8%BE%BE%E5%88%B0%E5%BE%88%E9%AB%98%E7%9A%84%E7%B2%BE%E5%BA%A6%EF%BC%9F**%0A%E4%BB%96%E4%BB%AC%E4%BF%A9%E9%83%BD%E5%B1%9E%E4%BA%8EBoosting%2CBoosting%E6%AF%8F%E4%B8%80%E6%AD%A5%E9%83%BD%E5%9C%A8%E4%B8%8A%E4%B8%80%E8%BD%AE%E7%9A%84%E5%9F%BA%E7%A1%80%E4%B8%8A%E6%9B%B4%E5%8A%A0%E6%8B%9F%E5%90%88%E5%8E%9F%E6%95%B0%E6%8D%AE%EF%BC%8C%E6%89%80%E4%BB%A5Bossting%E6%96%B9%E6%B3%95%E5%8F%AF%E4%BB%A5%E4%BF%9D%E8%AF%81%E5%81%8F%E5%B7%AE%E5%B0%8F%EF%BC%8C%E5%9B%A0%E6%AD%A4%E5%AF%B9%E4%BA%8E%E6%AF%8F%E4%B8%AA%E5%9F%BA%E5%88%86%E7%B1%BB%E5%99%A8%E6%9D%A5%E8%AF%B4%E9%87%8D%E7%82%B9%E5%B0%B1%E5%9C%A8%E4%BA%8E%E9%80%89%E6%8B%A9%E6%96%B9%E5%B7%AE%E6%9B%B4%E5%B0%8F%E7%9A%84%E5%88%86%E7%B1%BB%E5%99%A8%EF%BC%8C%E5%8D%B3%E6%9B%B4%E7%AE%80%E5%8D%95%E7%9A%84%E5%88%86%E7%B1%BB%E5%99%A8%EF%BC%8C%E6%89%80%E4%BB%A5%E4%BC%9A%E9%80%89%E6%8B%A9%E6%B7%B1%E5%BA%A6%E5%BE%88%E6%B5%85%E7%9A%84%E5%86%B3%E7%AD%96%E6%A0%91%E3%80%82%0A**XGBoost%E5%A6%82%E4%BD%95%E5%A4%84%E7%90%86%E6%95%B0%E6%8D%AE%E4%B8%8D%E5%B9%B3%E8%A1%A1%E9%97%AE%E9%A2%98%EF%BC%9F**%0A%E6%9D%83%E9%87%8D%E8%B0%83%E6%95%B4%EF%BC%9A%0A%E5%B0%8F%E6%A0%B7%E6%9C%AC%E9%87%8F%E7%B1%BB%E5%88%AB%E6%9D%83%E9%87%8D%E9%AB%98%EF%BC%8C%E5%A4%A7%E6%A0%B7%E6%9C%AC%E9%87%8F%E7%B1%BB%E5%88%AB%E6%9D%83%E9%87%8D%E4%BD%8E%0Afocal%20loss%3A%E8%A7%A3%E5%86%B3%E9%9A%BE%E6%98%93%E6%A0%B7%E6%9C%AC%E6%95%B0%E9%87%8F%E4%B8%8D%E5%B9%B3%E8%A1%A1%E9%97%AE%E9%A2%98%0A%0A**%E6%AF%94%E8%BE%83LR%E5%92%8CGBDT%EF%BC%8C%E4%BB%80%E4%B9%88%E6%83%85%E5%A2%83%E4%B8%8BLR%E5%A5%BD%E4%BA%8EGBDT%EF%BC%9F**%0A%EF%BC%881%EF%BC%89%E6%AF%94%E8%BE%83%0A*%20LR%E6%98%AF%E7%BA%BF%E6%80%A7%E6%A8%A1%E5%9E%8B%EF%BC%8CGBDT%E6%98%AF%E9%9D%9E%E7%BA%BF%E6%80%A7%E6%A8%A1%E5%9E%8B%EF%BC%8C%E5%9B%A0%E6%AD%A4%E4%B8%BA%E4%BA%86%E5%A2%9E%E5%BC%BA%E6%A8%A1%E5%9E%8B%E7%9A%84%E9%9D%9E%E7%BA%BF%E6%80%A7%E8%A1%A8%E8%BE%BE%E8%83%BD%E5%8A%9B%EF%BC%8C%E4%BD%BF%E7%94%A8LR%E6%A8%A1%E5%9E%8B%E4%B9%8B%E5%89%8D%E4%BC%9A%E6%9C%89%E9%9D%9E%E5%B8%B8%E7%B9%81%E9%87%8D%E7%9A%84%E7%89%B9%E5%BE%81%E5%B7%A5%E7%A8%8B%E4%BB%BB%E5%8A%A1%0A*%20LR%E6%98%AF%E5%8D%95%E6%A8%A1%EF%BC%8CGBDT%E6%98%AF%E9%9B%86%E6%88%90%E6%A8%A1%E5%9E%8B%EF%BC%8C%E5%9C%A8%E6%95%B0%E6%8D%AE%E4%BD%8E%E5%99%AA%E7%9A%84%E6%83%85%E5%86%B5%E4%B8%8B%EF%BC%8CGBDT%E7%9A%84%E6%95%88%E6%9E%9C%E4%BC%9A%E4%BC%98%E4%BA%8ELR%0A*%20LR%E4%BD%BF%E7%94%A8%E6%A2%AF%E5%BA%A6%E4%B8%8B%E9%99%8D%E6%B3%95%E8%BF%9B%E8%A1%8C%E8%AE%AD%E7%BB%83%EF%BC%8C%E9%9C%80%E8%A6%81%E5%AF%B9%E7%89%B9%E5%BE%81%E8%BF%9B%E8%A1%8C%E5%BD%92%E4%B8%80%E5%8C%96%EF%BC%8C%E8%80%8CGBDT%E5%9F%BA%E4%BA%8E%E5%9F%BA%E5%B0%BC%E7%B3%BB%E6%95%B0%E9%80%89%E6%8B%A9%E7%89%B9%E5%BE%81%EF%BC%8C%E4%B8%8D%E9%9C%80%E8%A6%81%E5%81%9A%E7%89%B9%E5%BE%81%E5%BD%92%E4%B8%80%E5%8C%96%0A%EF%BC%882%EF%BC%89%E4%BC%98%E7%82%B9%0A*%20%E5%BD%93%E9%9C%80%E8%A6%81%E5%AF%B9%E6%A8%A1%E5%9E%8B%E8%BF%9B%E8%A1%8C%E8%A7%A3%E9%87%8A%E7%9A%84%E6%97%B6%E5%80%99%EF%BC%8CGBDT%E6%AF%94LR%E6%9B%B4%E5%8A%A0%E9%BB%91%E7%9B%92%EF%BC%8C%E5%9B%A0%E4%B8%BA%E6%88%91%E4%BB%AC%E4%B8%8D%E8%83%BD%E5%8E%BB%E8%A7%A3%E9%87%8A%E6%AF%8F%E4%B8%80%E6%A3%B5%E6%A0%91%E3%80%82%E7%9B%B8%E6%AF%94%E4%B9%8B%E4%B8%8B%EF%BC%8CLR%E7%9A%84%E7%89%B9%E5%BE%81%E6%9D%83%E9%87%8D%E8%83%BD%E5%A4%9F%E5%BE%88%E7%9B%B4%E8%A7%82%E5%9C%B0%E5%8F%8D%E6%98%A0%E5%87%BA%E7%89%B9%E5%BE%81%E5%AF%B9%E4%B8%8D%E5%90%8C%E7%B1%BB%E6%A0%B7%E6%9C%AC%E7%9A%84%E8%B4%A1%E7%8C%AE%E7%A8%8B%E5%BA%A6%0A*%20LR%E5%8F%AF%E4%BB%A5%E5%B9%B6%E8%A1%8C%E5%8C%96%E5%A4%84%E7%90%86%EF%BC%8CGBDT%E6%98%AF%E4%B8%B2%E8%A1%8C%E5%BE%88%E9%9A%BE%E5%B9%B6%E8%A1%8C%E5%A4%84%E7%90%86%0A*%20%E5%AF%B9%E4%BA%8E%E9%AB%98%E7%BB%B4%E7%A8%80%E7%96%8F%E6%95%B0%E6%8D%AE%EF%BC%8CGBDT%E5%BE%88%E5%AE%B9%E6%98%93%E8%BF%87%E6%8B%9F%E5%90%88%EF%BC%8C%E5%BE%97%E5%88%B0%E5%BE%88%E6%B7%B1%E7%9A%84%E6%A0%91%EF%BC%8CLR%E5%8F%AF%E4%BB%A5%E9%80%9A%E8%BF%87%E5%8A%A0%E5%85%A5%E6%AD%A3%E5%88%99%E5%8C%96%E9%98%B2%E6%AD%A2%E8%BF%87%E6%8B%9F%E5%90%88%0A%0A**GBDT%E4%B8%8ERF%E7%9A%84%E5%8C%BA%E5%88%AB**%0A%E7%9B%B8%E5%90%8C%E7%82%B9%EF%BC%9A%0A1%E3%80%81GBDT%E4%B8%8ERF%E9%83%BD%E6%98%AF%E9%87%87%E7%94%A8%E5%A4%9A%E6%A3%B5%E6%A0%91%E7%BB%84%E5%90%88%E4%BD%9C%E4%B8%BA%E6%9C%80%E7%BB%88%E7%BB%93%E6%9E%9C%EF%BC%9B%E8%BF%99%E6%98%AF%E4%B8%A4%E8%80%85%E5%85%B1%E5%90%8C%E7%82%B9%E3%80%82%0A%E4%B8%8D%E5%90%8C%E7%82%B9%EF%BC%9A%0A1%E3%80%81RF%E7%9A%84%E6%A0%91%E5%8F%AF%E4%BB%A5%E6%98%AF%E5%9B%9E%E5%BD%92%E6%A0%91%E4%B9%9F%E5%8F%AF%E4%BB%A5%E6%98%AF%E5%88%86%E7%B1%BB%E6%A0%91%EF%BC%8C%E8%80%8CGBDT%E5%8F%AA%E8%83%BD%E6%98%AF%E5%9B%9E%E5%BD%92%E6%A0%91%E3%80%82%0A2%E3%80%81RF%E4%B8%AD%E6%A0%91%E6%98%AF%E7%8B%AC%E7%AB%8B%E7%9A%84%EF%BC%8C%E7%9B%B8%E4%BA%92%E4%B9%8B%E9%97%B4%E4%B8%8D%E5%BD%B1%E5%93%8D%EF%BC%8C%E5%8F%AF%E4%BB%A5%E5%B9%B6%E8%A1%8C%EF%BC%9B%E8%80%8CGBDT%E6%A0%91%E4%B9%8B%E9%97%B4%E6%9C%89%E4%BE%9D%E8%B5%96%EF%BC%8C%E6%98%AF%E4%B8%B2%E8%A1%8C%E3%80%82%0A3%E3%80%81RF%E6%9C%80%E7%BB%88%E7%9A%84%E7%BB%93%E6%9E%9C%E6%98%AF%E5%A4%9A%E6%A3%B5%E6%A0%91%E8%A1%A8%E5%86%B3%E5%86%B3%E5%AE%9A%EF%BC%8C%E8%80%8CGBDT%E6%98%AF%E5%A4%9A%E6%A3%B5%E6%A0%91%E5%8F%A0%E5%8A%A0%E7%BB%84%E5%90%88%E6%9C%80%E7%BB%88%E7%9A%84%E7%BB%93%E6%9E%9C%E3%80%82%0A4%E3%80%81**RF%E5%AF%B9%E5%BC%82%E5%B8%B8%E5%80%BC%E4%B8%8D%E6%95%8F%E6%84%9F%EF%BC%8C%E5%8E%9F%E5%9B%A0%E6%98%AF%E5%A4%9A%E6%A3%B5%E6%A0%91%E8%A1%A8%E5%86%B3**%EF%BC%8C%E8%80%8CGBDT%E4%BD%BF%E7%94%A8%E5%B9%B3%E6%96%B9%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0%E7%9A%84%E6%97%B6%E5%80%99%E5%AF%B9%E5%BC%82%E5%B8%B8%E5%80%BC%E6%AF%94%E8%BE%83%E6%95%8F%E6%84%9F%E3%80%82%0A5%E3%80%81RF%E6%98%AF%E9%80%9A%E8%BF%87%E5%87%8F%E5%B0%91%E6%A8%A1%E5%9E%8B%E7%9A%84%E6%96%B9%E5%B7%AE%E6%9D%A5%E6%8F%90%E9%AB%98%E6%80%A7%E8%83%BD%EF%BC%8C%E8%80%8CGBDT%E6%98%AF%E5%87%8F%E5%B0%91%E6%A8%A1%E5%9E%8B%E7%9A%84%E5%81%8F%E5%B7%AE%E6%9D%A5%E6%8F%90%E9%AB%98%E6%80%A7%E8%83%BD%E7%9A%84%E3%80%82%0A%0A**%E4%B8%BA%E4%BB%80%E4%B9%88bagging%E5%8F%AF%E4%BB%A5%E9%99%8D%E4%BD%8E%E6%96%B9%E5%B7%AE%EF%BC%9F**%0A!%5B1a42f83b7cfd4b571627ebf7afea742e.png%5D(en-resource%3A%2F%2Fdatabase%2F2015%3A1)%0A!%5B02c8ae34338ed736efce7417ba0a53b4.png%5D(en-resource%3A%2F%2Fdatabase%2F2014%3A1)%0A!%5B322a7533b8f88f6f2bbe25aca09ff272.png%5D(en-resource%3A%2F%2Fdatabase%2F2018%3A1)%0A%0A**%E9%9A%8F%E6%9C%BA%E6%A3%AE%E6%9E%97%E4%B8%BA%E4%BB%80%E4%B9%88%E8%A6%81%E8%BF%9B%E8%A1%8C%E6%9C%89%E6%94%BE%E5%9B%9E%E7%9A%84%E9%9A%8F%E6%9C%BA%E6%8A%BD%E6%A0%B7%EF%BC%9F**%0A1.%20%E5%A6%82%E6%9E%9C%E4%B8%8D%E6%94%BE%E5%9B%9E%E6%8A%BD%E6%A0%B7%EF%BC%8C%E9%82%A3%E4%B9%88%E6%AF%8F%E6%A3%B5%E6%A0%91%E7%94%A8%E7%9A%84%E6%A0%B7%E6%9C%AC%E5%AE%8C%E5%85%A8%E4%B8%8D%E5%90%8C%EF%BC%8C%E5%9F%BA%E5%AD%A6%E4%B9%A0%E5%99%A8%E4%B9%8B%E9%97%B4%E7%9A%84**%E7%9B%B8%E4%BC%BC%E6%80%A7%E5%B0%8F**%EF%BC%8C%E6%8A%95%E7%A5%A8%E7%BB%93%E6%9E%9C%E5%B7%AE**%EF%BC%8C%E6%A8%A1%E5%9E%8B%E5%81%8F%E5%B7%AE%E5%A4%A7**%EF%BC%9B%0A2.%20%E5%A6%82%E6%9E%9C%E4%B8%8D%E6%8A%BD%E6%A0%B7%EF%BC%8C%E9%82%A3%E4%B9%88%E5%9F%BA%E5%AD%A6%E4%B9%A0%E5%99%A8%E7%94%A8%E6%89%80%E6%9C%89%E6%A0%B7%E6%9C%AC%E8%AE%AD%E7%BB%83%EF%BC%8C%E5%9F%BA%E5%AD%A6%E4%B9%A0%E5%99%A8%E5%A4%AA%E7%9B%B8%E4%BC%BC**%E5%B7%AE%E5%BC%82%E6%80%A7%E5%A4%AA%E5%B0%8F**%EF%BC%8C%E6%A8%A1%E5%9E%8B%E7%9A%84%E6%B3%9B%E5%8C%96%E6%80%A7%E5%B0%B1%E5%BE%88%E5%B7%AE%EF%BC%8C**%E6%96%B9%E5%B7%AE%E5%A4%A7**%EF%BC%9B%0A3.%20%E4%B8%BA%E4%BB%80%E4%B9%88%E4%B8%8D%E9%9A%8F%E6%9C%BA%E6%8A%BD%E6%A0%B7%EF%BC%9F%E8%BF%99%E9%87%8C%E8%87%AA%E5%8A%A9%E9%87%87%E6%A0%B7%E5%8F%AF%E4%BB%A5%E4%BA%A7%E7%94%9F%E4%B8%80%E9%83%A8%E5%88%86%E8%A2%8B%E5%A4%96%E6%A0%B7%E6%9C%AC%EF%BC%8C%E5%8F%AF%E4%BB%A5%E7%94%A8%E6%9D%A5%E5%81%9A%E8%A2%8B%E5%A4%96%E4%BC%B0%E8%AE%A1%EF%BC%9B%E5%8F%A6%E5%A4%96%E8%87%AA%E5%8A%A9%E9%87%87%E6%A0%B7%E4%B8%80%E5%AE%9A%E7%A8%8B%E5%BA%A6%E4%B8%8A%E6%94%B9%E5%8F%98%E4%BA%86%E6%AF%8F%E4%B8%AA%E5%9F%BA%E5%AD%A6%E4%B9%A0%E5%99%A8%E6%89%80%E7%94%A8%E6%95%B0%E6%8D%AE%E7%9A%84%E6%A0%B7%E6%9C%AC%E5%88%86%E5%B8%83%EF%BC%8C%E4%B8%80%E5%AE%9A%E7%A8%8B%E5%BA%A6%E4%B8%8A%E5%BC%95%E5%85%A5%E4%BA%86%E5%99%AA%E9%9F%B3%EF%BC%8C%E5%A2%9E%E5%8A%A0%E4%BA%86%E6%A8%A1%E5%9E%8B%E7%9A%84%E6%B3%9B%E5%8C%96%E8%83%BD%E5%8A%9B%E3%80%82%0A%0A**%E9%9A%8F%E6%9C%BA%E6%A3%AE%E6%9E%97%E8%A7%A3%E5%86%B3%E4%B8%8D%E5%B9%B3%E8%A1%A1%E6%95%B0%E6%8D%AE%E9%97%AE%E9%A2%98%EF%BC%9F**%0A%E8%87%AA%E5%8A%A9%E9%87%87%E6%A0%B7%E6%B3%95%EF%BC%8C%E6%AF%8F%E6%AC%A1%E9%9A%8F%E6%9C%BA%E9%80%89%E5%8F%96%E4%B8%80%E9%83%A8%E5%88%86%E5%AD%90%E9%9B%86%EF%BC%8C%E7%9B%B8%E5%AF%B9%E6%9D%A5%E8%AF%B4%E5%8F%AF%E4%BB%A5%E5%9D%87%E8%A1%A1%E7%9A%84%E4%BD%BF%E7%94%A8%E6%AF%8F%E4%B8%AA%E7%B1%BB%E5%88%AB%E6%95%B0%E6%8D%AE%E3%80%82%EF%BC%9F%EF%BC%9F%EF%BC%9F%EF%BC%9F%0A%E5%9C%A8%E6%AF%8F%E6%AC%A1%E7%94%9F%E6%88%90%E8%AE%AD%E7%BB%83%E9%9B%86%E6%97%B6%E4%BD%BF%E7%94%A8%E6%89%80%E6%9C%89%E5%88%86%E7%B1%BB%E4%B8%AD%E7%9A%84%E5%B0%8F%E6%A0%B7%E6%9C%AC%E9%87%8F%EF%BC%8C%E5%90%8C%E6%97%B6%E4%BB%8E%E5%88%86%E7%B1%BB%E4%B8%AD%E7%9A%84%E5%A4%A7%E6%A0%B7%E6%9C%AC%E9%87%8F%E4%B8%AD%E9%9A%8F%E6%9C%BA%E6%8A%BD%E5%8F%96%E6%95%B0%E6%8D%AE%E6%9D%A5%E4%B8%8E%E5%B0%8F%E6%A0%B7%E6%9C%AC%E9%87%8F%E5%90%88%E5%B9%B6%E6%9E%84%E6%88%90%E8%AE%AD%E7%BB%83%E9%9B%86%EF%BC%8C%E8%BF%99%E6%A0%B7%E5%8F%8D%E5%A4%8D%E5%A4%9A%E6%AC%A1%E4%BC%9A%E5%BE%97%E5%88%B0%E5%BE%88%E5%A4%9A%E8%AE%AD%E7%BB%83%E9%9B%86%E5%92%8C%E8%AE%AD%E7%BB%83%E6%A8%A1%E5%9E%8B%E3%80%82%0A*%20**Stacking**%0A%0A**%E6%9C%89%E7%BC%BA%E5%A4%B1%E5%80%BC%E7%9A%84%E6%95%B0%E6%8D%AE%E5%9C%A8%E7%BB%8F%E8%BF%87%E7%BC%BA%E5%A4%B1%E5%A4%84%E7%90%86%E5%90%8E%EF%BC%9A**%0A%E6%95%B0%E6%8D%AE%E9%87%8F%E5%BE%88%E5%B0%8F%EF%BC%8C%E6%9C%B4%E7%B4%A0%E8%B4%9D%E5%8F%B6%E6%96%AF%EF%BC%8C%E8%B4%9D%E5%8F%B6%E6%96%AF%E6%A8%A1%E5%9E%8B%E5%AF%B9%E4%BA%8E%E7%BC%BA%E5%A4%B1%E6%95%B0%E6%8D%AE%E4%B9%9F%E6%AF%94%E8%BE%83%E7%A8%B3%E5%AE%9A%0A%E6%95%B0%E6%8D%AE%E9%87%8F%E9%80%82%E4%B8%AD%E6%88%96%E8%80%85%E8%BE%83%E5%A4%A7%EF%BC%8C%E7%94%A8%E6%A0%91%E6%A8%A1%E5%9E%8B%20%EF%BC%8C%E4%BC%98%E5%85%88xgboost%0A%E6%95%B0%E6%8D%AE%E9%87%8F%E8%BE%83%E5%A4%A7%EF%BC%8C%E4%B9%9F%E5%8F%AF%E4%BB%A5%E7%94%A8%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C%0A%E9%81%BF%E5%85%8D%E4%BD%BF%E7%94%A8%E8%B7%9D%E7%A6%BB%E5%BA%A6%E9%87%8F%E7%9B%B8%E5%85%B3%E7%9A%84%E6%A8%A1%E5%9E%8B%EF%BC%8C%E5%A6%82KNN%E5%92%8CSVM%0A%0A**%E4%B8%BA%E4%BB%80%E4%B9%88%E9%9A%8F%E6%9C%BA%E6%A3%AE%E6%9E%97%E7%9A%84%E6%A0%91%E6%B7%B1%E5%BA%A6%E5%BE%80%E5%BE%80%E5%A4%A7%E4%BA%8E%20GBDT%20%E7%9A%84%E6%A0%91%E6%B7%B1%E5%BA%A6%EF%BC%9F**%0A%0A%E5%AF%B9%E4%BA%8E%20Bagging%20%E7%AE%97%E6%B3%95%E6%9D%A5%E8%AF%B4%EF%BC%8C%E5%85%B6%E5%8F%AF%E4%BB%A5%E4%BF%9D%E8%AF%81%E6%96%B9%E5%B7%AE%E3%80%82%E6%89%80%E4%BB%A5%E5%AF%B9%E4%BA%8E%E6%AF%8F%E4%B8%AA%E5%9F%BA%E5%88%86%E7%B1%BB%E5%99%A8%E6%9D%A5%E8%AF%B4%EF%BC%8C%E7%9B%AE%E6%A0%87%E5%B0%B1%E6%98%AF%E5%A6%82%E4%BD%95%E9%99%8D%E4%BD%8E%E8%BF%99%E4%B8%AA%E5%81%8F%E5%B7%AE%EF%BC%8C%E6%89%80%E4%BB%A5%E6%88%91%E4%BB%AC%E4%BC%9A%E9%87%87%E7%94%A8%E6%B7%B1%E5%BA%A6%E5%BE%88%E6%B7%B1%E7%94%9A%E8%87%B3%E4%B8%8D%E5%89%AA%E6%9E%9D%E7%9A%84%E5%86%B3%E7%AD%96%E6%A0%91%E3%80%82%0A%E5%AF%B9%E4%BA%8E%20Boosting%20%E6%9D%A5%E8%AF%B4%EF%BC%8C%E6%AF%8F%E4%B8%80%E6%AD%A5%E6%88%91%E4%BB%AC%E9%83%BD%E4%BC%9A%E5%9C%A8%E4%B8%8A%E4%B8%80%E8%BD%AE%E7%9A%84%E5%9F%BA%E7%A1%80%E4%B8%8A%E6%9B%B4%E5%8A%A0%E6%8B%9F%E5%90%88%E5%8E%9F%E6%95%B0%E6%8D%AE%EF%BC%8C%E6%89%80%E4%BB%A5%E5%8F%AF%E4%BB%A5%E4%BF%9D%E8%AF%81%E5%81%8F%E5%B7%AE%EF%BC%8C%E6%89%80%E4%BB%A5%E5%AF%B9%E4%BA%8E%E6%AF%8F%E4%B8%AA%E5%9F%BA%E5%88%86%E7%B1%BB%E5%99%A8%E6%9D%A5%E8%AF%B4%EF%BC%8C%E9%97%AE%E9%A2%98%E5%B0%B1%E5%9C%A8%E4%BA%8E%E5%A6%82%E4%BD%95%E9%80%89%E6%8B%A9%E6%96%B9%E5%B7%AE%20%E6%9B%B4%E5%B0%8F%E7%9A%84%E5%88%86%E7%B1%BB%E5%99%A8%EF%BC%8C%E5%8D%B3%E6%9B%B4%E7%AE%80%E5%8D%95%E7%9A%84%E5%88%86%E7%B1%BB%E5%99%A8%EF%BC%8C%E6%89%80%E4%BB%A5%E6%88%91%E4%BB%AC%E9%80%89%E6%8B%A9%E4%BA%86%E6%B7%B1%E5%BA%A6%E5%BE%88%E6%B5%85%E7%9A%84%E5%86%B3%E7%AD%96%E6%A0%91%E3%80%82%0A%0A**%E4%BA%A4%E5%8F%89%E9%AA%8C%E8%AF%81%E5%92%8C%E8%87%AA%E5%8A%A9%E9%87%87%E6%A0%B7%E5%8C%BA%E5%88%AB%EF%BC%9F**%0A%E4%BA%A4%E5%8F%89%E9%AA%8C%E8%AF%81%E9%9A%8F%E6%9C%BA%E4%BD%BF%E7%94%A8%E4%B8%8D%E5%90%8C%E7%9A%84%E5%88%92%E5%88%86%E9%87%8D%E5%A4%8Dp%E6%AC%A1%EF%BC%8C%E6%9C%80%E7%BB%88%E7%9A%84%E8%AF%84%E4%BC%B0%E7%BB%93%E6%9E%9C%E6%98%AF%E8%BF%99p%E6%AC%A1k%E6%8A%98%E4%BA%A4%E5%8F%89%E9%AA%8C%E8%AF%81%E7%BB%93%E6%9E%9C%E7%9A%84%E5%9D%87%E5%80%BC%EF%BC%8C%E7%9B%AE%E7%9A%84%E6%98%AF%E5%87%8F%E5%B0%8F%E5%9B%A0%E4%B8%BA%E6%A0%B7%E6%9C%AC%E4%B8%8D%E5%90%8C%E8%80%8C%E5%BC%95%E5%85%A5%E7%9A%84%E5%B7%AE%E5%88%AB%E3%80%82%0A%E8%87%AA%E5%8A%A9%E6%B3%95%E8%83%BD%E4%BB%8E%E5%88%9D%E5%A7%8B%E6%95%B0%E6%8D%AE%E9%9B%86%E4%B8%AD%E4%BA%A7%E7%94%9F%E5%A4%9A%E4%B8%AA%E4%B8%8D%E5%90%8C%E7%9A%84%E8%AE%AD%E7%BB%83%E9%9B%86%EF%BC%8C%E5%8F%AF%E4%BB%A5%E5%85%85%E5%88%86%E5%88%A9%E7%94%A8%E6%95%B0%E6%8D%AE</center></body></html>