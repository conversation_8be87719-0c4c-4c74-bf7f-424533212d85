<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/><meta name="exporter-version" content="Evernote Mac 9.7.14 (474683)"/><meta name="created" content="2021-11-14 07:50:04 +0000"/><meta name="updated" content="2021-11-14 07:50:04 +0000"/><meta name="content-class" content="yinxiang.markdown"/><title>异构图</title></head><body><div style="font-size: 14px; margin: 0; padding: 0; width: 100%;"><p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">异构图</strong><br clear="none"/>
<img src="https://app.yinxiang.com/FileSharing.action?hash=1/320aab875a8bea2baa9ac631d976df6e-170245" width="1320" height="284"/></p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;">异构图指的是图中存在不同类型的节点和边（节点和边至少有一个具有多种类型），常见于知识图谱的场景。<br clear="none"/>
根据节点的边类型和距离将邻居节点分类，如此就可以将异构图分解为一个同构图的集合（这也被称为多通道网络）<br clear="none"/>
在异构图中，两个节点可以通过不同的语义路径连接，称为元路径.异构图中不同的元路径可以提取不同的语义信息。<br clear="none"/>
metapath2vec是Deepwalk的扩展，适用于异构网络。为了构建随机漫游，metapath2vec使用基于元数据的漫游来捕获不同类型节点之间的关系。<br clear="none"/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">异构图嵌入</strong><br clear="none"/>
（1）结构保留异构图嵌入，主要是捕获并保存异构的结构和语义，例如元路经和元图。<br clear="none"/>
（2）属性辅助异构图嵌入，嵌入技术中加了结构之外的很多信息。<br clear="none"/>
HETNN是无监督HGNNs的代表作。它由三部分组成:内容聚合、邻居聚合和类型聚合。通过这三种聚合， 异构网络可以保持图结构和节点属性的异构性。</p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;">HNE提出在HG中学习跨模型数据的嵌入，但它忽略了各种类型的链接。 SHNE专注于通过设计具有门控递归单元的深度语义编码器来捕获节点的语义信息(GRU)。虽然他用skim-gram来保持图的异构性，是专门为文本数据设计的，旨在学习不同类型边的异构图的节点嵌入， 与HetGNN相比，GATNE更注重区分节点对之间不同的链接关系。<br clear="none"/>
HAN [15]其使用分层注意机制来捕捉节点和语义重要性。节点级注意旨在利用自我注意机制[73]来了解某个元路径中邻居的重要性。语义级注意旨在学习不同元路径的重要性。预测层利用标记节点更新节点嵌入。 HAN是第一个将GNN（图神经网络）扩展到异构图的人，他设计了一个层次化的注意机制，可以捕捉结构和语义信息。<br clear="none"/>
（3）面向应用的异构图嵌入（4）动态异构图嵌入</p>
<ul style="line-height: 160%; box-sizing: content-box; display: block; list-style-type: disc; padding-left: 30px; margin: 6px 0 10px; color: #333;"><li style="line-height: 160%; box-sizing: content-box; position: relative;">浅层模型（Shallow Model）<br clear="none"/>
基于随机游走的方法<br clear="none"/>
基于分解的方法</li><li style="line-height: 160%; box-sizing: content-box; position: relative;">深度模型（Deep Model）<br clear="none"/>
基于信息传递的方法<br clear="none"/>
基于编码器-解码器的方法<br clear="none"/>
基于对抗的方法<br clear="none"/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">元路径</strong><br clear="none"/>
在电影推荐的场景里，我们用 U 表示用户，用 M 表示电影，那么 UUM 是一条元路径。它表示一位用户关注了另一位用户，那么我们可以将用户看过的电影，推荐给关注他的人。当然，还有比如 UMUM 表示与你看过相同电影的人还在看什么电影这条路径；UMTM 表示与你看过同一类型电影的路径…元路径有很多，不同元路径对于不同的业务语义。</li></ul>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">metapath2vec</strong><br clear="none"/>
基于meta-path的random walks来构建每个顶点的异构邻域，然后用Skip-Gram模型来完成顶点的嵌入。<br clear="none"/>
类似word2vec的思想，即：使用skip-gram模型对每个顶点的局部领域顶点信息进行预测，进而学习到每个顶点的特征表示。</p>
<p style="line-height: 160%; box-sizing: content-box; margin: 10px 0; color: #333;"><strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">元路径随机游走</strong>：定义好一个游走类型路径，在上图中，路径可以定为APA,APVPA等，然后按照这个路径游走，即下一个节点只采样符合要求的节点类型；元路径通常为对称的。<br clear="none"/>
优点：这种元路径随机游走策略可以确保不同类型的节点语义关系被恰当的并入skip-gram模型中<br clear="none"/>
<strong style="line-height: 160%; box-sizing: content-box; font-weight: 700;">skip-gram</strong>:最大化节点和其异构上下文邻居的共现概率。<br clear="none"/>
<img src="https://app.yinxiang.com/FileSharing.action?hash=1/b6562bf9c7a2adafe9d5cc4acc1cb187-99036" width="1364" height="412"/><br clear="none"/>
<img src="https://app.yinxiang.com/FileSharing.action?hash=1/75d99ddcd7036c7251ba4df862a6b1f2-364546" width="1366" height="658"/><br clear="none"/>
负采样</p>
</div><center style="display:none !important;visibility:collapse !important;height:0 !important;white-space:nowrap;width:100%;overflow:hidden">**%E5%BC%82%E6%9E%84%E5%9B%BE**%0A!%5B320aab875a8bea2baa9ac631d976df6e.png%5D(evernotecid%3A%2F%2F50E31125-9E8E-4F15-8430-8CF4D64A8B72%2Fappyinxiangcom%2F30104613%2FENResource%2Fp466)%0A%0A%E5%BC%82%E6%9E%84%E5%9B%BE%E6%8C%87%E7%9A%84%E6%98%AF%E5%9B%BE%E4%B8%AD%E5%AD%98%E5%9C%A8%E4%B8%8D%E5%90%8C%E7%B1%BB%E5%9E%8B%E7%9A%84%E8%8A%82%E7%82%B9%E5%92%8C%E8%BE%B9%EF%BC%88%E8%8A%82%E7%82%B9%E5%92%8C%E8%BE%B9%E8%87%B3%E5%B0%91%E6%9C%89%E4%B8%80%E4%B8%AA%E5%85%B7%E6%9C%89%E5%A4%9A%E7%A7%8D%E7%B1%BB%E5%9E%8B%EF%BC%89%EF%BC%8C%E5%B8%B8%E8%A7%81%E4%BA%8E%E7%9F%A5%E8%AF%86%E5%9B%BE%E8%B0%B1%E7%9A%84%E5%9C%BA%E6%99%AF%E3%80%82%0A%E6%A0%B9%E6%8D%AE%E8%8A%82%E7%82%B9%E7%9A%84%E8%BE%B9%E7%B1%BB%E5%9E%8B%E5%92%8C%E8%B7%9D%E7%A6%BB%E5%B0%86%E9%82%BB%E5%B1%85%E8%8A%82%E7%82%B9%E5%88%86%E7%B1%BB%EF%BC%8C%E5%A6%82%E6%AD%A4%E5%B0%B1%E5%8F%AF%E4%BB%A5%E5%B0%86%E5%BC%82%E6%9E%84%E5%9B%BE%E5%88%86%E8%A7%A3%E4%B8%BA%E4%B8%80%E4%B8%AA%E5%90%8C%E6%9E%84%E5%9B%BE%E7%9A%84%E9%9B%86%E5%90%88%EF%BC%88%E8%BF%99%E4%B9%9F%E8%A2%AB%E7%A7%B0%E4%B8%BA%E5%A4%9A%E9%80%9A%E9%81%93%E7%BD%91%E7%BB%9C%EF%BC%89%0A%E5%9C%A8%E5%BC%82%E6%9E%84%E5%9B%BE%E4%B8%AD%EF%BC%8C%E4%B8%A4%E4%B8%AA%E8%8A%82%E7%82%B9%E5%8F%AF%E4%BB%A5%E9%80%9A%E8%BF%87%E4%B8%8D%E5%90%8C%E7%9A%84%E8%AF%AD%E4%B9%89%E8%B7%AF%E5%BE%84%E8%BF%9E%E6%8E%A5%EF%BC%8C%E7%A7%B0%E4%B8%BA%E5%85%83%E8%B7%AF%E5%BE%84.%E5%BC%82%E6%9E%84%E5%9B%BE%E4%B8%AD%E4%B8%8D%E5%90%8C%E7%9A%84%E5%85%83%E8%B7%AF%E5%BE%84%E5%8F%AF%E4%BB%A5%E6%8F%90%E5%8F%96%E4%B8%8D%E5%90%8C%E7%9A%84%E8%AF%AD%E4%B9%89%E4%BF%A1%E6%81%AF%E3%80%82%0Ametapath2vec%E6%98%AFDeepwalk%E7%9A%84%E6%89%A9%E5%B1%95%EF%BC%8C%E9%80%82%E7%94%A8%E4%BA%8E%E5%BC%82%E6%9E%84%E7%BD%91%E7%BB%9C%E3%80%82%E4%B8%BA%E4%BA%86%E6%9E%84%E5%BB%BA%E9%9A%8F%E6%9C%BA%E6%BC%AB%E6%B8%B8%EF%BC%8Cmetapath2vec%E4%BD%BF%E7%94%A8%E5%9F%BA%E4%BA%8E%E5%85%83%E6%95%B0%E6%8D%AE%E7%9A%84%E6%BC%AB%E6%B8%B8%E6%9D%A5%E6%8D%95%E8%8E%B7%E4%B8%8D%E5%90%8C%E7%B1%BB%E5%9E%8B%E8%8A%82%E7%82%B9%E4%B9%8B%E9%97%B4%E7%9A%84%E5%85%B3%E7%B3%BB%E3%80%82%0A**%E5%BC%82%E6%9E%84%E5%9B%BE%E5%B5%8C%E5%85%A5**%0A%EF%BC%881%EF%BC%89%E7%BB%93%E6%9E%84%E4%BF%9D%E7%95%99%E5%BC%82%E6%9E%84%E5%9B%BE%E5%B5%8C%E5%85%A5%EF%BC%8C%E4%B8%BB%E8%A6%81%E6%98%AF%E6%8D%95%E8%8E%B7%E5%B9%B6%E4%BF%9D%E5%AD%98%E5%BC%82%E6%9E%84%E7%9A%84%E7%BB%93%E6%9E%84%E5%92%8C%E8%AF%AD%E4%B9%89%EF%BC%8C%E4%BE%8B%E5%A6%82%E5%85%83%E8%B7%AF%E7%BB%8F%E5%92%8C%E5%85%83%E5%9B%BE%E3%80%82%0A%EF%BC%882%EF%BC%89%E5%B1%9E%E6%80%A7%E8%BE%85%E5%8A%A9%E5%BC%82%E6%9E%84%E5%9B%BE%E5%B5%8C%E5%85%A5%EF%BC%8C%E5%B5%8C%E5%85%A5%E6%8A%80%E6%9C%AF%E4%B8%AD%E5%8A%A0%E4%BA%86%E7%BB%93%E6%9E%84%E4%B9%8B%E5%A4%96%E7%9A%84%E5%BE%88%E5%A4%9A%E4%BF%A1%E6%81%AF%E3%80%82%0AHETNN%E6%98%AF%E6%97%A0%E7%9B%91%E7%9D%A3HGNNs%E7%9A%84%E4%BB%A3%E8%A1%A8%E4%BD%9C%E3%80%82%E5%AE%83%E7%94%B1%E4%B8%89%E9%83%A8%E5%88%86%E7%BB%84%E6%88%90%3A%E5%86%85%E5%AE%B9%E8%81%9A%E5%90%88%E3%80%81%E9%82%BB%E5%B1%85%E8%81%9A%E5%90%88%E5%92%8C%E7%B1%BB%E5%9E%8B%E8%81%9A%E5%90%88%E3%80%82%E9%80%9A%E8%BF%87%E8%BF%99%E4%B8%89%E7%A7%8D%E8%81%9A%E5%90%88%EF%BC%8C%20%E5%BC%82%E6%9E%84%E7%BD%91%E7%BB%9C%E5%8F%AF%E4%BB%A5%E4%BF%9D%E6%8C%81%E5%9B%BE%E7%BB%93%E6%9E%84%E5%92%8C%E8%8A%82%E7%82%B9%E5%B1%9E%E6%80%A7%E7%9A%84%E5%BC%82%E6%9E%84%E6%80%A7%E3%80%82%0A%0AHNE%E6%8F%90%E5%87%BA%E5%9C%A8HG%E4%B8%AD%E5%AD%A6%E4%B9%A0%E8%B7%A8%E6%A8%A1%E5%9E%8B%E6%95%B0%E6%8D%AE%E7%9A%84%E5%B5%8C%E5%85%A5%EF%BC%8C%E4%BD%86%E5%AE%83%E5%BF%BD%E7%95%A5%E4%BA%86%E5%90%84%E7%A7%8D%E7%B1%BB%E5%9E%8B%E7%9A%84%E9%93%BE%E6%8E%A5%E3%80%82%20SHNE%E4%B8%93%E6%B3%A8%E4%BA%8E%E9%80%9A%E8%BF%87%E8%AE%BE%E8%AE%A1%E5%85%B7%E6%9C%89%E9%97%A8%E6%8E%A7%E9%80%92%E5%BD%92%E5%8D%95%E5%85%83%E7%9A%84%E6%B7%B1%E5%BA%A6%E8%AF%AD%E4%B9%89%E7%BC%96%E7%A0%81%E5%99%A8%E6%9D%A5%E6%8D%95%E8%8E%B7%E8%8A%82%E7%82%B9%E7%9A%84%E8%AF%AD%E4%B9%89%E4%BF%A1%E6%81%AF(GRU)%E3%80%82%E8%99%BD%E7%84%B6%E4%BB%96%E7%94%A8skim-gram%E6%9D%A5%E4%BF%9D%E6%8C%81%E5%9B%BE%E7%9A%84%E5%BC%82%E6%9E%84%E6%80%A7%EF%BC%8C%E6%98%AF%E4%B8%93%E9%97%A8%E4%B8%BA%E6%96%87%E6%9C%AC%E6%95%B0%E6%8D%AE%E8%AE%BE%E8%AE%A1%E7%9A%84%EF%BC%8C%E6%97%A8%E5%9C%A8%E5%AD%A6%E4%B9%A0%E4%B8%8D%E5%90%8C%E7%B1%BB%E5%9E%8B%E8%BE%B9%E7%9A%84%E5%BC%82%E6%9E%84%E5%9B%BE%E7%9A%84%E8%8A%82%E7%82%B9%E5%B5%8C%E5%85%A5%EF%BC%8C%20%E4%B8%8EHetGNN%E7%9B%B8%E6%AF%94%EF%BC%8CGATNE%E6%9B%B4%E6%B3%A8%E9%87%8D%E5%8C%BA%E5%88%86%E8%8A%82%E7%82%B9%E5%AF%B9%E4%B9%8B%E9%97%B4%E4%B8%8D%E5%90%8C%E7%9A%84%E9%93%BE%E6%8E%A5%E5%85%B3%E7%B3%BB%E3%80%82%0AHAN%20%5B15%5D%E5%85%B6%E4%BD%BF%E7%94%A8%E5%88%86%E5%B1%82%E6%B3%A8%E6%84%8F%E6%9C%BA%E5%88%B6%E6%9D%A5%E6%8D%95%E6%8D%89%E8%8A%82%E7%82%B9%E5%92%8C%E8%AF%AD%E4%B9%89%E9%87%8D%E8%A6%81%E6%80%A7%E3%80%82%E8%8A%82%E7%82%B9%E7%BA%A7%E6%B3%A8%E6%84%8F%E6%97%A8%E5%9C%A8%E5%88%A9%E7%94%A8%E8%87%AA%E6%88%91%E6%B3%A8%E6%84%8F%E6%9C%BA%E5%88%B6%5B73%5D%E6%9D%A5%E4%BA%86%E8%A7%A3%E6%9F%90%E4%B8%AA%E5%85%83%E8%B7%AF%E5%BE%84%E4%B8%AD%E9%82%BB%E5%B1%85%E7%9A%84%E9%87%8D%E8%A6%81%E6%80%A7%E3%80%82%E8%AF%AD%E4%B9%89%E7%BA%A7%E6%B3%A8%E6%84%8F%E6%97%A8%E5%9C%A8%E5%AD%A6%E4%B9%A0%E4%B8%8D%E5%90%8C%E5%85%83%E8%B7%AF%E5%BE%84%E7%9A%84%E9%87%8D%E8%A6%81%E6%80%A7%E3%80%82%E9%A2%84%E6%B5%8B%E5%B1%82%E5%88%A9%E7%94%A8%E6%A0%87%E8%AE%B0%E8%8A%82%E7%82%B9%E6%9B%B4%E6%96%B0%E8%8A%82%E7%82%B9%E5%B5%8C%E5%85%A5%E3%80%82%20HAN%E6%98%AF%E7%AC%AC%E4%B8%80%E4%B8%AA%E5%B0%86GNN%EF%BC%88%E5%9B%BE%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C%EF%BC%89%E6%89%A9%E5%B1%95%E5%88%B0%E5%BC%82%E6%9E%84%E5%9B%BE%E7%9A%84%E4%BA%BA%EF%BC%8C%E4%BB%96%E8%AE%BE%E8%AE%A1%E4%BA%86%E4%B8%80%E4%B8%AA%E5%B1%82%E6%AC%A1%E5%8C%96%E7%9A%84%E6%B3%A8%E6%84%8F%E6%9C%BA%E5%88%B6%EF%BC%8C%E5%8F%AF%E4%BB%A5%E6%8D%95%E6%8D%89%E7%BB%93%E6%9E%84%E5%92%8C%E8%AF%AD%E4%B9%89%E4%BF%A1%E6%81%AF%E3%80%82%0A%EF%BC%883%EF%BC%89%E9%9D%A2%E5%90%91%E5%BA%94%E7%94%A8%E7%9A%84%E5%BC%82%E6%9E%84%E5%9B%BE%E5%B5%8C%E5%85%A5%EF%BC%884%EF%BC%89%E5%8A%A8%E6%80%81%E5%BC%82%E6%9E%84%E5%9B%BE%E5%B5%8C%E5%85%A5%0A*%20%E6%B5%85%E5%B1%82%E6%A8%A1%E5%9E%8B%EF%BC%88Shallow%20Model%EF%BC%89%0A%E5%9F%BA%E4%BA%8E%E9%9A%8F%E6%9C%BA%E6%B8%B8%E8%B5%B0%E7%9A%84%E6%96%B9%E6%B3%95%0A%E5%9F%BA%E4%BA%8E%E5%88%86%E8%A7%A3%E7%9A%84%E6%96%B9%E6%B3%95%0A*%20%E6%B7%B1%E5%BA%A6%E6%A8%A1%E5%9E%8B%EF%BC%88Deep%20Model%EF%BC%89%0A%E5%9F%BA%E4%BA%8E%E4%BF%A1%E6%81%AF%E4%BC%A0%E9%80%92%E7%9A%84%E6%96%B9%E6%B3%95%0A%E5%9F%BA%E4%BA%8E%E7%BC%96%E7%A0%81%E5%99%A8-%E8%A7%A3%E7%A0%81%E5%99%A8%E7%9A%84%E6%96%B9%E6%B3%95%0A%E5%9F%BA%E4%BA%8E%E5%AF%B9%E6%8A%97%E7%9A%84%E6%96%B9%E6%B3%95%0A**%E5%85%83%E8%B7%AF%E5%BE%84**%0A%E5%9C%A8%E7%94%B5%E5%BD%B1%E6%8E%A8%E8%8D%90%E7%9A%84%E5%9C%BA%E6%99%AF%E9%87%8C%EF%BC%8C%E6%88%91%E4%BB%AC%E7%94%A8%20U%20%E8%A1%A8%E7%A4%BA%E7%94%A8%E6%88%B7%EF%BC%8C%E7%94%A8%20M%20%E8%A1%A8%E7%A4%BA%E7%94%B5%E5%BD%B1%EF%BC%8C%E9%82%A3%E4%B9%88%20UUM%20%E6%98%AF%E4%B8%80%E6%9D%A1%E5%85%83%E8%B7%AF%E5%BE%84%E3%80%82%E5%AE%83%E8%A1%A8%E7%A4%BA%E4%B8%80%E4%BD%8D%E7%94%A8%E6%88%B7%E5%85%B3%E6%B3%A8%E4%BA%86%E5%8F%A6%E4%B8%80%E4%BD%8D%E7%94%A8%E6%88%B7%EF%BC%8C%E9%82%A3%E4%B9%88%E6%88%91%E4%BB%AC%E5%8F%AF%E4%BB%A5%E5%B0%86%E7%94%A8%E6%88%B7%E7%9C%8B%E8%BF%87%E7%9A%84%E7%94%B5%E5%BD%B1%EF%BC%8C%E6%8E%A8%E8%8D%90%E7%BB%99%E5%85%B3%E6%B3%A8%E4%BB%96%E7%9A%84%E4%BA%BA%E3%80%82%E5%BD%93%E7%84%B6%EF%BC%8C%E8%BF%98%E6%9C%89%E6%AF%94%E5%A6%82%20UMUM%20%E8%A1%A8%E7%A4%BA%E4%B8%8E%E4%BD%A0%E7%9C%8B%E8%BF%87%E7%9B%B8%E5%90%8C%E7%94%B5%E5%BD%B1%E7%9A%84%E4%BA%BA%E8%BF%98%E5%9C%A8%E7%9C%8B%E4%BB%80%E4%B9%88%E7%94%B5%E5%BD%B1%E8%BF%99%E6%9D%A1%E8%B7%AF%E5%BE%84%EF%BC%9BUMTM%20%E8%A1%A8%E7%A4%BA%E4%B8%8E%E4%BD%A0%E7%9C%8B%E8%BF%87%E5%90%8C%E4%B8%80%E7%B1%BB%E5%9E%8B%E7%94%B5%E5%BD%B1%E7%9A%84%E8%B7%AF%E5%BE%84%E2%80%A6%E5%85%83%E8%B7%AF%E5%BE%84%E6%9C%89%E5%BE%88%E5%A4%9A%EF%BC%8C%E4%B8%8D%E5%90%8C%E5%85%83%E8%B7%AF%E5%BE%84%E5%AF%B9%E4%BA%8E%E4%B8%8D%E5%90%8C%E7%9A%84%E4%B8%9A%E5%8A%A1%E8%AF%AD%E4%B9%89%E3%80%82%0A%0A**metapath2vec**%0A%E5%9F%BA%E4%BA%8Emeta-path%E7%9A%84random%20walks%E6%9D%A5%E6%9E%84%E5%BB%BA%E6%AF%8F%E4%B8%AA%E9%A1%B6%E7%82%B9%E7%9A%84%E5%BC%82%E6%9E%84%E9%82%BB%E5%9F%9F%EF%BC%8C%E7%84%B6%E5%90%8E%E7%94%A8Skip-Gram%E6%A8%A1%E5%9E%8B%E6%9D%A5%E5%AE%8C%E6%88%90%E9%A1%B6%E7%82%B9%E7%9A%84%E5%B5%8C%E5%85%A5%E3%80%82%0A%E7%B1%BB%E4%BC%BCword2vec%E7%9A%84%E6%80%9D%E6%83%B3%EF%BC%8C%E5%8D%B3%EF%BC%9A%E4%BD%BF%E7%94%A8skip-gram%E6%A8%A1%E5%9E%8B%E5%AF%B9%E6%AF%8F%E4%B8%AA%E9%A1%B6%E7%82%B9%E7%9A%84%E5%B1%80%E9%83%A8%E9%A2%86%E5%9F%9F%E9%A1%B6%E7%82%B9%E4%BF%A1%E6%81%AF%E8%BF%9B%E8%A1%8C%E9%A2%84%E6%B5%8B%EF%BC%8C%E8%BF%9B%E8%80%8C%E5%AD%A6%E4%B9%A0%E5%88%B0%E6%AF%8F%E4%B8%AA%E9%A1%B6%E7%82%B9%E7%9A%84%E7%89%B9%E5%BE%81%E8%A1%A8%E7%A4%BA%E3%80%82%0A%0A**%E5%85%83%E8%B7%AF%E5%BE%84%E9%9A%8F%E6%9C%BA%E6%B8%B8%E8%B5%B0**%EF%BC%9A%E5%AE%9A%E4%B9%89%E5%A5%BD%E4%B8%80%E4%B8%AA%E6%B8%B8%E8%B5%B0%E7%B1%BB%E5%9E%8B%E8%B7%AF%E5%BE%84%EF%BC%8C%E5%9C%A8%E4%B8%8A%E5%9B%BE%E4%B8%AD%EF%BC%8C%E8%B7%AF%E5%BE%84%E5%8F%AF%E4%BB%A5%E5%AE%9A%E4%B8%BAAPA%2CAPVPA%E7%AD%89%EF%BC%8C%E7%84%B6%E5%90%8E%E6%8C%89%E7%85%A7%E8%BF%99%E4%B8%AA%E8%B7%AF%E5%BE%84%E6%B8%B8%E8%B5%B0%EF%BC%8C%E5%8D%B3%E4%B8%8B%E4%B8%80%E4%B8%AA%E8%8A%82%E7%82%B9%E5%8F%AA%E9%87%87%E6%A0%B7%E7%AC%A6%E5%90%88%E8%A6%81%E6%B1%82%E7%9A%84%E8%8A%82%E7%82%B9%E7%B1%BB%E5%9E%8B%EF%BC%9B%E5%85%83%E8%B7%AF%E5%BE%84%E9%80%9A%E5%B8%B8%E4%B8%BA%E5%AF%B9%E7%A7%B0%E7%9A%84%E3%80%82%0A%E4%BC%98%E7%82%B9%EF%BC%9A%E8%BF%99%E7%A7%8D%E5%85%83%E8%B7%AF%E5%BE%84%E9%9A%8F%E6%9C%BA%E6%B8%B8%E8%B5%B0%E7%AD%96%E7%95%A5%E5%8F%AF%E4%BB%A5%E7%A1%AE%E4%BF%9D%E4%B8%8D%E5%90%8C%E7%B1%BB%E5%9E%8B%E7%9A%84%E8%8A%82%E7%82%B9%E8%AF%AD%E4%B9%89%E5%85%B3%E7%B3%BB%E8%A2%AB%E6%81%B0%E5%BD%93%E7%9A%84%E5%B9%B6%E5%85%A5skip-gram%E6%A8%A1%E5%9E%8B%E4%B8%AD%0A**skip-gram**%3A%E6%9C%80%E5%A4%A7%E5%8C%96%E8%8A%82%E7%82%B9%E5%92%8C%E5%85%B6%E5%BC%82%E6%9E%84%E4%B8%8A%E4%B8%8B%E6%96%87%E9%82%BB%E5%B1%85%E7%9A%84%E5%85%B1%E7%8E%B0%E6%A6%82%E7%8E%87%E3%80%82%0A!%5Bb6562bf9c7a2adafe9d5cc4acc1cb187.png%5D(evernotecid%3A%2F%2F50E31125-9E8E-4F15-8430-8CF4D64A8B72%2Fappyinxiangcom%2F30104613%2FENResource%2Fp468)%0A!%5B75d99ddcd7036c7251ba4df862a6b1f2.png%5D(evernotecid%3A%2F%2F50E31125-9E8E-4F15-8430-8CF4D64A8B72%2Fappyinxiangcom%2F30104613%2FENResource%2Fp467)%0A%E8%B4%9F%E9%87%87%E6%A0%B7%0A%0A</center></body></html>