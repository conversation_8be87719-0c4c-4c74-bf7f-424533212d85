<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/><meta name="exporter-version" content="Evernote Mac 9.7.14 (474683)"/><meta name="author" content="安能行叹复坐愁"/><meta name="created" content="2022-05-11 03:44:41 +0000"/><meta name="source" content="desktop.mac"/><meta name="updated" content="2023-05-25 12:23:39 +0000"/><meta name="application-data:yinxiang.superNote" content="{&quot;aiMode&quot;:false}"/><title>智力题</title></head><body style="font-size: 16px;"><div><span style="font-size: 13px;"><span style="font-size: 13px; font-weight: bold;">1.圆上取3个点，是锐角三角形的概率</span></span></div><div><font style="font-size: 13px;"><span style="font-size: 13px; font-weight: bold;">2.</span><span style="font-size: 13px; font-weight: bold;">水壶问题</span></font></div><div><span style="font-size: 13px;"><span style="font-size: 13px; font-weight: bold;">3.红黑墨水问题</span></span></div><div><span style="font-size: 13px;"><span style="font-size: 13px; font-weight: bold;">4.10个球，3黑7白，取到白球概率？（极大似然）</span></span></div><div><span style="font-size: 13px;"><span style="font-size: 13px; font-weight: bold;">5.3个1-6的骰子和1个1-20的骰子</span></span></div><div><font style="font-size: 13px;"><span style="font-size: 13px; font-weight: bold;">6.</span><span style="font-size: 13px; font-weight: bold;">一个可以装6L水，一个可以装5L水，如何在桶里装入3L的水？</span></font></div><div><span style="font-size: 13px;"><span style="font-size: 13px; font-weight: bold;">7.赛马</span></span></div><div><span style="font-size: 13px;"><span style="font-size: 13px; font-weight: bold;">8.1000瓶，一瓶有毒，老鼠喝了后24小时后死亡，用几只老鼠可以确定哪瓶是毒药。</span></span></div><div><font style="font-size: 13px;"><span style="font-size: 13px; font-weight: bold;">9.烧绳子</span></font></div><div><font style="font-size: 13px;"><span style="font-size: 13px; font-weight: bold;">10.12个小球，只有一个重量和其他球不一样，给一个天平，找出异常球。</span></font></div><div><span style="font-size: 13px; color: rgb(50, 50, 50); font-weight: bold;">11.</span><span style="font-size: 13px; color: rgb(50, 50, 50); font-weight: bold;">有10瓶药，每瓶有10粒药，其中有一瓶是变质的。好药每颗重1克，变质的药每颗比好药重0.1克。问怎样用天平称一次找出变质的那瓶药？</span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50); font-weight: bold;">13.分割金条付工资</span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50); font-weight: bold;">14.</span><span style="font-size: 13px; font-weight: bold;">有三个酒杯，其中两个大酒杯每个可以装8两酒，一个可以装3两酒。现在两个大酒杯都装满了酒，只用这三个杯子怎么把酒平均的分给4个人喝？</span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50); font-weight: bold;">15.南极北极</span></div><div><b><span style="font-size: 13px;">16.</span><span style="font-size: 13px;">50名运动员按顺序排成一排，教练下令：“单数运动员出列！”剩下的运动 员重新排列编号，教练又下令：“单数运动员出列！”如此下去，最后只剩下一个人，他是最开始的几号运动员？</span></b></div><div><span style="font-weight: bold; font-size: 13px;">17.[m,n]的</span><span style="font-weight: bold; font-size: 13px;">连续正整数之和为1000 的共有几组? </span></div><div><span style="font-weight: bold; font-size: 13px;">18.</span><span style="font-size: 13px;"><b>49个人中至少几个人生日是同一月？</b></span></div><div><span style="font-size: 13px;"><b>19.</b></span><span style="font-weight: bold; font-size: 13px;">一枚正反概率不一样的硬币，如何当一枚正常的硬币来用（正反概率相同）？ </span></div><div><span style="font-weight: bold; font-size: 13px;">21.盲人分牌</span></div><div><span style="font-size: 13px;"><b>22.脑门贴数字</b></span></div><div><span style="font-weight: bold; font-size: 13px;">23.飞机加油，绕地球一圈</span></div><div><span style="font-size: 13px;"><b>24.ABCD过桥时间最短</b></span></div><div><span style="font-size: 13px;"><b>25.拿石子，拿最后一颗的败</b></span></div><div><span style="font-size: 13px; font-weight: bold;">26.海盗分金币</span></div><div><span style="font-size: 13px;"><b>27.扔鸡蛋</b></span></div><div><span style="font-size: 13px;"><b><br/></b></span></div><div><span style="font-size: 13px; font-weight: bold;">1.在一个圆上任取三个点，是锐角三角形的概率</span></div><div><span style="font-size: 13px;">首先，锐角三角形、直角三角形、钝角三角形中，只有锐角三角形的外接圆圆心在其内部。</span></div><div><span style="font-size: 13px;">问题等价于是锐角三角形的概率是多少。</span></div><div><span style="font-size: 13px;">任取不过圆心的弦AB，作为三角形的一条边。</span></div><div><span style="font-size: 13px;">作过A的直径交圆于A'。</span></div><div><span style="font-size: 13px;">作过B的直径交圆于B'。</span></div><div><span style="font-size: 13px;">则劣弧AB=劣弧A'B'，设其对应圆心角为θ。</span></div><div><span style="font-size: 13px;">则</span><span style="font-size: 13px; color: unset; font-family: unset;">C落在A'和B'点，△ABC是直角三角形，弧长度为0；</span></div><div><span style="font-size: 13px;">C落在劣弧A'B'间，△ABC是锐角三角形，弧长度为θ；</span></div><div><span style="font-size: 13px;">其他为钝角三角形，弧长度为是2π-θ/。</span></div><div><span style="font-size: 13px;">f(θ)=θ，该函数对θ∈(0，π)积分，得锐角总长度π²/2。</span></div><div><span style="font-size: 13px;">g(θ)=2π-θ，该函数对θ∈(0，π)积分，得钝角总长度3π²/2。</span></div><div><span style="font-size: 13px;">则锐角:钝角=1:3，所以锐角为1/4。</span></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px; font-weight: bold;">2.水壶问题（leetcode可搜）</span></div><div><span style="font-size: 13px;">有两个容量分别为x升 和y升 的水壶以及无限多的水。请判断能否通过使用这两个水壶，从而可以得到恰好z升的水？</span></div><div><span style="font-size: 13px;">思路：</span></div><div><span style="font-size: 13px;">为了简单思考，先处理完特殊情况，两壶相加刚好满足，或其中一壶刚好有一个满足的情况。 然后发现一个规律，就是只能通过大壶倒小壶产生新的数字： 比如大壶的容量是a，小壶的容量是b，只能通过将大壶的水倒入小壶，然后再把小壶的水清空来产生新的容量数字， 这些产生的新的数字将是：a-b, a-2b, a-3b..., a-n*b 直到大壶里剩余的水小于b升， 此时，要想产生新的容量数字，也只有一个操作，即： 将大壶剩余的数倒入小壶，然后大壶装满，产生了一个新的数字: 2a - n*b 此时，要想产生新的容量数字，仍只有一个操作，即： 从大壶里倒一点水将小壶补满，然后再把小壶清空，产生了一个新的数字: 2a - n*b - b</span></div><div><span style="font-size: 13px;">接下来就是循环的过程了：令n为任意整数，则: 大壶装第3次水后的容量数字是：3a - n*b 大壶装第4次水后的容量数字是：4a - n*b 大壶装第m次水后的容量数字是：m*a - n*b 所有规律是：所以可能的容量都是 若干个大壶的水量 减去 若干个小壶的水量 产生的。 其实这是在利用大壶做加法，小壶做减法的过程，</span></div><div><span style="font-size: 13px;">所以我们只要写个循环，用一个变量c表示当前总水量， 当大壶水量够的时候，就不断减去小壶容量b，也就是不断的 c -= b，一边判断 c 是否等于 targetCapacity， 当大壶水量不够的时候，就把剩余的水倒入小壶，然后加满大壶，也就是：c += a 循环终止条件是什么呢，思考发现，最终肯定能够刚好减成零， 最坏的情况是往大壶里装了b次水，那历史总装水量就是a * b, 刚好是小壶容量的整数倍。 （实际情况是历史总装水是 a 和 b 的最小公倍数就行了） 减成零了之后，下次相当于回到初始状态了。 如果在这个过程中没有搜索到，以后也不会搜索到了。</span></div><div><span style="font-size: 13px;">（写代码的时候，可以把c初始化成a + b，这样特殊情况也顾及到了）</span></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font style="font-size: 13px;">class Solution:</font></div><div><font style="font-size: 13px;">    def canMeasureWater(self, jug1Capacity: int, jug2Capacity: int, targetCapacity: int) -&gt; bool:</font></div><div><font style="font-size: 13px;">        if jug1Capacity + jug2Capacity &lt; targetCapacity: return False</font></div><div><font style="font-size: 13px;">        a, b = (jug1Capacity, jug2Capacity) if jug1Capacity &gt; jug2Capacity else (jug2Capacity, jug1Capacity)</font></div><div><font style="font-size: 13px;">        c = a + b</font></div><div><font style="font-size: 13px;">        while c:</font></div><div><font style="font-size: 13px;">            if c == targetCapacity:</font></div><div><font style="font-size: 13px;">                return True</font></div><div><div><font style="font-size: 13px;"><br/></font></div></div><div><font style="font-size: 13px;">            if c &gt;= b:</font></div><div><font style="font-size: 13px;">                c -= b</font></div><div><font style="font-size: 13px;">            else:</font></div><div><font style="font-size: 13px;">                c += a</font></div><div><font style="font-size: 13px;">        return False</font></div></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px; font-weight: bold;">3.红黑墨水问题</span></div><div><span style="font-size: 13px;">两瓶墨水，一红一黑，用小勺从红墨水瓶里舀一勺放入黑瓶，搅拌均匀，然后从黑瓶里舀一勺放入红瓶，这时红瓶里的红墨水多还是黑瓶里的黑墨水多？如果不搅匀呢？</span></div><div><span style="font-size: 13px;">都是一样多，搅拌均匀的话可以很容易的写出公式。不搅匀的话，直接宏观来想，是守恒的，红墨水少了多少，就需要用多少黑墨水来填</span></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px; font-weight: bold;">4.有一个罐子，装着黑白两种球。有放回抽样10次，7次白球，3次黑球，问，白球所占比例是多少？</span></div><div><span style="font-size: 13px;">答：30%。极大似然估计。</span></div><div><span style="font-size: 13px;">假设白球所占比例为a，那么黑球所占比例就是1-a。每次抽出来的球的颜色服从同一独立分布。</span></div><div><span style="font-size: 13px;">P(10次抽样)=a^7(1-a)^3</span></div><div><span style="font-size: 13px;">极大似然估计，要让P最大。所以对a^7(1-a)^3进行求导，使导数=0。解得，a=0.7。</span></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px; font-weight: bold;">5.掷骰子</span></div><div><span style="font-size: 13px;">A组：三个[1,6]的骰子，掷一次，求和。</span></div><div><span style="font-size: 13px;">B组：一个[1,20]的骰子，掷一次。</span></div><div><span style="font-size: 13px;">问，A&gt;B的概率和B&gt;A的概率谁大？</span></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px;">解答：</span></div><div><span style="font-size: 13px;">一样大。</span></div><div><span style="font-size: 13px;">A组的值域：[3,18]：3和18的概率一样，4和17的概率一样，5和16的概率一样……，10和11的概率一样，越往中间概率越大，是左右对称的。</span></div><div><span style="font-size: 13px;">B组的值域：[1,20]。</span></div><div><span style="font-size: 13px;">A和B的期望都是10.5，P(A&gt;10.5,B&lt;10.5,A&gt;B)=0.25，P(A&lt;10.5,B&gt;10.5,A&lt;B)=0.25。</span></div><div><span style="font-size: 13px;">A和B同时&gt;0.5，A和B同时小于0.5是对称的。</span></div><div><span style="font-size: 13px; color: unset;">对b来说，如果b&gt;a，那必有一个对称的a’，满足b&lt;a</span><span style="font-size: 13px;">’</span></div><div><span style="font-size: 13px;">A数组概率对称，B数组是均匀的，B数组的期望在A数组的最中间，就能满足一样大。</span></div><div><font style="font-size: 13px;"><br/></font></div><div><font style="font-size: 13px;"><span style="font-size: 13px; font-weight: bold;">6.</span><span style="font-size: 13px; font-weight: bold;">只有两个无刻度的水桶，一个可以装6L水，一个可以装5L水，如何在桶里装入3L的水？</span></font></div><div><span style="font-size: 13px;">先将5L的桶装满，将5L的桶的水倒入6L的桶中。这时5L的桶是空的，6L的桶中有5L的水 再将5L的桶装满，倒入6L的桶中。这时5L的桶有4L的水，6L的桶是满的 将6L的桶中的水倒掉，5L的桶的水倒入6L的桶中。这时5L的桶是空的，6L的桶中有4L的水 将5L的桶装满，倒入6L的桶中。这时5L的桶还有3L的水，6L的桶是满的。</span></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px; font-weight: bold;">7.赛马</span></div><div><span style="font-size: 13px;">25匹马，5个赛道，每次只能同时有5匹马跑，最少比赛几次选出最快的马？</span></div><div><span style="font-size: 13px;">答：7次。</span></div><div><span style="font-size: 13px;">前五次：将25匹马放到5个赛道比赛，找出每个赛道的第一名。假设A1、B1、C1、D1、E1分别为每组的第一名。</span></div><div><span style="font-size: 13px;">第六次：将A1、B1、C1、D1、E1放到一个赛道上找出第一名，假设为A1，其他四名分别为B1、C1、D1、E1。这时第一名已经找到了，还需找到二、三名。因为C1的速度比D1和E1的速度快，所以赛道D和赛道E的所有马都被淘汰了。有机会成为二、三名的马为A2、A3、B1、B2、C1这五匹马，即前五名在这个区域，并且第一名为A1。</span></div><div><font style="font-size: 13px;"><br/></font></div><div><span style="font-size: 13px; font-weight: bold;">8.1000瓶，一瓶有毒，老鼠喝了后24小时后死亡，用几只老鼠可以确定哪瓶是毒药。</span></div><div><span style="font-size: 13px;">答：10只。因为2^10&gt;1000，按照二进制喂，最后死亡的老鼠的位置为1，其余位置为0，组成的二进制数就是毒药。</span></div><div><span style="font-size: 13px;">信息熵的角度解释：</span></div><div><span style="font-size: 13px;">信息熵公式：</span><img src="%E6%99%BA%E5%8A%9B%E9%A2%98.resources/74D069DF-75D2-40BB-9605-9B4B4542B3CB.png" height="94" width="442"/><br/></div><div><span style="font-size: 13px;">1000瓶，一瓶有毒，信息熵为：</span><img src="%E6%99%BA%E5%8A%9B%E9%A2%98.resources/2A03900C-D307-4216-AD46-0A47C7F229A4.png" height="112" width="462"/><br/></div><div><span style="font-size: 13px;">N只老鼠喝完后，会有2^n种状态，信息熵为：</span><img src="%E6%99%BA%E5%8A%9B%E9%A2%98.resources/8B2A0144-3349-4F6F-A10F-A3F422670254.png" height="138" width="678"/><br/></div><div><span style="font-size: 13px;">如果至少需要n只老鼠能够找到这桶毒水，那么随机变量Y的信息熵必须要大于随机变量X的信息熵。所以n&gt;9.966，n=10。</span></div><div><font style="font-size: 13px;"><img src="%E6%99%BA%E5%8A%9B%E9%A2%98.resources/470C0BA6-4F1A-4E3D-B3DE-EA686F085397.png" height="124" width="780"/><br/></font></div><div><font style="font-size: 13px;"><br/></font></div><div><font style="font-size: 13px;"><span style="font-size: 13px; font-weight: bold;">9.烧绳子</span></font></div><div><span style="font-size: 13px; color: rgb(50, 50, 50);">烧一根不均匀的绳子,从头烧到尾是要1个小时.现在有若干条材质相同的绳子 问如何用烧绳的方法来计时一个小时15分钟。</span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50);">把第一根绳子两头同时点燃,同时把第二根绳子点燃一头,当第一根绳子烧完时,时间为半个小时,这时把第二根绳子的另一头也点燃,开始计时,当第二根绳子烧完时,停止计时,那么这段时间就是15分钟。也就是说，只需要3根绳子就可以计时一个小时15分钟。</span></div><div><span style="font-size: 13px;"><br/></span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50); font-weight: bold;">10.</span><span style="font-size: 13px; color: rgb(50, 50, 50); font-weight: bold;">一共12个一样的小球， 其中只有一个重量与其它不一样(未知轻重)，给你一个天平，找出那个不同重量的球？</span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50);">1.将12个小球分为三组（因为分成两组不能找到重量不一样的球在哪组），为A组、B组、C组 </span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50);">2.将三组球分别两两称重，找到重量和另外两组不同的那一组（只要有两组可以使天平平衡，重量不一致的球必然在第三组）。假设坏的球在C组 </span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50);">3.将C组的球分成两组C1和C2，每组两个球，这时从A组和B组里找到两个正常的球，分别和C1和C2去称，天平不能平衡说明重量不一致的球就在哪组。假设在C1 </span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50);">4.将C1组的球分别和正常的球去称，天平不平衡时就能找到重量与其他不一致的球。</span></div><div><span style="font-size: 13px;"><br/></span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50); font-weight: bold;">11.</span><span style="font-size: 13px; color: rgb(50, 50, 50); font-weight: bold;">有10瓶药，每瓶有10粒药，其中有一瓶是变质的。好药每颗重1克，变质的药每颗比好药重0.1克。问怎样用天平称一次找出变质的那瓶药？</span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50);">将这10瓶药标好号1-10。 然后按照瓶子的标号取药，1号药瓶取1粒药，2号药瓶取2粒药，3号药瓶取3例药，以此类推，取完10瓶药一起放到天平上去称。如果没有变质的药，重量应该是55克，这时多出几克，几号药瓶就是变质的。例如55.3克，那么变质的药就是3号药瓶的</span></div><div><span style="font-size: 13px;"><br/></span></div><div><span style="font-size: 13px;"><span style="font-size: 13px; color: rgb(50, 50, 50); font-weight: bold;">12.你有两个罐子，50个红色弹球，50个蓝色弹球，如何将这100个球放入到两个罐子，随机选出一个罐子取出的球为红球的概率最大？ </span></span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50);">将一个红球放到一个罐子中，另一个罐子放49个红球和50个蓝球，这样随便选出一个罐子取出红球的概率是1/2 * 1 + 1/2 * 49 /（49+50），接近0.75。</span></div><div><span style="font-size: 13px;"><br/></span></div><div><span style="font-size: 13px;"><span style="color: rgb(50, 50, 50); font-size: 13px; font-weight: bold;">13.分割金条付工资</span></span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50);">你现在有一根金条，有一个工人为你工作了七天，他们的工资是金条的七分之一，并且需要当天结清，你只能对金条切割两次，请问需要怎么做。</span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50);">这个问题和平时用的纸币金额是一个道理，将一根金条切割两次可以得到三根金条，这三根金条必须可以组合出1-7之间的任意金额。</span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50);">将金条分两次切成长度为1、2、4的金条即可。</span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50);">第一天，将长度为1的金条支付给工人。</span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50);">第二天，将长度为2的金条支付给工人，工人将长度为1的金条还给你。</span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50);">第三天，将长度为1的金条支付给工人</span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50);">第四天，将长度为4的金条支付给工人，工人将长度为1，2的金条还给你</span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50);">第五条，将长度为1的金条支付给工人</span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50);">第六条，将长度为2的金条支付给工人，工人将长度为1的金条还给你</span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50);">第七天，将长度为1的金条支付给工人</span></div><div><span style="font-size: 13px;"><br/></span></div><div><span style="font-size: 13px; font-weight: bold;">14.</span><span style="font-size: 13px; font-weight: bold;">有三个酒杯，其中两个大酒杯每个可以装8两酒，一个可以装3两酒。现在两个大酒杯都装满了酒，只用这三个杯子怎么把酒平均的分给4个人喝？</span></div><div><span style="font-size: 13px;">用三个数字表示三个杯子，最开始为880，即两个8两的杯子是满的，一个3两的杯子是空的。 880—&gt;853，这时A喝掉第三个杯子的三两酒变成850 850—&gt;823，这时B喝掉第二个杯子的二两变酒成803 803—&gt;830—&gt;533—&gt;560—&gt;263—&gt;281，A喝掉第三个杯子的一两酒变成280（A喝完了4两） 280—&gt;253—&gt;550—&gt;523—&gt;820—&gt;703—&gt;730—&gt;433—&gt;460—&gt;163—&gt;181，这时C和D各喝一两酒变成080 080—&gt;053—&gt;350—&gt;323，这时B喝点第二个杯子的二两酒，C和D各喝三两酒，到此所有人都喝了四两酒</span></div><div><span style="font-size: 13px;"><br/></span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50);"><b>15.南极北极</b></span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50);">向南走一英里，向东走一英里，向北走一英里能够回到原点，这个点在哪？</span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50);">答案：北极点和离南极点1+1/2kπ (k为正整數)的所有点。</span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50); font-weight: bold;">考虑南极点：</span><span style="font-size: 13px; color: rgb(50, 50, 50);">在南极点，所有方位都是向北，那么向南走一英里则是原地旋转，向东走一英里还是原地旋转，利用在南极点向北走一英里则是向任意方向走一英里的特点，则向北走一英里则是向任意方向走一英里，肯定不能回到原点。</span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50); font-weight: bold;">考虑南极附近的点：</span></div><div><img src="%E6%99%BA%E5%8A%9B%E9%A2%98.resources/611C3A21-C3B9-4B4B-BEC3-47ACFA5BBA76.png" height="230" width="622"/></div><div><span style="font-size: 13px; color: rgb(50, 50, 50);">A-&gt;B，B转k圈，B-&gt;A。 满足题意。所以B距离南极点是R，2πkR=1公里，那R=1/2kπ。</span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50); font-weight: bold;">考虑北极点：</span><span style="font-size: 13px; color: rgb(50, 50, 50);">距离</span><span style="font-size: 13px; color: rgb(50, 50, 50);">北极点1/2kπ的点满足题意。</span></div><div><span style="font-size: 13px; color: rgb(50, 50, 50); font-weight: bold;">北极附近的点：</span><span style="font-size: 13px; color: rgb(50, 50, 50);">不满足题意。</span></div><div><span style="font-size: 13px;"><b><br/></b></span></div><div><b><span style="font-size: 13px;">16.</span><span style="font-size: 13px;">50名运动员按顺序排成一排，教练下令：“单数运动员出列！”剩下的运动 员重新排列编号，教练又下令：“单数运动员出列！”如此下去，最后只剩下一个人，他是最开始的几号运动员？</span></b></div><div><span style="font-size: 13px;">正向思维： </span></div><div><span style="font-size: 13px;">1.运动员编号为1-50，单号出列后为2，4，6，……，50 </span></div><div><span style="font-size: 13px;">2.运动员重新编号为1-25，单号出列后为2，4，6，……,24 </span></div><div><span style="font-size: 13px;">3.运动员重新编号为1-12，单号出列后为2，4，6，……，12 </span></div><div><span style="font-size: 13px;">4.运动员重新编号为1-6，单号出列后为2，4，6 </span></div><div><span style="font-size: 13px;">5.运动员重新编号为1-3，单号出列后为2 </span></div><div><span style="font-size: 13px;">反向思维： </span></div><div><span style="font-size: 13px;">1.第五轮运动员的编号为2 </span></div><div><span style="font-size: 13px;">2.第五轮编号为2的运动员在第四轮编号为4 </span></div><div><span style="font-size: 13px;">3.第四轮编号为4的运动员在第三轮中编号为8 </span></div><div><span style="font-size: 13px;">4.第三轮编号为8的运动员在第二轮中编号为16 </span></div><div><span style="font-size: 13px;">5.第二轮编号为16的运动员在第一轮中编号为32 所以，剩下的最后一名运动员在开始的编号为32</span></div><div><span style="font-size: 13px;"><br/></span></div><div><b><span style="font-size: 13px;">17.[m,n]的</span><span style="font-size: 13px;">连续正整数之和为1000 的共有几组? </span></b></div><div><span style="font-size: 13px;">假设开始的数为m，从m加到n等于1000，根据等差求和公式得(m+n)(n-m+1)/2=1000，(m+n)(n-m+1)=2000，即2000为一个奇数和一个偶数的乘积，得20000=2*2*2*2*5*5*5 </span></div><div><span style="font-size: 13px;">下面分情况讨论 </span></div><div><span style="font-size: 13px;">当奇数为1时，m+n=2000，n-m+1=1，得m=1000,n=1000 </span></div><div><span style="font-size: 13px;">当奇数为5时，m+n=400,n-m+1=5，得m=197,n=203 </span></div><div><span style="font-size: 13px;">当奇数为25时，m+n=80,n-m+1=25，得m=27,n=53 </span></div><div><span style="font-size: 13px;">当奇数为125时，m+n=125,n-m+1=16，得m=54,n=71 综上，有四组。</span></div><div><span style="font-size: 13px;"><br/></span></div><div><span style="font-size: 13px;"><b>18.</b></span><span style="font-size: 13px;"><b>49个人中至少几个人生日是同一月？</b> </span></div><div><span style="font-size: 13px;">一年有12个月，那么49个人最后至少有49/12+1=5个人出生月份相同</span></div><div><span style="font-size: 13px;"><br/></span></div><div><b><span style="font-size: 13px;">19.</span><span style="font-size: 13px;">一枚正反概率不一样的硬币，如何当一枚正常的硬币来用（正反概率相同）？ </span></b></div><div><span style="font-size: 13px;">答案：连续抛两次即可，第一次为正面、第二次为反面和第一次为反面、第二次为正面得概率相同。</span></div><div><span style="font-size: 13px;"><br/></span></div><div><span style="font-size: 13px;">20.</span><span style="font-size: 13px;">1楼到n楼的每层电梯门口都放着一颗钻石，钻石大小不一。你乘坐电梯从1楼到n楼，每层楼电梯门都会打开一次，只能拿一次钻石，问怎样才能拿到「最大」的一颗？</span></div><div><span style="font-size: 13px;"><b><br/></b></span></div><div><b><span style="font-size: 13px;">21.盲人分牌</span></b></div><div><b><span style="font-size: 13px;">一副牌除去大小王还有52张，其中10张牌是正面朝上的，要求一个盲人将牌分成两堆，并且每堆牌正面朝上的数量相同，可以任意翻动每张牌，应该怎么分？</span></b></div><div><span style="font-size: 13px;">分成两堆，一堆10张，一堆42张，并且将第一堆的10张牌全部反转，这时两堆牌中正面朝上的数量是相同的。</span></div><div><span style="font-size: 13px;"><br/></span></div><div><b><span style="font-size: 13px;">22.</span></b><span style="font-size: 13px; font-weight: bold;">脑门贴数字</span></div><div><b><span style="font-size: 13px;">两人玩游戏，在脑门上贴数字（正整数&gt;=1），只看见对方的，看不见自己的，而且两人的数字相差1，以下是两人的对话：A：我不知道，B：我也不知道，A：我知道了，B：我也知道了，问A头上的字是多少，B头上的字是多少？</span></b></div><div><span style="font-size: 13px;">第一轮：A说不知道，B也说不知道，说明两个人脑门上都没有数字1。每个人脑门上的数字都有两种可能。 第二轮：A说知道了，B也说知道了，说明B说不知道给A排除了一个可能，只有数字1才有可能被排除，A才有可能猜出自己脑门上的数字，所以A脑门的数字是3。B听到A说知道了，说明自己说不知道给A排除了一个答案，排除的答案只能是1，A才能猜出，既然排除的是1，A只能是3，那么B脑门的数字则只能是2。</span></div><div><span style="font-size: 13px;"><br/></span></div><div><span style="font-weight: bold; font-size: 13px;">23.飞机加油，绕地球一圈</span></div><div><b><span style="font-size: 13px;">在一个飞机场有N架飞机，每架飞机只有一个油箱，每箱油可以使飞机绕地球飞半圈。如果使一架飞机绕地球一圈，至少需要出动多少飞机？（要求所有飞机均能安全返回到机场，只能通过飞机给飞机这样加油方式，不能降落到机场加油）</span></b></div><div><span style="font-size: 13px;">前半程：假设整个路程为1，A、B、C三架飞机同时起飞，在1/8处，三架飞机的油量都剩了3/4，其中飞机C给另外飞机A、B加满油，自己还剩1/4，刚好够返航到机场。到2/8处，A、B两架飞机的油量都剩了3/4，其飞机B给飞机A加满油后，自己还剩2/4，刚好能够返航。此时飞机A飞到6/8处时，油量为0。 后半程：D、E、F三架飞机同时反方向起飞，在7/8处，三架飞机的油量还剩了3/4，飞机F给飞机D、E加满后返航，D、E两架飞机在6/8处碰到飞机A，此时飞机A油量为0，D、E两架飞机的油量为3/4，分别给飞机A加1/4的油量。这时A、D、E飞机油量都是2/4，刚好够返航。 总结一下，几个加油的节点，分别在1/8，2/8，7/8，6/8处。</span></div><div><span style="font-size: 13px;"><br/></span></div><div><b><span style="font-size: 13px;">24.</span><span style="font-size: 13px;">晚上有四个人需要过桥，但是只有一个手电筒，并且桥一次最多两个人，每个人通过桥所需的时间也不同，A、B、C、D过桥所需的时间分别为1、2、5、10分钟。请问如何过桥所需时间最短？</span></b></div><div><span style="font-size: 13px;"><br/></span></div><div><span style="font-size: 13px; font-weight: bold;">25.拿石子，拿最后一颗的败</span></div><div><span style="font-size: 13px;">一共有N颗石子，每次最多取M颗最少取1颗，A，B轮流取（A先），谁最后拿完石子谁就获胜，请问最后谁会获胜？ </span></div><div><span style="font-size: 13px;">思路:</span></div><div><span style="font-size: 13px;">假设M&gt;=N，那么A一次就把石子拿完了，A胜</span></div><div><span style="font-size: 13px;">假设M&lt;N，如果N可以被 (M+1) 整除时，A失败，如果N不可以被 (M+1) 整除时，A胜</span></div><div><span style="font-size: 13px;">具体分析:如果N可以被(M+1)整除时，无论A怎么拿，B都会保持拿完后石子的数量为(M+1)的倍数，到最后只能M+1个，A无论怎么拿，B都会在下一次把石子拿完。如果N不可以被(M+1)整除时，A可以保证自己拿完剩下的石子数量一定是(M+1) 的倍数，同理，A胜。</span></div><div><span style="font-size: 13px;"><b><br/></b></span></div><div><span style="font-size: 13px;"><b>26.海盗分金币</b></span></div><div><span style="font-size: 13px;">海盗分金币问题：5个海盗抢到了100枚金币，他们的分配方案，先抽签决定自己的发言顺序。1号提出的方案，由5个人进行投票表决，如果半数人以上同意（不包括半数），就按他的方案分配，否则扔进海里喂鱼。如果1号被喂鱼，由2号发言，提出的方案由4个人进行投票表决，规则同上。如果2号被喂鱼，以此类推。1号海盗提出什么样的方法才能使得自己分到最多的金币？</span></div><div><span style="font-size: 13px;"><br/></span></div><div><span style="font-size: 13px;">从最后面开始，如果前三个人都被喂鱼了，只剩4号和5号，那么无论4号说什么，5号都会反对，4号一定会被喂鱼，5号独吞100枚金币。所以3号无论说什么，4号只能同意。 </span></div><div><span style="font-size: 13px;">3号知道这些，会提出“100，0，0”这种分配方案，4号海盗为了活命只能赞同，加上自己一票即可使得投票通过半数。 </span></div><div><span style="font-size: 13px;">2号知道这些，会提出“98，0，1，1”的分配方案，以此拉拢4号和5号。 </span></div><div><span style="font-size: 13px;">1号知道这些，他还需要两个人支持他，2号是不可能的，3号只需1枚金币，4号或者5号其中一人即可，所以1号的分配方案是“97，0，1，2，0”或者“97，0，1，0，2”</span></div><div><span style="font-size: 13px;"><br/></span></div><div><span style="font-size: 13px;">27.扔鸡蛋</span></div><div><span style="font-size: 13px;">2个鸡蛋，100层楼：</span></div><div><span style="font-size: 13px;">假设在最坏情况下，小明需要扔 x 次才可以测出临界楼层。小明首先在第 i 层扔出了鸡蛋，有两种情况：</span></div><div><span style="font-size: 13px;">鸡蛋碎了，那么小明的第二个鸡蛋只能从第1层一层层往上直到 i-1层 来测试，于是小明共尝试 1+i-1 = i 次，即 i=x</span></div><div><span style="font-size: 13px;">鸡蛋没碎，那么小明还剩下x-1次测试机会，于是第二次尝试楼层只能高出第i层 x-2 层（包括第二次尝试楼层）。</span></div><div><span style="font-size: 13px;">同理继续往下，可知若第二次没碎，第三次的楼层只能高出第二次楼层 x-3 层。于是得到方程：</span></div><div><span style="font-size: 13px;">x + ( x − 1 ) + ( x − 2 ) + . . . + 1 ≥ 100 x+(x-1)+(x-2)+...+1 \geq 100</span></div><div><span style="font-size: 13px;">x+(x−1)+(x−2)+...+1≥100</span></div><div><span style="font-size: 13px;">解得 x=14 ，即小明第一次在14层扔，若没碎，则在27层扔…。若一直都没碎，小明扔的楼层序列为：14,27,39,50,60,69,77,84,90,95,99,100。在最坏情况下(答案为13层时)，小明共需尝试14次。</span></div><div><span style="font-size: 13px;"><br/></span></div><div><span style="font-size: 13px;">2个鸡蛋，N层楼</span></div><div><span style="font-size: 13px;"><img src="%E6%99%BA%E5%8A%9B%E9%A2%98.resources/7B42BF45-82A5-4455-8C93-31ABC6439EB0.png" height="994" width="1888"/></span></div><div><font size="2">K个鸡蛋，N层楼</font></div><div><span style="font-size: 13px;"/></div><div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div>def superEggDrop(self, K, N):</div><div>    memo = dict() # 记忆化搜索</div><div>    # k个鸡蛋，n层楼，最少需要几次</div><div>    def dp(K, N):</div><div>        if K == 1: return N  # 只有一个鸡蛋，只能从第一层开始试</div><div>        if N == 0: return 0  # 0层楼，不用试</div><div>        if (K, N) in memo:</div><div>            return memo[(K, N)]</div><div><div><br/></div></div><div>        # 在1到N中，选一层扔鸡蛋，取最小次数的那一层。 max的意思：题目要求考虑最坏情况             </div><div>        # for 1 &lt;= i &lt;= N:</div><div>        #     res = min(res, max(dp(K - 1, i - 1), dp(K, N - i)) + 1)</div><div><div><br/></div></div><div>        res = float('INF')</div><div>        # 用二分搜索代替线性搜索</div><div>        lo, hi = 1, N</div><div>        while lo &lt;= hi:</div><div>            mid = (lo + hi) // 2</div><div>            broken = dp(K - 1, mid - 1)  # 碎</div><div>            not_broken = dp(K, N - mid)  # 没碎</div><div><div><br/></div></div><div>            if broken &gt; not_broken:</div><div>                hi = mid - 1</div><div>                res = min(res, broken + 1)</div><div>            else:</div><div>                lo = mid + 1</div><div>                res = min(res, not_broken + 1)</div><div><div><br/></div></div><div>        memo[(K, N)] = res</div><div>        return res</div><div><div><br/></div><div><br/></div></div><div>    return dp(K, N)</div></div></div><div><span style="font-size: 13px;"><br/></span></div></body></html>